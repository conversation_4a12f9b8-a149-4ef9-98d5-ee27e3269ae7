<?xml version="1.0" encoding="UTF-8"?>

<!-- SUMO检测器配置文件 -->
<additionalFile xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://sumo.dlr.de/xsd/additional_file.xsd">

    <!-- 感应线圈检测器 - 用于检测车辆到达 -->
    
    <!-- 东向检测器 -->
    <inductionLoop id="det_east_in_0" lane="east_in_0" pos="50" freq="1" file="results/detectors_east.xml"/>
    <inductionLoop id="det_east_in_1" lane="east_in_1" pos="50" freq="1" file="results/detectors_east.xml"/>
    <inductionLoop id="det_east_out_0" lane="east_out_0" pos="50" freq="1" file="results/detectors_east.xml"/>
    <inductionLoop id="det_east_out_1" lane="east_out_1" pos="50" freq="1" file="results/detectors_east.xml"/>
    
    <!-- 南向检测器 -->
    <inductionLoop id="det_south_in_0" lane="south_in_0" pos="50" freq="1" file="results/detectors_south.xml"/>
    <inductionLoop id="det_south_in_1" lane="south_in_1" pos="50" freq="1" file="results/detectors_south.xml"/>
    <inductionLoop id="det_south_out_0" lane="south_out_0" pos="50" freq="1" file="results/detectors_south.xml"/>
    <inductionLoop id="det_south_out_1" lane="south_out_1" pos="50" freq="1" file="results/detectors_south.xml"/>
    
    <!-- 西向检测器 -->
    <inductionLoop id="det_west_in_0" lane="west_in_0" pos="50" freq="1" file="results/detectors_west.xml"/>
    <inductionLoop id="det_west_in_1" lane="west_in_1" pos="50" freq="1" file="results/detectors_west.xml"/>
    <inductionLoop id="det_west_out_0" lane="west_out_0" pos="50" freq="1" file="results/detectors_west.xml"/>
    <inductionLoop id="det_west_out_1" lane="west_out_1" pos="50" freq="1" file="results/detectors_west.xml"/>
    
    <!-- 北向检测器 -->
    <inductionLoop id="det_north_in_0" lane="north_in_0" pos="50" freq="1" file="results/detectors_north.xml"/>
    <inductionLoop id="det_north_in_1" lane="north_in_1" pos="50" freq="1" file="results/detectors_north.xml"/>
    <inductionLoop id="det_north_out_0" lane="north_out_0" pos="50" freq="1" file="results/detectors_north.xml"/>
    <inductionLoop id="det_north_out_1" lane="north_out_1" pos="50" freq="1" file="results/detectors_north.xml"/>

    <!-- 车道区域检测器 - 用于检测排队长度 -->
    
    <!-- 东向排队检测器 -->
    <laneAreaDetector id="queue_east_in_0" lane="east_in_0" pos="20" length="200" freq="1" file="results/queue_east.xml"/>
    <laneAreaDetector id="queue_east_in_1" lane="east_in_1" pos="20" length="200" freq="1" file="results/queue_east.xml"/>
    
    <!-- 南向排队检测器 -->
    <laneAreaDetector id="queue_south_in_0" lane="south_in_0" pos="20" length="200" freq="1" file="results/queue_south.xml"/>
    <laneAreaDetector id="queue_south_in_1" lane="south_in_1" pos="20" length="200" freq="1" file="results/queue_south.xml"/>
    
    <!-- 西向排队检测器 -->
    <laneAreaDetector id="queue_west_in_0" lane="west_in_0" pos="20" length="200" freq="1" file="results/queue_west.xml"/>
    <laneAreaDetector id="queue_west_in_1" lane="west_in_1" pos="20" length="200" freq="1" file="results/queue_west.xml"/>
    
    <!-- 北向排队检测器 -->
    <laneAreaDetector id="queue_north_in_0" lane="north_in_0" pos="20" length="200" freq="1" file="results/queue_north.xml"/>
    <laneAreaDetector id="queue_north_in_1" lane="north_in_1" pos="20" length="200" freq="1" file="results/queue_north.xml"/>

    <!-- 多入口多出口检测器 - 用于统计通过路口的车辆 -->
    <multiEntryExitDetector id="intersection_detector" freq="1" file="results/intersection_stats.xml">
        <!-- 入口 -->
        <detEntry lane="east_in_0" pos="5"/>
        <detEntry lane="east_in_1" pos="5"/>
        <detEntry lane="south_in_0" pos="5"/>
        <detEntry lane="south_in_1" pos="5"/>
        <detEntry lane="west_in_0" pos="5"/>
        <detEntry lane="west_in_1" pos="5"/>
        <detEntry lane="north_in_0" pos="5"/>
        <detEntry lane="north_in_1" pos="5"/>
        
        <!-- 出口 -->
        <detExit lane="east_out_0" pos="5"/>
        <detExit lane="east_out_1" pos="5"/>
        <detExit lane="south_out_0" pos="5"/>
        <detExit lane="south_out_1" pos="5"/>
        <detExit lane="west_out_0" pos="5"/>
        <detExit lane="west_out_1" pos="5"/>
        <detExit lane="north_out_0" pos="5"/>
        <detExit lane="north_out_1" pos="5"/>
    </multiEntryExitDetector>

</additionalFile>
