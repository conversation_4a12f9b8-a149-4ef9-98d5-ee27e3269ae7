{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, computed, onMounted } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport html2pdf from 'html2pdf.js';\nimport { Document, Warning } from '@element-plus/icons-vue';\nimport { exportPdfReport } from '@/api/video';\nexport default {\n  name: 'VideoReportPanel',\n  components: {\n    Document,\n    Warning\n  },\n  props: {\n    // 分析结果数据\n    result: {\n      type: Object,\n      default: () => ({})\n    },\n    // 报告的URL\n    reportUrl: {\n      type: String,\n      default: ''\n    },\n    // 视频路径（用于报告名称）\n    videoPath: {\n      type: String,\n      default: ''\n    }\n  },\n  setup(props, {\n    emit\n  }) {\n    const activeNames = ref(['1', '2']); // 默认展开前两个建议\n    const iframeLoading = ref(true);\n    const exporting = ref(false);\n    const generating = ref(false);\n    const actualReportUrl = ref('');\n\n    // 获取车辆数量\n    const vehicleCount = computed(() => props.result?.vehicle_count || 0);\n\n    // 计算拥堵百分比位置\n    const getCongestionPercentage = computed(() => {\n      const count = vehicleCount.value;\n      if (count <= 5) return 12.5; // 不拥挤，位于第一档中间\n      if (count <= 10) return 37.5; // 一般，位于第二档中间\n      if (count <= 20) return 62.5; // 较拥挤，位于第三档中间\n      return 87.5; // 拥挤，位于第四档中间\n    });\n\n    // 获取拥堵等级\n    const getCongestionLevel = computed(() => {\n      const count = vehicleCount.value;\n      if (count <= 5) return '不拥挤';\n      if (count <= 10) return '一般';\n      if (count <= 20) return '较拥挤';\n      return '拥挤';\n    });\n\n    // 获取解决方案标题\n    const getSolutionTitle = computed(() => {\n      const count = vehicleCount.value;\n      if (count <= 5) return '方案一：正常红绿灯交换';\n      if (count <= 10) return '方案二：延长横向绿灯时间';\n      if (count <= 20) return '方案三：延长纵向绿灯时间';\n      return '方案四：发出提醒（需人为干预）';\n    });\n\n    // 获取解决方案描述\n    const getSolutionDescription = computed(() => {\n      const count = vehicleCount.value;\n      if (count <= 5) return '当前车流量正常，可维持默认信号灯配置。各方向交通流量均衡，无需特殊干预。';\n      if (count <= 10) return '当前车流量略大，建议延长东西方向绿灯时间，确保车辆顺畅通行。';\n      if (count <= 20) return '当前车流量较大，建议延长南北方向绿灯时间，减少车辆积压。';\n      return '当前车流量大，可能出现拥堵，建议启动交通应急预案，必要时派遣交警现场指挥。';\n    });\n\n    // 获取详细建议\n    const getDetailedSuggestions = computed(() => {\n      const count = vehicleCount.value;\n      const direction = props.result?.direction || 'horizontal';\n      if (count <= 5) {\n        return ['维持当前信号灯配置，绿灯时间保持在30秒', '保持交通监控系统正常运行', '定期检查道路标志和标线的清晰度'];\n      } else if (count <= 10) {\n        const suggestions = ['将横向方向绿灯时间延长至45秒', '纵向方向绿灯时间可适当缩短至25秒', '加强对驾驶员的提醒，避免不必要的车道变换'];\n        if (direction === 'horizontal') {\n          suggestions.push('重点关注东西方向的车流量变化');\n        }\n        return suggestions;\n      } else if (count <= 20) {\n        const suggestions = ['将纵向方向绿灯时间延长至45秒', '横向方向绿灯时间可适当缩短至25秒', '开启辅助车道，增加通行能力', '考虑实施临时交通管制措施'];\n        if (direction === 'vertical') {\n          suggestions.push('重点关注南北方向的车流量变化');\n        }\n        return suggestions;\n      } else {\n        return ['建议派遣交警到现场指挥交通', '启动交通应急预案，必要时实施单向通行', '向驾驶员发布交通提醒，建议选择替代路线', '临时关闭部分入口，减少车辆进入', '协调相邻路口信号灯，形成绿波带'];\n      }\n    });\n\n    // 计算东西向绿灯时间\n    const getEastWestGreenTime = computed(() => {\n      const count = vehicleCount.value;\n      const direction = props.result?.direction || 'horizontal';\n      if (count <= 5) return 30;\n      if (count <= 10) return direction === 'horizontal' ? 45 : 25;\n      if (count <= 20) return direction === 'horizontal' ? 25 : 20;\n      return 20; // 拥挤状态下，适当减少东西向绿灯时间\n    });\n\n    // 计算南北向绿灯时间\n    const getNorthSouthGreenTime = computed(() => {\n      const count = vehicleCount.value;\n      const direction = props.result?.direction || 'horizontal';\n      if (count <= 5) return 30;\n      if (count <= 10) return direction === 'vertical' ? 40 : 25;\n      if (count <= 20) return direction === 'vertical' ? 45 : 30;\n      return 25; // 拥挤状态下，适当减少南北向绿灯时间\n    });\n\n    // 计算东西向红灯时间\n    const getEastWestRedTime = computed(() => {\n      return getNorthSouthGreenTime.value + 3; // 绿灯时间 + 黄灯时间\n    });\n\n    // 计算南北向红灯时间\n    const getNorthSouthRedTime = computed(() => {\n      return getEastWestGreenTime.value + 3; // 绿灯时间 + 黄灯时间\n    });\n\n    // 尝试修复报告URL\n    const getFixedReportUrl = url => {\n      if (!url) return '';\n      console.log('修复报告URL:', url);\n\n      // 如果已经是完整URL，直接返回\n      if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('/api/')) {\n        return url;\n      }\n\n      // 如果是TaskID格式（可能是24位十六进制字符串），构建规范的API URL\n      if (/^[0-9a-f]{24}$/i.test(url)) {\n        return `/api/video-analysis/${url}/report`;\n      }\n\n      // 如果是相对路径，添加API前缀\n      if (url.startsWith('/')) {\n        return `/api${url}`;\n      }\n\n      // 其他情况，使用标准报告URL格式\n      // 如果有taskId属性，优先使用\n      if (props.result && props.result.taskId) {\n        return `/api/video-analysis/${props.result.taskId}/report`;\n      }\n\n      // 最后尝试构建一个合理的URL\n      return `/api/static/reports/${url}`;\n    };\n\n    // iframe加载完成\n    const iframeLoaded = () => {\n      console.log('iframe加载完成，URL:', props.reportUrl);\n      try {\n        const iframe = document.querySelector('.report-container iframe');\n        if (iframe) {\n          // 检查iframe的内容是否加载成功\n          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;\n          if (iframeDoc) {\n            console.log('iframe文档加载成功，标题:', iframeDoc.title);\n            if (iframeDoc.body.innerHTML === '') {\n              console.warn('iframe内容为空，可能加载失败');\n              // 内容为空时也触发错误处理\n              handleIframeError(new Error('iframe内容为空'));\n              return;\n            }\n          } else {\n            console.warn('无法访问iframe内容，可能是跨域问题');\n          }\n        }\n      } catch (err) {\n        console.error('iframe检查失败:', err);\n        handleIframeError(err);\n      }\n      iframeLoading.value = false;\n    };\n\n    // 处理iframe加载错误\n    const handleIframeError = error => {\n      console.error('iframe加载失败:', error);\n      iframeLoading.value = false;\n      ElMessage.warning({\n        message: '报告加载失败，是否在新窗口打开？',\n        duration: 5000,\n        showClose: true,\n        type: 'warning',\n        onClose: () => {}\n      });\n\n      // 提供在新窗口打开的选项\n      ElMessageBox.confirm('报告在框架中加载失败，是否在新窗口打开查看？', '报告加载失败', {\n        confirmButtonText: '在新窗口打开',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const url = getFixedReportUrl(props.reportUrl);\n        window.open(url, '_blank');\n      }).catch(() => {\n        // 用户取消，不做任何操作\n      });\n    };\n\n    // 从视频路径中提取文件名作为报告名称\n    const getReportName = () => {\n      if (!props.videoPath) return '交通分析报告';\n      const pathParts = props.videoPath.split('/');\n      const fileName = pathParts[pathParts.length - 1];\n      const nameWithoutExt = fileName.split('.')[0];\n      return `${nameWithoutExt}_交通分析报告`;\n    };\n\n    // 导出PDF报告\n    const exportPDF = async () => {\n      if (exporting.value) {\n        return;\n      }\n      exporting.value = true;\n      try {\n        // 检查是否有iframe\n        const iframe = document.querySelector('.report-container iframe');\n        if (!iframe && props.result?.taskId) {\n          // 尝试通过API导出\n          console.log('尝试通过API导出PDF报告');\n          try {\n            const response = await exportPdfReport(props.result.taskId);\n\n            // 处理Blob响应\n            const blob = new Blob([response.data], {\n              type: 'application/pdf'\n            });\n            const url = URL.createObjectURL(blob);\n\n            // 创建下载链接\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `${getReportName()}.pdf`;\n            document.body.appendChild(link);\n            link.click();\n\n            // 清理资源\n            setTimeout(() => {\n              URL.revokeObjectURL(url);\n              document.body.removeChild(link);\n            }, 100);\n            ElMessage.success('PDF报告已导出');\n          } catch (error) {\n            console.error('API导出PDF失败:', error);\n\n            // 处理HTML响应或认证错误\n            if (error.htmlResponse) {\n              ElMessage.error('导出失败：服务器返回了HTML而不是PDF (可能是认证问题)');\n\n              // 询问用户是否重新登录\n              ElMessageBox.confirm('您的登录会话可能已过期。是否重新登录？', '认证错误', {\n                confirmButtonText: '重新登录',\n                cancelButtonText: '取消',\n                type: 'warning'\n              }).then(() => {\n                // 清除认证信息并跳转到登录页面\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('user');\n                window.location.href = '/login';\n              }).catch(() => {});\n              return;\n            }\n            throw new Error(error.message || '通过API导出PDF失败');\n          }\n          return;\n        }\n        if (!iframe) {\n          throw new Error('找不到报告iframe');\n        }\n\n        // 尝试访问iframe内容\n        let iframeContent;\n        try {\n          iframeContent = iframe.contentDocument || iframe.contentWindow && iframe.contentWindow.document;\n\n          // 检查iframe是否加载了HTML内容\n          if (iframeContent && iframeContent.body.innerHTML === '') {\n            throw new Error('iframe内容为空，可能是因为报告未生成或加载失败');\n          }\n\n          // 检查iframe内容是否为错误页面\n          if (iframeContent && iframeContent.title && (iframeContent.title.includes('Error') || iframeContent.title.includes('错误') || iframeContent.title.includes('Not Found'))) {\n            throw new Error(`报告加载失败: ${iframeContent.title}`);\n          }\n        } catch (error) {\n          console.error('无法访问iframe内容（可能是跨域问题）:', error);\n          throw new Error('无法访问报告内容，可能是由于跨域限制或报告未生成');\n        }\n        if (!iframeContent) {\n          throw new Error('无法访问iframe内容');\n        }\n\n        // 创建一个新的容器，复制iframe内容\n        const element = iframeContent.body.cloneNode(true);\n\n        // 添加样式以确保正确打印\n        const styles = document.createElement('style');\n        styles.innerHTML = `\n          body {\n            font-family: Arial, sans-serif;\n            color: #333;\n            background-color: white;\n          }\n          h1, h2, h3 {\n            color: #1a1a1a;\n          }\n          .chart-container {\n            page-break-inside: avoid;\n            margin-bottom: 20px;\n          }\n        `;\n        element.appendChild(styles);\n\n        // 使用html2pdf库进行转换\n        const opt = {\n          margin: [10, 10, 10, 10],\n          filename: `${getReportName()}.pdf`,\n          image: {\n            type: 'jpeg',\n            quality: 0.98\n          },\n          html2canvas: {\n            scale: 2,\n            useCORS: true,\n            logging: false\n          },\n          jsPDF: {\n            unit: 'mm',\n            format: 'a4',\n            orientation: 'portrait'\n          }\n        };\n        await html2pdf().set(opt).from(element).save();\n        ElMessage.success('PDF报告已导出');\n      } catch (err) {\n        console.error('导出PDF失败:', err);\n        ElMessage.error('导出PDF失败: ' + err.message);\n\n        // 如果客户端导出失败，尝试使用服务器端导出\n        if (props.result?.taskId) {\n          ElMessage.info('正在尝试从服务器导出...');\n          try {\n            const response = await exportPdfReport(props.result.taskId);\n\n            // 处理Blob响应\n            const blob = new Blob([response.data], {\n              type: 'application/pdf'\n            });\n            const url = URL.createObjectURL(blob);\n\n            // 创建下载链接\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `${getReportName()}.pdf`;\n            document.body.appendChild(link);\n            link.click();\n\n            // 清理资源\n            setTimeout(() => {\n              URL.revokeObjectURL(url);\n              document.body.removeChild(link);\n            }, 100);\n            ElMessage.success('PDF报告已从服务器导出');\n          } catch (apiErr) {\n            console.error('服务器导出PDF失败:', apiErr);\n\n            // 检查是否是认证错误\n            if (apiErr.htmlResponse) {\n              ElMessage.error('认证失败，请重新登录后再试');\n              // 询问是否重新登录\n              ElMessageBox.confirm('您的登录会话可能已过期，是否重新登录？', '认证错误', {\n                confirmButtonText: '重新登录',\n                cancelButtonText: '取消',\n                type: 'warning'\n              }).then(() => {\n                // 清除认证信息并跳转到登录页面\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('user');\n                window.location.href = '/login';\n              }).catch(() => {});\n            } else {\n              ElMessage.error('服务器导出PDF也失败: ' + (apiErr.message || '未知错误'));\n            }\n          }\n        }\n      } finally {\n        exporting.value = false;\n      }\n    };\n\n    // 生成报告\n    const generateReport = async () => {\n      generating.value = true;\n      try {\n        // 检查是否有任务ID\n        if (!props.result?.taskId) {\n          throw new Error('缺少任务ID，无法生成报告');\n        }\n\n        // 这里应该调用实际的报告生成API\n        // 为了演示，我们模拟一个API调用\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        console.log('向服务器发送报告生成请求，任务ID:', props.result.taskId);\n\n        // 假设报告生成成功\n        ElMessage.success('报告生成请求已发送，请稍后查看');\n\n        // 这里应该刷新页面或重新加载报告URL\n        // 如果后端有实时通知功能更好\n      } catch (err) {\n        console.error('生成报告失败:', err);\n        ElMessage.error('生成报告失败: ' + err.message);\n      } finally {\n        generating.value = false;\n      }\n    };\n\n    // 监听属性变化\n    onMounted(() => {\n      console.log('VideoReportPanel组件已挂载，reportUrl:', props.reportUrl);\n      if (props.reportUrl) {\n        actualReportUrl.value = getFixedReportUrl(props.reportUrl);\n        console.log('处理后的报告URL:', actualReportUrl.value);\n      } else {\n        console.log('无reportUrl提供');\n      }\n    });\n    return {\n      activeNames,\n      vehicleCount,\n      getCongestionPercentage,\n      getCongestionLevel,\n      getSolutionTitle,\n      getSolutionDescription,\n      getDetailedSuggestions,\n      getEastWestGreenTime,\n      getNorthSouthGreenTime,\n      getEastWestRedTime,\n      getNorthSouthRedTime,\n      iframeLoading,\n      exporting,\n      generating,\n      iframeLoaded,\n      handleIframeError,\n      exportPDF,\n      generateReport,\n      actualReportUrl,\n      getFixedReportUrl\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "ElMessage", "ElMessageBox", "html2pdf", "Document", "Warning", "exportPdfReport", "name", "components", "props", "result", "type", "Object", "default", "reportUrl", "String", "videoPath", "setup", "emit", "activeNames", "iframeLoading", "exporting", "generating", "actualReportUrl", "vehicleCount", "vehicle_count", "getCongestionPercentage", "count", "value", "getCongestionLevel", "getSolutionTitle", "getSolutionDescription", "getDetailedSuggestions", "direction", "suggestions", "push", "getEastWestGreenTime", "getNorthSouthGreenTime", "getEastWestRedTime", "getNorthSouthRedTime", "getFixedReportUrl", "url", "console", "log", "startsWith", "test", "taskId", "iframeLoaded", "iframe", "document", "querySelector", "iframeDoc", "contentDocument", "contentWindow", "title", "body", "innerHTML", "warn", "handleIframeError", "Error", "err", "error", "warning", "message", "duration", "showClose", "onClose", "confirm", "confirmButtonText", "cancelButtonText", "then", "window", "open", "catch", "getReportName", "pathParts", "split", "fileName", "length", "nameWithoutExt", "exportPDF", "response", "blob", "Blob", "data", "URL", "createObjectURL", "link", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "setTimeout", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "success", "htmlResponse", "localStorage", "removeItem", "location", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "includes", "element", "cloneNode", "styles", "opt", "margin", "filename", "image", "quality", "html2canvas", "scale", "useCORS", "logging", "jsPDF", "unit", "format", "orientation", "set", "from", "save", "info", "apiErr", "generateReport", "Promise", "resolve"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\video\\VideoReportPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"video-report-panel\">\n    <div class=\"optimization-section\">\n      <h3>交通拥堵解决方案</h3>\n      \n      <!-- 拥堵状态卡片 -->\n      <el-card class=\"congestion-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <span>当前拥堵状态评估</span>\n          </div>\n        </template>\n        <div class=\"congestion-content\">\n          <div class=\"congestion-level-container\">\n            <div class=\"congestion-meter\">\n              <div class=\"meter-indicator\" :style=\"{ left: getCongestionPercentage + '%' }\"></div>\n              <div class=\"meter-scale\">\n                <span>不拥挤</span>\n                <span>一般</span>\n                <span>较拥挤</span>\n                <span>拥挤</span>\n              </div>\n            </div>\n            <div class=\"congestion-label\">\n              {{ getCongestionLevel }}\n            </div>\n          </div>\n          <div class=\"congestion-details\">\n            <p>当前检测到 <strong>{{ vehicleCount }}</strong> 辆车</p>\n            <p>建议采用: <strong>{{ getSolutionTitle }}</strong></p>\n          </div>\n        </div>\n      </el-card>\n      \n      <!-- 交通方案建议 -->\n      <el-collapse v-model=\"activeNames\" class=\"mt-4\">\n        <el-collapse-item title=\"交通信号灯优化方案\" name=\"1\">\n          <div class=\"solution-content\">\n            <p>基于当前交通流量分析，推荐以下信号灯配置：</p>\n            <div class=\"traffic-light-config\">\n              <el-row :gutter=\"20\">\n                <el-col :span=\"12\">\n                  <div class=\"light-direction\">\n                    <div class=\"direction-label\">东西方向</div>\n                    <div class=\"light-timings\">\n                      <div class=\"light-item\">\n                        <div class=\"light green\"></div>\n                        <div class=\"light-time\">{{ getEastWestGreenTime }}秒</div>\n                      </div>\n                      <div class=\"light-item\">\n                        <div class=\"light yellow\"></div>\n                        <div class=\"light-time\">3秒</div>\n                      </div>\n                      <div class=\"light-item\">\n                        <div class=\"light red\"></div>\n                        <div class=\"light-time\">{{ getEastWestRedTime }}秒</div>\n                      </div>\n                    </div>\n                  </div>\n                </el-col>\n                <el-col :span=\"12\">\n                  <div class=\"light-direction\">\n                    <div class=\"direction-label\">南北方向</div>\n                    <div class=\"light-timings\">\n                      <div class=\"light-item\">\n                        <div class=\"light green\"></div>\n                        <div class=\"light-time\">{{ getNorthSouthGreenTime }}秒</div>\n                      </div>\n                      <div class=\"light-item\">\n                        <div class=\"light yellow\"></div>\n                        <div class=\"light-time\">3秒</div>\n                      </div>\n                      <div class=\"light-item\">\n                        <div class=\"light red\"></div>\n                        <div class=\"light-time\">{{ getNorthSouthRedTime }}秒</div>\n                      </div>\n                    </div>\n                  </div>\n                </el-col>\n              </el-row>\n            </div>\n          </div>\n        </el-collapse-item>\n        \n        <el-collapse-item title=\"具体优化建议\" name=\"2\">\n          <div class=\"suggestion-content\">\n            <p>{{ getSolutionDescription }}</p>\n            <div class=\"suggestion-details\">\n              <h4>详细建议：</h4>\n              <ul class=\"suggestion-list\">\n                <li v-for=\"(suggestion, index) in getDetailedSuggestions\" :key=\"index\">\n                  {{ suggestion }}\n              </li>\n            </ul>\n            </div>\n          </div>\n        </el-collapse-item>\n        \n        <el-collapse-item title=\"长期改进措施\" name=\"3\">\n          <div class=\"suggestion-content\">\n            <p>除了短期交通信号调整外，还建议考虑以下长期改进措施：</p>\n            <ul class=\"suggestion-list\">\n              <li>优化道路设计，增加车道数量或调整车道分配</li>\n              <li>安装智能交通系统，实时监控和调整交通信号</li>\n              <li>实施交通需求管理策略，如错峰出行和公共交通优先</li>\n              <li>在交通高峰期增加交通引导人员</li>\n              <li>建设更完善的公共交通网络，减少私家车使用</li>\n            </ul>\n          </div>\n        </el-collapse-item>\n      </el-collapse>\n    </div>\n    \n    <div class=\"report-section\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"24\">\n          <div class=\"report-header\">\n            <h3>完整分析报告</h3>\n            <div class=\"report-actions\">\n              <el-tooltip content=\"导出PDF报告\" placement=\"top\">\n                <el-button type=\"primary\" @click=\"exportPDF\" :loading=\"exporting\">\n                  <el-icon><document /></el-icon> 导出PDF\n                </el-button>\n              </el-tooltip>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n      \n      <div class=\"report-container\" v-loading=\"iframeLoading\">\n        <iframe \n          v-if=\"reportUrl\" \n          :src=\"getFixedReportUrl(reportUrl)\" \n          @load=\"iframeLoaded\"\n          @error=\"handleIframeError\"\n          ref=\"reportIframe\"\n          frameborder=\"0\" \n          sandbox=\"allow-same-origin allow-scripts allow-forms\"\n          style=\"width:100%; height:500px;\"\n        ></iframe>\n        <div v-else class=\"no-report\">\n          <el-icon><warning /></el-icon>\n          <p>报告生成中或无法加载</p>\n          <el-button type=\"primary\" size=\"small\" @click=\"generateReport\" :loading=\"generating\">\n            生成报告\n          </el-button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport html2pdf from 'html2pdf.js';\nimport { Document, Warning } from '@element-plus/icons-vue';\nimport { exportPdfReport } from '@/api/video';\n\nexport default {\n  name: 'VideoReportPanel',\n  components: {\n    Document,\n    Warning\n  },\n  props: {\n    // 分析结果数据\n    result: {\n      type: Object,\n      default: () => ({})\n    },\n    // 报告的URL\n    reportUrl: {\n      type: String,\n      default: ''\n    },\n    // 视频路径（用于报告名称）\n    videoPath: {\n      type: String,\n      default: ''\n    }\n  },\n  setup(props, { emit }) {\n    const activeNames = ref(['1', '2']); // 默认展开前两个建议\n    const iframeLoading = ref(true);\n    const exporting = ref(false);\n    const generating = ref(false);\n    const actualReportUrl = ref('');\n    \n    // 获取车辆数量\n    const vehicleCount = computed(() => props.result?.vehicle_count || 0);\n    \n    // 计算拥堵百分比位置\n    const getCongestionPercentage = computed(() => {\n      const count = vehicleCount.value;\n      if (count <= 5) return 12.5; // 不拥挤，位于第一档中间\n      if (count <= 10) return 37.5; // 一般，位于第二档中间\n      if (count <= 20) return 62.5; // 较拥挤，位于第三档中间\n      return 87.5; // 拥挤，位于第四档中间\n    });\n    \n    // 获取拥堵等级\n    const getCongestionLevel = computed(() => {\n      const count = vehicleCount.value;\n      if (count <= 5) return '不拥挤';\n      if (count <= 10) return '一般';\n      if (count <= 20) return '较拥挤';\n      return '拥挤';\n    });\n    \n    // 获取解决方案标题\n    const getSolutionTitle = computed(() => {\n      const count = vehicleCount.value;\n      if (count <= 5) return '方案一：正常红绿灯交换';\n      if (count <= 10) return '方案二：延长横向绿灯时间';\n      if (count <= 20) return '方案三：延长纵向绿灯时间';\n      return '方案四：发出提醒（需人为干预）';\n    });\n    \n    // 获取解决方案描述\n    const getSolutionDescription = computed(() => {\n      const count = vehicleCount.value;\n      if (count <= 5) return '当前车流量正常，可维持默认信号灯配置。各方向交通流量均衡，无需特殊干预。';\n      if (count <= 10) return '当前车流量略大，建议延长东西方向绿灯时间，确保车辆顺畅通行。';\n      if (count <= 20) return '当前车流量较大，建议延长南北方向绿灯时间，减少车辆积压。';\n      return '当前车流量大，可能出现拥堵，建议启动交通应急预案，必要时派遣交警现场指挥。';\n    });\n    \n    // 获取详细建议\n    const getDetailedSuggestions = computed(() => {\n      const count = vehicleCount.value;\n      const direction = props.result?.direction || 'horizontal';\n      \n      if (count <= 5) {\n        return [\n          '维持当前信号灯配置，绿灯时间保持在30秒',\n          '保持交通监控系统正常运行',\n          '定期检查道路标志和标线的清晰度'\n        ];\n      } else if (count <= 10) {\n        const suggestions = [\n          '将横向方向绿灯时间延长至45秒',\n          '纵向方向绿灯时间可适当缩短至25秒',\n          '加强对驾驶员的提醒，避免不必要的车道变换'\n        ];\n        \n        if (direction === 'horizontal') {\n          suggestions.push('重点关注东西方向的车流量变化');\n        }\n        \n        return suggestions;\n      } else if (count <= 20) {\n        const suggestions = [\n          '将纵向方向绿灯时间延长至45秒',\n          '横向方向绿灯时间可适当缩短至25秒',\n          '开启辅助车道，增加通行能力',\n          '考虑实施临时交通管制措施'\n        ];\n        \n        if (direction === 'vertical') {\n          suggestions.push('重点关注南北方向的车流量变化');\n        }\n        \n        return suggestions;\n      } else {\n        return [\n          '建议派遣交警到现场指挥交通',\n          '启动交通应急预案，必要时实施单向通行',\n          '向驾驶员发布交通提醒，建议选择替代路线',\n          '临时关闭部分入口，减少车辆进入',\n          '协调相邻路口信号灯，形成绿波带'\n        ];\n      }\n    });\n    \n    // 计算东西向绿灯时间\n    const getEastWestGreenTime = computed(() => {\n      const count = vehicleCount.value;\n      const direction = props.result?.direction || 'horizontal';\n      \n      if (count <= 5) return 30;\n      if (count <= 10) return direction === 'horizontal' ? 45 : 25;\n      if (count <= 20) return direction === 'horizontal' ? 25 : 20;\n      return 20; // 拥挤状态下，适当减少东西向绿灯时间\n    });\n    \n    // 计算南北向绿灯时间\n    const getNorthSouthGreenTime = computed(() => {\n      const count = vehicleCount.value;\n      const direction = props.result?.direction || 'horizontal';\n      \n      if (count <= 5) return 30;\n      if (count <= 10) return direction === 'vertical' ? 40 : 25;\n      if (count <= 20) return direction === 'vertical' ? 45 : 30;\n      return 25; // 拥挤状态下，适当减少南北向绿灯时间\n    });\n    \n    // 计算东西向红灯时间\n    const getEastWestRedTime = computed(() => {\n      return getNorthSouthGreenTime.value + 3; // 绿灯时间 + 黄灯时间\n    });\n    \n    // 计算南北向红灯时间\n    const getNorthSouthRedTime = computed(() => {\n      return getEastWestGreenTime.value + 3; // 绿灯时间 + 黄灯时间\n    });\n    \n    // 尝试修复报告URL\n    const getFixedReportUrl = (url) => {\n      if (!url) return '';\n      \n      console.log('修复报告URL:', url);\n      \n      // 如果已经是完整URL，直接返回\n      if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('/api/')) {\n        return url;\n      }\n      \n      // 如果是TaskID格式（可能是24位十六进制字符串），构建规范的API URL\n      if (/^[0-9a-f]{24}$/i.test(url)) {\n        return `/api/video-analysis/${url}/report`;\n      }\n      \n      // 如果是相对路径，添加API前缀\n      if (url.startsWith('/')) {\n        return `/api${url}`;\n      }\n      \n      // 其他情况，使用标准报告URL格式\n      // 如果有taskId属性，优先使用\n      if (props.result && props.result.taskId) {\n        return `/api/video-analysis/${props.result.taskId}/report`;\n      }\n      \n      // 最后尝试构建一个合理的URL\n      return `/api/static/reports/${url}`;\n    };\n    \n    // iframe加载完成\n    const iframeLoaded = () => {\n      console.log('iframe加载完成，URL:', props.reportUrl);\n      try {\n        const iframe = document.querySelector('.report-container iframe');\n        if (iframe) {\n          // 检查iframe的内容是否加载成功\n          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;\n          if (iframeDoc) {\n            console.log('iframe文档加载成功，标题:', iframeDoc.title);\n            if (iframeDoc.body.innerHTML === '') {\n              console.warn('iframe内容为空，可能加载失败');\n              // 内容为空时也触发错误处理\n              handleIframeError(new Error('iframe内容为空'));\n              return;\n            }\n          } else {\n            console.warn('无法访问iframe内容，可能是跨域问题');\n          }\n        }\n      } catch (err) {\n        console.error('iframe检查失败:', err);\n        handleIframeError(err);\n      }\n      iframeLoading.value = false;\n    };\n    \n    // 处理iframe加载错误\n    const handleIframeError = (error) => {\n      console.error('iframe加载失败:', error);\n      iframeLoading.value = false;\n      ElMessage.warning({\n        message: '报告加载失败，是否在新窗口打开？',\n        duration: 5000,\n        showClose: true,\n        type: 'warning',\n        onClose: () => {}\n      });\n      \n      // 提供在新窗口打开的选项\n      ElMessageBox.confirm(\n        '报告在框架中加载失败，是否在新窗口打开查看？',\n        '报告加载失败',\n        {\n          confirmButtonText: '在新窗口打开',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }\n      ).then(() => {\n        const url = getFixedReportUrl(props.reportUrl);\n        window.open(url, '_blank');\n      }).catch(() => {\n        // 用户取消，不做任何操作\n      });\n    };\n    \n    // 从视频路径中提取文件名作为报告名称\n    const getReportName = () => {\n      if (!props.videoPath) return '交通分析报告';\n      \n      const pathParts = props.videoPath.split('/');\n      const fileName = pathParts[pathParts.length - 1];\n      const nameWithoutExt = fileName.split('.')[0];\n      \n      return `${nameWithoutExt}_交通分析报告`;\n    };\n    \n    // 导出PDF报告\n    const exportPDF = async () => {\n      if (exporting.value) {\n        return;\n      }\n      \n      exporting.value = true;\n      \n      try {\n        // 检查是否有iframe\n        const iframe = document.querySelector('.report-container iframe');\n        if (!iframe && props.result?.taskId) {\n          // 尝试通过API导出\n          console.log('尝试通过API导出PDF报告');\n          \n          try {\n            const response = await exportPdfReport(props.result.taskId);\n            \n            // 处理Blob响应\n            const blob = new Blob([response.data], { type: 'application/pdf' });\n            const url = URL.createObjectURL(blob);\n            \n            // 创建下载链接\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `${getReportName()}.pdf`;\n            document.body.appendChild(link);\n            link.click();\n            \n            // 清理资源\n            setTimeout(() => {\n              URL.revokeObjectURL(url);\n              document.body.removeChild(link);\n            }, 100);\n            \n            ElMessage.success('PDF报告已导出');\n          } catch (error) {\n            console.error('API导出PDF失败:', error);\n            \n            // 处理HTML响应或认证错误\n            if (error.htmlResponse) {\n              ElMessage.error('导出失败：服务器返回了HTML而不是PDF (可能是认证问题)');\n              \n              // 询问用户是否重新登录\n              ElMessageBox.confirm(\n                '您的登录会话可能已过期。是否重新登录？',\n                '认证错误',\n                {\n                  confirmButtonText: '重新登录',\n                  cancelButtonText: '取消',\n                  type: 'warning'\n                }\n              ).then(() => {\n                // 清除认证信息并跳转到登录页面\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('user');\n                window.location.href = '/login';\n              }).catch(() => {});\n              return;\n            }\n            \n            throw new Error(error.message || '通过API导出PDF失败');\n          }\n          return;\n        }\n        \n        if (!iframe) {\n          throw new Error('找不到报告iframe');\n        }\n        \n        // 尝试访问iframe内容\n        let iframeContent;\n        try {\n          iframeContent = iframe.contentDocument || (iframe.contentWindow && iframe.contentWindow.document);\n          \n          // 检查iframe是否加载了HTML内容\n          if (iframeContent && iframeContent.body.innerHTML === '') {\n            throw new Error('iframe内容为空，可能是因为报告未生成或加载失败');\n          }\n          \n          // 检查iframe内容是否为错误页面\n          if (iframeContent && iframeContent.title && \n              (iframeContent.title.includes('Error') || \n               iframeContent.title.includes('错误') ||\n               iframeContent.title.includes('Not Found'))) {\n            throw new Error(`报告加载失败: ${iframeContent.title}`);\n          }\n        } catch (error) {\n          console.error('无法访问iframe内容（可能是跨域问题）:', error);\n          throw new Error('无法访问报告内容，可能是由于跨域限制或报告未生成');\n        }\n        \n        if (!iframeContent) {\n          throw new Error('无法访问iframe内容');\n        }\n        \n        // 创建一个新的容器，复制iframe内容\n        const element = iframeContent.body.cloneNode(true);\n        \n        // 添加样式以确保正确打印\n        const styles = document.createElement('style');\n        styles.innerHTML = `\n          body {\n            font-family: Arial, sans-serif;\n            color: #333;\n            background-color: white;\n          }\n          h1, h2, h3 {\n            color: #1a1a1a;\n          }\n          .chart-container {\n            page-break-inside: avoid;\n            margin-bottom: 20px;\n          }\n        `;\n        element.appendChild(styles);\n        \n        // 使用html2pdf库进行转换\n        const opt = {\n          margin: [10, 10, 10, 10],\n          filename: `${getReportName()}.pdf`,\n          image: { type: 'jpeg', quality: 0.98 },\n          html2canvas: { scale: 2, useCORS: true, logging: false },\n          jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }\n        };\n        \n        await html2pdf().set(opt).from(element).save();\n        ElMessage.success('PDF报告已导出');\n      } catch (err) {\n        console.error('导出PDF失败:', err);\n        ElMessage.error('导出PDF失败: ' + err.message);\n        \n        // 如果客户端导出失败，尝试使用服务器端导出\n        if (props.result?.taskId) {\n          ElMessage.info('正在尝试从服务器导出...');\n          try {\n            const response = await exportPdfReport(props.result.taskId);\n            \n            // 处理Blob响应\n            const blob = new Blob([response.data], { type: 'application/pdf' });\n            const url = URL.createObjectURL(blob);\n            \n            // 创建下载链接\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `${getReportName()}.pdf`;\n            document.body.appendChild(link);\n            link.click();\n            \n            // 清理资源\n            setTimeout(() => {\n              URL.revokeObjectURL(url);\n              document.body.removeChild(link);\n            }, 100);\n            \n            ElMessage.success('PDF报告已从服务器导出');\n          } catch (apiErr) {\n            console.error('服务器导出PDF失败:', apiErr);\n            \n            // 检查是否是认证错误\n            if (apiErr.htmlResponse) {\n              ElMessage.error('认证失败，请重新登录后再试');\n              // 询问是否重新登录\n              ElMessageBox.confirm(\n                '您的登录会话可能已过期，是否重新登录？',\n                '认证错误',\n                {\n                  confirmButtonText: '重新登录',\n                  cancelButtonText: '取消',\n                  type: 'warning'\n                }\n              ).then(() => {\n                // 清除认证信息并跳转到登录页面\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('user');\n                window.location.href = '/login';\n              }).catch(() => {});\n            } else {\n              ElMessage.error('服务器导出PDF也失败: ' + (apiErr.message || '未知错误'));\n            }\n          }\n        }\n      } finally {\n        exporting.value = false;\n      }\n    };\n    \n    // 生成报告\n    const generateReport = async () => {\n      generating.value = true;\n      try {\n        // 检查是否有任务ID\n        if (!props.result?.taskId) {\n          throw new Error('缺少任务ID，无法生成报告');\n        }\n        \n        // 这里应该调用实际的报告生成API\n        // 为了演示，我们模拟一个API调用\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        \n        console.log('向服务器发送报告生成请求，任务ID:', props.result.taskId);\n        \n        // 假设报告生成成功\n        ElMessage.success('报告生成请求已发送，请稍后查看');\n        \n        // 这里应该刷新页面或重新加载报告URL\n        // 如果后端有实时通知功能更好\n      } catch (err) {\n        console.error('生成报告失败:', err);\n        ElMessage.error('生成报告失败: ' + err.message);\n      } finally {\n        generating.value = false;\n      }\n    };\n    \n    // 监听属性变化\n    onMounted(() => {\n      console.log('VideoReportPanel组件已挂载，reportUrl:', props.reportUrl);\n      if (props.reportUrl) {\n        actualReportUrl.value = getFixedReportUrl(props.reportUrl);\n        console.log('处理后的报告URL:', actualReportUrl.value);\n      } else {\n        console.log('无reportUrl提供');\n      }\n    });\n    \n    return {\n      activeNames,\n      vehicleCount,\n      getCongestionPercentage,\n      getCongestionLevel,\n      getSolutionTitle,\n      getSolutionDescription,\n      getDetailedSuggestions,\n      getEastWestGreenTime,\n      getNorthSouthGreenTime,\n      getEastWestRedTime,\n      getNorthSouthRedTime,\n      iframeLoading,\n      exporting,\n      generating,\n      iframeLoaded,\n      handleIframeError,\n      exportPDF,\n      generateReport,\n      actualReportUrl,\n      getFixedReportUrl\n    };\n  }\n};\n</script>\n\n<style scoped>\n.video-report-panel {\n  width: 100%;\n}\n\n.optimization-section,\n.report-section {\n  margin: 20px 0;\n}\n\nh3 {\n  color: #ffffff !important;\n  font-weight: 600 !important;\n  margin-bottom: 15px;\n}\n\nh4 {\n  color: #e5e7eb !important;\n  font-weight: 600 !important;\n  margin: 15px 0 10px 0;\n}\n\n.congestion-card {\n  margin-bottom: 20px;\n  background: rgba(255, 255, 255, 0.05) !important;\n  border: 1px solid rgba(255, 255, 255, 0.06) !important;\n}\n\n:deep(.congestion-card .el-card__header) {\n  background-color: rgba(26, 32, 50, 0.8);\n}\n\n.card-header {\n  font-weight: bold;\n  color: #ffffff;\n}\n\n.congestion-content {\n  padding: 20px 0;\n}\n\n.congestion-level-container {\n  text-align: center;\n  margin-bottom: 20px;\n}\n\n.congestion-meter {\n  height: 20px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  position: relative;\n  margin: 0 auto 15px;\n  width: 80%;\n}\n\n.meter-indicator {\n  width: 20px;\n  height: 20px;\n  background: #6366f1;\n  border-radius: 50%;\n  position: absolute;\n  top: 0;\n  transform: translateX(-50%);\n  box-shadow: 0 0 10px rgba(99, 102, 241, 0.7);\n}\n\n.meter-scale {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 5px;\n  color: #d1d5db;\n  font-size: 0.85rem;\n}\n\n.congestion-label {\n  font-size: 24px;\n  font-weight: 700;\n  color: #6366f1;\n  margin: 15px 0;\n}\n\n.congestion-details {\n  text-align: center;\n  color: #e5e7eb;\n}\n\n.congestion-details p {\n  margin: 5px 0;\n}\n\n.congestion-details strong {\n  color: #ffffff;\n  font-weight: 600;\n}\n\n.mt-4 {\n  margin-top: 1rem;\n}\n\n.suggestion-content {\n  color: #e5e7eb;\n  padding: 10px;\n}\n\n.suggestion-list {\n  margin-top: 10px;\n  padding-left: 20px;\n}\n\n.suggestion-list li {\n  margin-bottom: 8px;\n  position: relative;\n}\n\n.suggestion-list li::before {\n  content: '•';\n  color: #6366f1;\n  font-weight: bold;\n  position: absolute;\n  left: -15px;\n}\n\n.report-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.report-container {\n  background: rgba(17, 24, 39, 0.5);\n  border-radius: 8px;\n  padding: 15px;\n  height: 530px;\n}\n\n.no-report {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  color: #909399;\n}\n\n.no-report .el-icon {\n  font-size: 48px;\n  margin-bottom: 20px;\n}\n\n:deep(.el-collapse) {\n  --el-collapse-header-bg-color: rgba(17, 24, 39, 0.5);\n  --el-collapse-header-text-color: #e5e7eb;\n  --el-collapse-content-bg-color: rgba(31, 41, 55, 0.5);\n  --el-collapse-content-text-color: #e5e7eb;\n  border-radius: 8px;\n  overflow: hidden;\n  border: none;\n}\n\n:deep(.el-collapse-item__header) {\n  font-weight: 600;\n}\n\n:deep(.el-collapse-item__wrap) {\n  border-bottom: none;\n}\n\n/* 信号灯配置样式 */\n.traffic-light-config {\n  margin-top: 15px;\n}\n\n.light-direction {\n  background: rgba(17, 24, 39, 0.7);\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 10px;\n}\n\n.direction-label {\n  text-align: center;\n  font-weight: 600;\n  color: #ffffff;\n  margin-bottom: 15px;\n  font-size: 16px;\n}\n\n.light-timings {\n  display: flex;\n  justify-content: space-around;\n}\n\n.light-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.light {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  margin-bottom: 8px;\n}\n\n.light.red {\n  background-color: #ef4444;\n  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);\n}\n\n.light.yellow {\n  background-color: #f59e0b;\n  box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);\n}\n\n.light.green {\n  background-color: #10b981;\n  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);\n}\n\n.light-time {\n  font-size: 14px;\n  color: #d1d5db;\n}\n</style> "], "mappings": ";AAyJA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAK;AAC9C,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAc;AACtD,OAAOC,QAAO,MAAO,aAAa;AAClC,SAASC,QAAQ,EAAEC,OAAM,QAAS,yBAAyB;AAC3D,SAASC,eAAc,QAAS,aAAa;AAE7C,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IACVJ,QAAQ;IACRC;EACF,CAAC;EACDI,KAAK,EAAE;IACL;IACAC,MAAM,EAAE;MACNC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB,CAAC;IACD;IACAC,SAAS,EAAE;MACTH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACD;IACAG,SAAS,EAAE;MACTL,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX;EACF,CAAC;EACDI,KAAKA,CAACR,KAAK,EAAE;IAAES;EAAK,CAAC,EAAE;IACrB,MAAMC,WAAU,GAAIrB,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE;IACrC,MAAMsB,aAAY,GAAItB,GAAG,CAAC,IAAI,CAAC;IAC/B,MAAMuB,SAAQ,GAAIvB,GAAG,CAAC,KAAK,CAAC;IAC5B,MAAMwB,UAAS,GAAIxB,GAAG,CAAC,KAAK,CAAC;IAC7B,MAAMyB,eAAc,GAAIzB,GAAG,CAAC,EAAE,CAAC;;IAE/B;IACA,MAAM0B,YAAW,GAAIzB,QAAQ,CAAC,MAAMU,KAAK,CAACC,MAAM,EAAEe,aAAY,IAAK,CAAC,CAAC;;IAErE;IACA,MAAMC,uBAAsB,GAAI3B,QAAQ,CAAC,MAAM;MAC7C,MAAM4B,KAAI,GAAIH,YAAY,CAACI,KAAK;MAChC,IAAID,KAAI,IAAK,CAAC,EAAE,OAAO,IAAI,EAAE;MAC7B,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,IAAI,EAAE;MAC9B,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,IAAI,EAAE;MAC9B,OAAO,IAAI,EAAE;IACf,CAAC,CAAC;;IAEF;IACA,MAAME,kBAAiB,GAAI9B,QAAQ,CAAC,MAAM;MACxC,MAAM4B,KAAI,GAAIH,YAAY,CAACI,KAAK;MAChC,IAAID,KAAI,IAAK,CAAC,EAAE,OAAO,KAAK;MAC5B,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,IAAI;MAC5B,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,KAAK;MAC7B,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,MAAMG,gBAAe,GAAI/B,QAAQ,CAAC,MAAM;MACtC,MAAM4B,KAAI,GAAIH,YAAY,CAACI,KAAK;MAChC,IAAID,KAAI,IAAK,CAAC,EAAE,OAAO,aAAa;MACpC,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,cAAc;MACtC,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,cAAc;MACtC,OAAO,iBAAiB;IAC1B,CAAC,CAAC;;IAEF;IACA,MAAMI,sBAAqB,GAAIhC,QAAQ,CAAC,MAAM;MAC5C,MAAM4B,KAAI,GAAIH,YAAY,CAACI,KAAK;MAChC,IAAID,KAAI,IAAK,CAAC,EAAE,OAAO,sCAAsC;MAC7D,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,gCAAgC;MACxD,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,8BAA8B;MACtD,OAAO,uCAAuC;IAChD,CAAC,CAAC;;IAEF;IACA,MAAMK,sBAAqB,GAAIjC,QAAQ,CAAC,MAAM;MAC5C,MAAM4B,KAAI,GAAIH,YAAY,CAACI,KAAK;MAChC,MAAMK,SAAQ,GAAIxB,KAAK,CAACC,MAAM,EAAEuB,SAAQ,IAAK,YAAY;MAEzD,IAAIN,KAAI,IAAK,CAAC,EAAE;QACd,OAAO,CACL,sBAAsB,EACtB,cAAc,EACd,iBAAgB,CACjB;MACH,OAAO,IAAIA,KAAI,IAAK,EAAE,EAAE;QACtB,MAAMO,WAAU,GAAI,CAClB,iBAAiB,EACjB,mBAAmB,EACnB,sBAAqB,CACtB;QAED,IAAID,SAAQ,KAAM,YAAY,EAAE;UAC9BC,WAAW,CAACC,IAAI,CAAC,gBAAgB,CAAC;QACpC;QAEA,OAAOD,WAAW;MACpB,OAAO,IAAIP,KAAI,IAAK,EAAE,EAAE;QACtB,MAAMO,WAAU,GAAI,CAClB,iBAAiB,EACjB,mBAAmB,EACnB,eAAe,EACf,cAAa,CACd;QAED,IAAID,SAAQ,KAAM,UAAU,EAAE;UAC5BC,WAAW,CAACC,IAAI,CAAC,gBAAgB,CAAC;QACpC;QAEA,OAAOD,WAAW;MACpB,OAAO;QACL,OAAO,CACL,eAAe,EACf,oBAAoB,EACpB,qBAAqB,EACrB,iBAAiB,EACjB,iBAAgB,CACjB;MACH;IACF,CAAC,CAAC;;IAEF;IACA,MAAME,oBAAmB,GAAIrC,QAAQ,CAAC,MAAM;MAC1C,MAAM4B,KAAI,GAAIH,YAAY,CAACI,KAAK;MAChC,MAAMK,SAAQ,GAAIxB,KAAK,CAACC,MAAM,EAAEuB,SAAQ,IAAK,YAAY;MAEzD,IAAIN,KAAI,IAAK,CAAC,EAAE,OAAO,EAAE;MACzB,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAOM,SAAQ,KAAM,YAAW,GAAI,EAAC,GAAI,EAAE;MAC5D,IAAIN,KAAI,IAAK,EAAE,EAAE,OAAOM,SAAQ,KAAM,YAAW,GAAI,EAAC,GAAI,EAAE;MAC5D,OAAO,EAAE,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,MAAMI,sBAAqB,GAAItC,QAAQ,CAAC,MAAM;MAC5C,MAAM4B,KAAI,GAAIH,YAAY,CAACI,KAAK;MAChC,MAAMK,SAAQ,GAAIxB,KAAK,CAACC,MAAM,EAAEuB,SAAQ,IAAK,YAAY;MAEzD,IAAIN,KAAI,IAAK,CAAC,EAAE,OAAO,EAAE;MACzB,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAOM,SAAQ,KAAM,UAAS,GAAI,EAAC,GAAI,EAAE;MAC1D,IAAIN,KAAI,IAAK,EAAE,EAAE,OAAOM,SAAQ,KAAM,UAAS,GAAI,EAAC,GAAI,EAAE;MAC1D,OAAO,EAAE,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,MAAMK,kBAAiB,GAAIvC,QAAQ,CAAC,MAAM;MACxC,OAAOsC,sBAAsB,CAACT,KAAI,GAAI,CAAC,EAAE;IAC3C,CAAC,CAAC;;IAEF;IACA,MAAMW,oBAAmB,GAAIxC,QAAQ,CAAC,MAAM;MAC1C,OAAOqC,oBAAoB,CAACR,KAAI,GAAI,CAAC,EAAE;IACzC,CAAC,CAAC;;IAEF;IACA,MAAMY,iBAAgB,GAAKC,GAAG,IAAK;MACjC,IAAI,CAACA,GAAG,EAAE,OAAO,EAAE;MAEnBC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,GAAG,CAAC;;MAE5B;MACA,IAAIA,GAAG,CAACG,UAAU,CAAC,SAAS,KAAKH,GAAG,CAACG,UAAU,CAAC,UAAU,KAAKH,GAAG,CAACG,UAAU,CAAC,OAAO,CAAC,EAAE;QACtF,OAAOH,GAAG;MACZ;;MAEA;MACA,IAAI,iBAAiB,CAACI,IAAI,CAACJ,GAAG,CAAC,EAAE;QAC/B,OAAO,uBAAuBA,GAAG,SAAS;MAC5C;;MAEA;MACA,IAAIA,GAAG,CAACG,UAAU,CAAC,GAAG,CAAC,EAAE;QACvB,OAAO,OAAOH,GAAG,EAAE;MACrB;;MAEA;MACA;MACA,IAAIhC,KAAK,CAACC,MAAK,IAAKD,KAAK,CAACC,MAAM,CAACoC,MAAM,EAAE;QACvC,OAAO,uBAAuBrC,KAAK,CAACC,MAAM,CAACoC,MAAM,SAAS;MAC5D;;MAEA;MACA,OAAO,uBAAuBL,GAAG,EAAE;IACrC,CAAC;;IAED;IACA,MAAMM,YAAW,GAAIA,CAAA,KAAM;MACzBL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAElC,KAAK,CAACK,SAAS,CAAC;MAC/C,IAAI;QACF,MAAMkC,MAAK,GAAIC,QAAQ,CAACC,aAAa,CAAC,0BAA0B,CAAC;QACjE,IAAIF,MAAM,EAAE;UACV;UACA,MAAMG,SAAQ,GAAIH,MAAM,CAACI,eAAc,IAAKJ,MAAM,CAACK,aAAa,EAAEJ,QAAQ;UAC1E,IAAIE,SAAS,EAAE;YACbT,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEQ,SAAS,CAACG,KAAK,CAAC;YAChD,IAAIH,SAAS,CAACI,IAAI,CAACC,SAAQ,KAAM,EAAE,EAAE;cACnCd,OAAO,CAACe,IAAI,CAAC,mBAAmB,CAAC;cACjC;cACAC,iBAAiB,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;cAC1C;YACF;UACF,OAAO;YACLjB,OAAO,CAACe,IAAI,CAAC,sBAAsB,CAAC;UACtC;QACF;MACF,EAAE,OAAOG,GAAG,EAAE;QACZlB,OAAO,CAACmB,KAAK,CAAC,aAAa,EAAED,GAAG,CAAC;QACjCF,iBAAiB,CAACE,GAAG,CAAC;MACxB;MACAxC,aAAa,CAACQ,KAAI,GAAI,KAAK;IAC7B,CAAC;;IAED;IACA,MAAM8B,iBAAgB,GAAKG,KAAK,IAAK;MACnCnB,OAAO,CAACmB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCzC,aAAa,CAACQ,KAAI,GAAI,KAAK;MAC3B3B,SAAS,CAAC6D,OAAO,CAAC;QAChBC,OAAO,EAAE,kBAAkB;QAC3BC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAI;QACftD,IAAI,EAAE,SAAS;QACfuD,OAAO,EAAEA,CAAA,KAAM,CAAC;MAClB,CAAC,CAAC;;MAEF;MACAhE,YAAY,CAACiE,OAAO,CAClB,wBAAwB,EACxB,QAAQ,EACR;QACEC,iBAAiB,EAAE,QAAQ;QAC3BC,gBAAgB,EAAE,IAAI;QACtB1D,IAAI,EAAE;MACR,CACF,CAAC,CAAC2D,IAAI,CAAC,MAAM;QACX,MAAM7B,GAAE,GAAID,iBAAiB,CAAC/B,KAAK,CAACK,SAAS,CAAC;QAC9CyD,MAAM,CAACC,IAAI,CAAC/B,GAAG,EAAE,QAAQ,CAAC;MAC5B,CAAC,CAAC,CAACgC,KAAK,CAAC,MAAM;QACb;MAAA,CACD,CAAC;IACJ,CAAC;;IAED;IACA,MAAMC,aAAY,GAAIA,CAAA,KAAM;MAC1B,IAAI,CAACjE,KAAK,CAACO,SAAS,EAAE,OAAO,QAAQ;MAErC,MAAM2D,SAAQ,GAAIlE,KAAK,CAACO,SAAS,CAAC4D,KAAK,CAAC,GAAG,CAAC;MAC5C,MAAMC,QAAO,GAAIF,SAAS,CAACA,SAAS,CAACG,MAAK,GAAI,CAAC,CAAC;MAChD,MAAMC,cAAa,GAAIF,QAAQ,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAE7C,OAAO,GAAGG,cAAc,SAAS;IACnC,CAAC;;IAED;IACA,MAAMC,SAAQ,GAAI,MAAAA,CAAA,KAAY;MAC5B,IAAI3D,SAAS,CAACO,KAAK,EAAE;QACnB;MACF;MAEAP,SAAS,CAACO,KAAI,GAAI,IAAI;MAEtB,IAAI;QACF;QACA,MAAMoB,MAAK,GAAIC,QAAQ,CAACC,aAAa,CAAC,0BAA0B,CAAC;QACjE,IAAI,CAACF,MAAK,IAAKvC,KAAK,CAACC,MAAM,EAAEoC,MAAM,EAAE;UACnC;UACAJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAE7B,IAAI;YACF,MAAMsC,QAAO,GAAI,MAAM3E,eAAe,CAACG,KAAK,CAACC,MAAM,CAACoC,MAAM,CAAC;;YAE3D;YACA,MAAMoC,IAAG,GAAI,IAAIC,IAAI,CAAC,CAACF,QAAQ,CAACG,IAAI,CAAC,EAAE;cAAEzE,IAAI,EAAE;YAAkB,CAAC,CAAC;YACnE,MAAM8B,GAAE,GAAI4C,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;;YAErC;YACA,MAAMK,IAAG,GAAItC,QAAQ,CAACuC,aAAa,CAAC,GAAG,CAAC;YACxCD,IAAI,CAACE,IAAG,GAAIhD,GAAG;YACf8C,IAAI,CAACG,QAAO,GAAI,GAAGhB,aAAa,CAAC,CAAC,MAAM;YACxCzB,QAAQ,CAACM,IAAI,CAACoC,WAAW,CAACJ,IAAI,CAAC;YAC/BA,IAAI,CAACK,KAAK,CAAC,CAAC;;YAEZ;YACAC,UAAU,CAAC,MAAM;cACfR,GAAG,CAACS,eAAe,CAACrD,GAAG,CAAC;cACxBQ,QAAQ,CAACM,IAAI,CAACwC,WAAW,CAACR,IAAI,CAAC;YACjC,CAAC,EAAE,GAAG,CAAC;YAEPtF,SAAS,CAAC+F,OAAO,CAAC,UAAU,CAAC;UAC/B,EAAE,OAAOnC,KAAK,EAAE;YACdnB,OAAO,CAACmB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;;YAEnC;YACA,IAAIA,KAAK,CAACoC,YAAY,EAAE;cACtBhG,SAAS,CAAC4D,KAAK,CAAC,iCAAiC,CAAC;;cAElD;cACA3D,YAAY,CAACiE,OAAO,CAClB,qBAAqB,EACrB,MAAM,EACN;gBACEC,iBAAiB,EAAE,MAAM;gBACzBC,gBAAgB,EAAE,IAAI;gBACtB1D,IAAI,EAAE;cACR,CACF,CAAC,CAAC2D,IAAI,CAAC,MAAM;gBACX;gBACA4B,YAAY,CAACC,UAAU,CAAC,YAAY,CAAC;gBACrCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;gBAC/B5B,MAAM,CAAC6B,QAAQ,CAACX,IAAG,GAAI,QAAQ;cACjC,CAAC,CAAC,CAAChB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;cAClB;YACF;YAEA,MAAM,IAAId,KAAK,CAACE,KAAK,CAACE,OAAM,IAAK,cAAc,CAAC;UAClD;UACA;QACF;QAEA,IAAI,CAACf,MAAM,EAAE;UACX,MAAM,IAAIW,KAAK,CAAC,aAAa,CAAC;QAChC;;QAEA;QACA,IAAI0C,aAAa;QACjB,IAAI;UACFA,aAAY,GAAIrD,MAAM,CAACI,eAAc,IAAMJ,MAAM,CAACK,aAAY,IAAKL,MAAM,CAACK,aAAa,CAACJ,QAAS;;UAEjG;UACA,IAAIoD,aAAY,IAAKA,aAAa,CAAC9C,IAAI,CAACC,SAAQ,KAAM,EAAE,EAAE;YACxD,MAAM,IAAIG,KAAK,CAAC,4BAA4B,CAAC;UAC/C;;UAEA;UACA,IAAI0C,aAAY,IAAKA,aAAa,CAAC/C,KAAI,KAClC+C,aAAa,CAAC/C,KAAK,CAACgD,QAAQ,CAAC,OAAO,KACpCD,aAAa,CAAC/C,KAAK,CAACgD,QAAQ,CAAC,IAAI,KACjCD,aAAa,CAAC/C,KAAK,CAACgD,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE;YAC/C,MAAM,IAAI3C,KAAK,CAAC,WAAW0C,aAAa,CAAC/C,KAAK,EAAE,CAAC;UACnD;QACF,EAAE,OAAOO,KAAK,EAAE;UACdnB,OAAO,CAACmB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,MAAM,IAAIF,KAAK,CAAC,0BAA0B,CAAC;QAC7C;QAEA,IAAI,CAAC0C,aAAa,EAAE;UAClB,MAAM,IAAI1C,KAAK,CAAC,cAAc,CAAC;QACjC;;QAEA;QACA,MAAM4C,OAAM,GAAIF,aAAa,CAAC9C,IAAI,CAACiD,SAAS,CAAC,IAAI,CAAC;;QAElD;QACA,MAAMC,MAAK,GAAIxD,QAAQ,CAACuC,aAAa,CAAC,OAAO,CAAC;QAC9CiB,MAAM,CAACjD,SAAQ,GAAI;;;;;;;;;;;;;SAalB;QACD+C,OAAO,CAACZ,WAAW,CAACc,MAAM,CAAC;;QAE3B;QACA,MAAMC,GAAE,GAAI;UACVC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;UACxBC,QAAQ,EAAE,GAAGlC,aAAa,CAAC,CAAC,MAAM;UAClCmC,KAAK,EAAE;YAAElG,IAAI,EAAE,MAAM;YAAEmG,OAAO,EAAE;UAAK,CAAC;UACtCC,WAAW,EAAE;YAAEC,KAAK,EAAE,CAAC;YAAEC,OAAO,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAM,CAAC;UACxDC,KAAK,EAAE;YAAEC,IAAI,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI;YAAEC,WAAW,EAAE;UAAW;QAC7D,CAAC;QAED,MAAMnH,QAAQ,CAAC,CAAC,CAACoH,GAAG,CAACb,GAAG,CAAC,CAACc,IAAI,CAACjB,OAAO,CAAC,CAACkB,IAAI,CAAC,CAAC;QAC9CxH,SAAS,CAAC+F,OAAO,CAAC,UAAU,CAAC;MAC/B,EAAE,OAAOpC,GAAG,EAAE;QACZlB,OAAO,CAACmB,KAAK,CAAC,UAAU,EAAED,GAAG,CAAC;QAC9B3D,SAAS,CAAC4D,KAAK,CAAC,WAAU,GAAID,GAAG,CAACG,OAAO,CAAC;;QAE1C;QACA,IAAItD,KAAK,CAACC,MAAM,EAAEoC,MAAM,EAAE;UACxB7C,SAAS,CAACyH,IAAI,CAAC,eAAe,CAAC;UAC/B,IAAI;YACF,MAAMzC,QAAO,GAAI,MAAM3E,eAAe,CAACG,KAAK,CAACC,MAAM,CAACoC,MAAM,CAAC;;YAE3D;YACA,MAAMoC,IAAG,GAAI,IAAIC,IAAI,CAAC,CAACF,QAAQ,CAACG,IAAI,CAAC,EAAE;cAAEzE,IAAI,EAAE;YAAkB,CAAC,CAAC;YACnE,MAAM8B,GAAE,GAAI4C,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;;YAErC;YACA,MAAMK,IAAG,GAAItC,QAAQ,CAACuC,aAAa,CAAC,GAAG,CAAC;YACxCD,IAAI,CAACE,IAAG,GAAIhD,GAAG;YACf8C,IAAI,CAACG,QAAO,GAAI,GAAGhB,aAAa,CAAC,CAAC,MAAM;YACxCzB,QAAQ,CAACM,IAAI,CAACoC,WAAW,CAACJ,IAAI,CAAC;YAC/BA,IAAI,CAACK,KAAK,CAAC,CAAC;;YAEZ;YACAC,UAAU,CAAC,MAAM;cACfR,GAAG,CAACS,eAAe,CAACrD,GAAG,CAAC;cACxBQ,QAAQ,CAACM,IAAI,CAACwC,WAAW,CAACR,IAAI,CAAC;YACjC,CAAC,EAAE,GAAG,CAAC;YAEPtF,SAAS,CAAC+F,OAAO,CAAC,cAAc,CAAC;UACnC,EAAE,OAAO2B,MAAM,EAAE;YACfjF,OAAO,CAACmB,KAAK,CAAC,aAAa,EAAE8D,MAAM,CAAC;;YAEpC;YACA,IAAIA,MAAM,CAAC1B,YAAY,EAAE;cACvBhG,SAAS,CAAC4D,KAAK,CAAC,eAAe,CAAC;cAChC;cACA3D,YAAY,CAACiE,OAAO,CAClB,qBAAqB,EACrB,MAAM,EACN;gBACEC,iBAAiB,EAAE,MAAM;gBACzBC,gBAAgB,EAAE,IAAI;gBACtB1D,IAAI,EAAE;cACR,CACF,CAAC,CAAC2D,IAAI,CAAC,MAAM;gBACX;gBACA4B,YAAY,CAACC,UAAU,CAAC,YAAY,CAAC;gBACrCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;gBAC/B5B,MAAM,CAAC6B,QAAQ,CAACX,IAAG,GAAI,QAAQ;cACjC,CAAC,CAAC,CAAChB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YACpB,OAAO;cACLxE,SAAS,CAAC4D,KAAK,CAAC,eAAc,IAAK8D,MAAM,CAAC5D,OAAM,IAAK,MAAM,CAAC,CAAC;YAC/D;UACF;QACF;MACF,UAAU;QACR1C,SAAS,CAACO,KAAI,GAAI,KAAK;MACzB;IACF,CAAC;;IAED;IACA,MAAMgG,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjCtG,UAAU,CAACM,KAAI,GAAI,IAAI;MACvB,IAAI;QACF;QACA,IAAI,CAACnB,KAAK,CAACC,MAAM,EAAEoC,MAAM,EAAE;UACzB,MAAM,IAAIa,KAAK,CAAC,eAAe,CAAC;QAClC;;QAEA;QACA;QACA,MAAM,IAAIkE,OAAO,CAACC,OAAM,IAAKjC,UAAU,CAACiC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEvDpF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAElC,KAAK,CAACC,MAAM,CAACoC,MAAM,CAAC;;QAEtD;QACA7C,SAAS,CAAC+F,OAAO,CAAC,iBAAiB,CAAC;;QAEpC;QACA;MACF,EAAE,OAAOpC,GAAG,EAAE;QACZlB,OAAO,CAACmB,KAAK,CAAC,SAAS,EAAED,GAAG,CAAC;QAC7B3D,SAAS,CAAC4D,KAAK,CAAC,UAAS,GAAID,GAAG,CAACG,OAAO,CAAC;MAC3C,UAAU;QACRzC,UAAU,CAACM,KAAI,GAAI,KAAK;MAC1B;IACF,CAAC;;IAED;IACA5B,SAAS,CAAC,MAAM;MACd0C,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAElC,KAAK,CAACK,SAAS,CAAC;MAChE,IAAIL,KAAK,CAACK,SAAS,EAAE;QACnBS,eAAe,CAACK,KAAI,GAAIY,iBAAiB,CAAC/B,KAAK,CAACK,SAAS,CAAC;QAC1D4B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEpB,eAAe,CAACK,KAAK,CAAC;MAClD,OAAO;QACLc,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAC7B;IACF,CAAC,CAAC;IAEF,OAAO;MACLxB,WAAW;MACXK,YAAY;MACZE,uBAAuB;MACvBG,kBAAkB;MAClBC,gBAAgB;MAChBC,sBAAsB;MACtBC,sBAAsB;MACtBI,oBAAoB;MACpBC,sBAAsB;MACtBC,kBAAkB;MAClBC,oBAAoB;MACpBnB,aAAa;MACbC,SAAS;MACTC,UAAU;MACVyB,YAAY;MACZW,iBAAiB;MACjBsB,SAAS;MACT4C,cAAc;MACdrG,eAAe;MACfiB;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}