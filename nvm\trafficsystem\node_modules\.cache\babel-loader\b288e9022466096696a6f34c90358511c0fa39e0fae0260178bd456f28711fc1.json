{"ast": null, "code": "// Vue特性标志\nwindow.__VUE_PROD_DEVTOOLS__ = false;\nwindow.__VUE_PROD_HYDRATION_MISMATCH_DETAILS__ = false;\nimport { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\n\n// 导入Element Plus\nimport ElementPlus from 'element-plus';\nimport 'element-plus/dist/index.css';\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue';\nimport zhCn from 'element-plus/es/locale/lang/zh-cn';\n\n// 导入自定义样式\nimport './assets/styles.css';\n\n// 添加全局样式，防止导航栏字体变色\nimport './styles/prevent-color-change.css';\n\n// 导入双视频预览样式\nimport './styles/dual-video-preview.css';\n\n// 导入交通分析样式\nimport './styles/traffic-analysis.css';\n\n// 导入Bootstrap样式和图标\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport 'bootstrap-icons/font/bootstrap-icons.css';\n// 导入Bootstrap脚本\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\n\n// 导入并初始化STOMP服务\nimport stompService from './utils/stomp-service';\n\n// 导入WebSocket重连工具\nimport wsReconnector from './utils/websocket-reconnect';\n\n// 开发环境下加载调试工具\nif (process.env.NODE_ENV === 'development') {\n  import('./utils/websocket-debug.js').then(() => {\n    console.log('🔧 WebSocket调试工具已加载');\n  }).catch(err => {\n    console.warn('调试工具加载失败:', err);\n  });\n}\n\n// 创建Vue应用实例\nconst app = createApp(App);\n\n// 全局错误处理器\napp.config.errorHandler = (err, vm, info) => {\n  if (err.name === 'ChunkLoadError' || err.message && err.message.includes('Loading chunk')) {\n    sessionStorage.clear();\n    setTimeout(() => window.location.reload(), 1000);\n  }\n};\n\n// 注册Element Plus图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component);\n}\n\n// 使用插件\napp.use(store);\napp.use(router);\napp.use(ElementPlus, {\n  locale: zhCn\n});\n\n// 初始化STOMP服务连接\nstompService.init().catch(() => {\n  // STOMP服务初始化失败时静默处理\n});\n\n// 启动WebSocket重连监控\nsetTimeout(() => {\n  wsReconnector.startPeriodicCheck();\n}, 3000);\n\n// 开发环境下将stompService挂载到全局\nif (process.env.NODE_ENV === 'development') {\n  window.stompService = stompService;\n}\n\n// 挂载应用\napp.mount('#app');", "map": {"version": 3, "names": ["window", "__VUE_PROD_DEVTOOLS__", "__VUE_PROD_HYDRATION_MISMATCH_DETAILS__", "createApp", "App", "router", "store", "ElementPlus", "ElementPlusIconsVue", "zhCn", "stompService", "wsReconnector", "process", "env", "NODE_ENV", "then", "console", "log", "catch", "err", "warn", "app", "config", "<PERSON><PERSON><PERSON><PERSON>", "vm", "info", "name", "message", "includes", "sessionStorage", "clear", "setTimeout", "location", "reload", "key", "component", "Object", "entries", "use", "locale", "init", "startPeriodicCheck", "mount"], "sources": ["D:/code/nvm/trafficsystem/src/main.js"], "sourcesContent": ["// Vue特性标志\nwindow.__VUE_PROD_DEVTOOLS__ = false;\nwindow.__VUE_PROD_HYDRATION_MISMATCH_DETAILS__ = false;\n\nimport { createApp } from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\n\n// 导入Element Plus\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\nimport zhCn from 'element-plus/es/locale/lang/zh-cn'\n\n// 导入自定义样式\nimport './assets/styles.css'\n\n// 添加全局样式，防止导航栏字体变色\nimport './styles/prevent-color-change.css'\n\n// 导入双视频预览样式\nimport './styles/dual-video-preview.css'\n\n// 导入交通分析样式\nimport './styles/traffic-analysis.css'\n\n// 导入Bootstrap样式和图标\nimport 'bootstrap/dist/css/bootstrap.min.css'\nimport 'bootstrap-icons/font/bootstrap-icons.css'\n// 导入Bootstrap脚本\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js'\n\n// 导入并初始化STOMP服务\nimport stompService from './utils/stomp-service'\n\n// 导入WebSocket重连工具\nimport wsReconnector from './utils/websocket-reconnect'\n\n// 开发环境下加载调试工具\nif (process.env.NODE_ENV === 'development') {\n  import('./utils/websocket-debug.js').then(() => {\n    console.log('🔧 WebSocket调试工具已加载')\n  }).catch(err => {\n    console.warn('调试工具加载失败:', err)\n  })\n}\n\n// 创建Vue应用实例\nconst app = createApp(App)\n\n// 全局错误处理器\napp.config.errorHandler = (err, vm, info) => {\n  if (err.name === 'ChunkLoadError' || (err.message && err.message.includes('Loading chunk'))) {\n    sessionStorage.clear();\n    setTimeout(() => window.location.reload(), 1000);\n  }\n};\n\n// 注册Element Plus图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component)\n}\n\n// 使用插件\napp.use(store)\napp.use(router)\napp.use(ElementPlus, {\n  locale: zhCn\n})\n\n// 初始化STOMP服务连接\nstompService.init().catch(() => {\n  // STOMP服务初始化失败时静默处理\n});\n\n// 启动WebSocket重连监控\nsetTimeout(() => {\n  wsReconnector.startPeriodicCheck()\n}, 3000)\n\n// 开发环境下将stompService挂载到全局\nif (process.env.NODE_ENV === 'development') {\n  window.stompService = stompService;\n}\n\n// 挂载应用\napp.mount('#app')\n"], "mappings": "AAAA;AACAA,MAAM,CAACC,qBAAqB,GAAG,KAAK;AACpCD,MAAM,CAACE,uCAAuC,GAAG,KAAK;AAEtD,SAASC,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;;AAE3B;AACA,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAO,6BAA6B;AACpC,OAAO,KAAKC,mBAAmB,MAAM,yBAAyB;AAC9D,OAAOC,IAAI,MAAM,mCAAmC;;AAEpD;AACA,OAAO,qBAAqB;;AAE5B;AACA,OAAO,mCAAmC;;AAE1C;AACA,OAAO,iCAAiC;;AAExC;AACA,OAAO,+BAA+B;;AAEtC;AACA,OAAO,sCAAsC;AAC7C,OAAO,0CAA0C;AACjD;AACA,OAAO,2CAA2C;;AAElD;AACA,OAAOC,YAAY,MAAM,uBAAuB;;AAEhD;AACA,OAAOC,aAAa,MAAM,6BAA6B;;AAEvD;AACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EAC1C,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAC,MAAM;IAC9CC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC,CAAC,CAAC,CAACC,KAAK,CAACC,GAAG,IAAI;IACdH,OAAO,CAACI,IAAI,CAAC,WAAW,EAAED,GAAG,CAAC;EAChC,CAAC,CAAC;AACJ;;AAEA;AACA,MAAME,GAAG,GAAGlB,SAAS,CAACC,GAAG,CAAC;;AAE1B;AACAiB,GAAG,CAACC,MAAM,CAACC,YAAY,GAAG,CAACJ,GAAG,EAAEK,EAAE,EAAEC,IAAI,KAAK;EAC3C,IAAIN,GAAG,CAACO,IAAI,KAAK,gBAAgB,IAAKP,GAAG,CAACQ,OAAO,IAAIR,GAAG,CAACQ,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAE,EAAE;IAC3FC,cAAc,CAACC,KAAK,CAAC,CAAC;IACtBC,UAAU,CAAC,MAAM/B,MAAM,CAACgC,QAAQ,CAACC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;EAClD;AACF,CAAC;;AAED;AACA,KAAK,MAAM,CAACC,GAAG,EAAEC,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC7B,mBAAmB,CAAC,EAAE;EAClEa,GAAG,CAACc,SAAS,CAACD,GAAG,EAAEC,SAAS,CAAC;AAC/B;;AAEA;AACAd,GAAG,CAACiB,GAAG,CAAChC,KAAK,CAAC;AACde,GAAG,CAACiB,GAAG,CAACjC,MAAM,CAAC;AACfgB,GAAG,CAACiB,GAAG,CAAC/B,WAAW,EAAE;EACnBgC,MAAM,EAAE9B;AACV,CAAC,CAAC;;AAEF;AACAC,YAAY,CAAC8B,IAAI,CAAC,CAAC,CAACtB,KAAK,CAAC,MAAM;EAC9B;AAAA,CACD,CAAC;;AAEF;AACAa,UAAU,CAAC,MAAM;EACfpB,aAAa,CAAC8B,kBAAkB,CAAC,CAAC;AACpC,CAAC,EAAE,IAAI,CAAC;;AAER;AACA,IAAI7B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EAC1Cd,MAAM,CAACU,YAAY,GAAGA,YAAY;AACpC;;AAEA;AACAW,GAAG,CAACqB,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}