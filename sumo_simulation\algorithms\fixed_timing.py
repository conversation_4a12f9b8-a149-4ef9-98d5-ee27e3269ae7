#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
固定配时算法
传统的固定周期信号灯控制算法，作为对照组
"""

import time
import logging
from typing import Dict, Any

class FixedTimingController:
    """固定配时信号灯控制器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化固定配时控制器
        
        Args:
            config: 配置参数字典
        """
        self.config = config or {}
        
        # 默认配时参数
        self.cycle_time = self.config.get('cycle_time', 120)  # 总周期时间（秒）
        self.east_west_green = self.config.get('east_west_green', 45)  # 东西方向绿灯时间
        self.north_south_green = self.config.get('north_south_green', 45)  # 南北方向绿灯时间
        self.yellow_time = self.config.get('yellow_time', 3)  # 黄灯时间
        self.all_red_time = self.config.get('all_red_time', 2)  # 全红时间
        
        # 相位定义
        self.phases = [
            {
                'name': 'east_west_green',
                'duration': self.east_west_green,
                'state': 'GGGrrrGGGrrr',  # 东西绿灯，南北红灯
                'description': '东西方向绿灯'
            },
            {
                'name': 'east_west_yellow',
                'duration': self.yellow_time,
                'state': 'yyyrrryyyrrr',  # 东西黄灯，南北红灯
                'description': '东西方向黄灯'
            },
            {
                'name': 'all_red_1',
                'duration': self.all_red_time,
                'state': 'rrrrrrrrrrrr',  # 全红
                'description': '全红间隔1'
            },
            {
                'name': 'north_south_green',
                'duration': self.north_south_green,
                'state': 'rrrGGGrrrGGG',  # 南北绿灯，东西红灯
                'description': '南北方向绿灯'
            },
            {
                'name': 'north_south_yellow',
                'duration': self.yellow_time,
                'state': 'rrryyyrrryyy',  # 南北黄灯，东西红灯
                'description': '南北方向黄灯'
            },
            {
                'name': 'all_red_2',
                'duration': self.all_red_time,
                'state': 'rrrrrrrrrrrr',  # 全红
                'description': '全红间隔2'
            }
        ]
        
        # 当前相位索引
        self.current_phase_index = 0
        self.phase_start_time = 0
        
        # 日志记录
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"固定配时控制器初始化完成，周期时间: {self.cycle_time}秒")
        
    def get_current_phase(self, simulation_time: float) -> Dict[str, Any]:
        """
        获取当前相位信息
        
        Args:
            simulation_time: 当前仿真时间
            
        Returns:
            当前相位信息字典
        """
        # 计算在当前周期中的时间
        cycle_time_elapsed = simulation_time % self.cycle_time
        
        # 计算累计时间，找到当前相位
        cumulative_time = 0
        for i, phase in enumerate(self.phases):
            cumulative_time += phase['duration']
            if cycle_time_elapsed < cumulative_time:
                self.current_phase_index = i
                self.phase_start_time = cumulative_time - phase['duration']
                break
        
        current_phase = self.phases[self.current_phase_index]
        phase_elapsed = cycle_time_elapsed - self.phase_start_time
        phase_remaining = current_phase['duration'] - phase_elapsed
        
        return {
            'phase_index': self.current_phase_index,
            'phase_name': current_phase['name'],
            'phase_state': current_phase['state'],
            'phase_description': current_phase['description'],
            'phase_duration': current_phase['duration'],
            'phase_elapsed': phase_elapsed,
            'phase_remaining': phase_remaining,
            'cycle_time_elapsed': cycle_time_elapsed,
            'algorithm': 'fixed_timing'
        }
    
    def should_change_phase(self, simulation_time: float, traffic_data: Dict[str, Any] = None) -> bool:
        """
        判断是否应该切换相位（固定配时不需要交通数据）
        
        Args:
            simulation_time: 当前仿真时间
            traffic_data: 交通数据（固定配时算法不使用）
            
        Returns:
            是否应该切换相位
        """
        current_phase_info = self.get_current_phase(simulation_time)
        return current_phase_info['phase_remaining'] <= 0
    
    def get_next_phase_state(self, simulation_time: float, traffic_data: Dict[str, Any] = None) -> str:
        """
        获取下一个相位的信号灯状态
        
        Args:
            simulation_time: 当前仿真时间
            traffic_data: 交通数据（固定配时算法不使用）
            
        Returns:
            下一个相位的信号灯状态字符串
        """
        current_phase_info = self.get_current_phase(simulation_time)
        return current_phase_info['phase_state']
    
    def get_algorithm_info(self) -> Dict[str, Any]:
        """
        获取算法信息
        
        Returns:
            算法信息字典
        """
        return {
            'name': 'Fixed Timing Control',
            'description': '固定配时信号灯控制算法',
            'type': 'baseline',
            'cycle_time': self.cycle_time,
            'east_west_green': self.east_west_green,
            'north_south_green': self.north_south_green,
            'yellow_time': self.yellow_time,
            'all_red_time': self.all_red_time,
            'phases': self.phases
        }
    
    def get_performance_metrics(self, traffic_data: Dict[str, Any]) -> Dict[str, float]:
        """
        计算性能指标（固定配时的基础指标）
        
        Args:
            traffic_data: 交通数据
            
        Returns:
            性能指标字典
        """
        return {
            'algorithm_type': 'fixed_timing',
            'cycle_efficiency': 1.0,  # 固定配时的效率基准为1.0
            'adaptability': 0.0,      # 固定配时无适应性
            'response_time': 0.0,     # 固定配时无响应时间
            'optimization_level': 0.0  # 固定配时无优化
        }
    
    def reset(self):
        """重置控制器状态"""
        self.current_phase_index = 0
        self.phase_start_time = 0
        self.logger.info("固定配时控制器已重置")
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"FixedTimingController(cycle={self.cycle_time}s, EW={self.east_west_green}s, NS={self.north_south_green}s)"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()


def create_controller(config: Dict[str, Any] = None) -> FixedTimingController:
    """
    创建固定配时控制器的工厂函数
    
    Args:
        config: 配置参数
        
    Returns:
        固定配时控制器实例
    """
    return FixedTimingController(config)


# 预定义的配置方案
PRESET_CONFIGS = {
    'standard': {
        'cycle_time': 120,
        'east_west_green': 45,
        'north_south_green': 45,
        'yellow_time': 3,
        'all_red_time': 2
    },
    'rush_hour': {
        'cycle_time': 150,
        'east_west_green': 60,
        'north_south_green': 60,
        'yellow_time': 4,
        'all_red_time': 3
    },
    'off_peak': {
        'cycle_time': 90,
        'east_west_green': 35,
        'north_south_green': 35,
        'yellow_time': 3,
        'all_red_time': 2
    }
}


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建控制器
    controller = FixedTimingController()
    
    # 测试相位切换
    for t in range(0, 240, 10):
        phase_info = controller.get_current_phase(t)
        print(f"时间 {t}s: {phase_info['phase_description']} "
              f"(剩余 {phase_info['phase_remaining']:.1f}s)")
    
    print("\n算法信息:")
    print(controller.get_algorithm_info())
