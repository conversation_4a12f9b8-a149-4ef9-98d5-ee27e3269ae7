{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { Connection, Grid, Top, Bottom, ArrowLeft as Back, Right } from '@element-plus/icons-vue';\nimport RealTimeFrameViewer from '@/components/analysis/RealTimeFrameViewer.vue';\nimport stompService from '@/utils/stomp-service';\nexport default {\n  name: 'FourWayRealtimeViewer',\n  components: {\n    Connection,\n    Grid,\n    Top,\n    Bottom,\n    Back,\n    Right,\n    RealTimeFrameViewer\n  },\n  props: {\n    taskId: {\n      type: String,\n      required: true\n    },\n    autoStart: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['detection-update', 'status-change', 'analysis-complete'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const isConnected = ref(false);\n    const lastUpdateTime = ref('');\n    const frameSubscription = ref(null);\n    const showProgress = ref(true);\n\n    // 方向查看器引用\n    const northViewer = ref(null);\n    const southViewer = ref(null);\n    const eastViewer = ref(null);\n    const westViewer = ref(null);\n\n    // 方向帧数据状态\n    const directionFrameData = reactive({\n      north: null,\n      south: null,\n      east: null,\n      west: null\n    });\n\n    // 方向统计数据\n    const directionStats = reactive({\n      north: {\n        vehicleCount: 0,\n        frameRate: '0 fps',\n        status: 'waiting',\n        lastUpdate: null,\n        lastFrameTime: null,\n        currentFrame: 0,\n        totalFrames: 0,\n        recentVehicles: [],\n        // 最近N帧的车辆数，用于移动平均\n        maxRecentFrames: 10 // 保留最近10帧数据\n      },\n      south: {\n        vehicleCount: 0,\n        frameRate: '0 fps',\n        status: 'waiting',\n        lastUpdate: null,\n        lastFrameTime: null,\n        currentFrame: 0,\n        totalFrames: 0,\n        recentVehicles: [],\n        maxRecentFrames: 10\n      },\n      east: {\n        vehicleCount: 0,\n        frameRate: '0 fps',\n        status: 'waiting',\n        lastUpdate: null,\n        lastFrameTime: null,\n        currentFrame: 0,\n        totalFrames: 0,\n        recentVehicles: [],\n        maxRecentFrames: 10\n      },\n      west: {\n        vehicleCount: 0,\n        frameRate: '0 fps',\n        status: 'waiting',\n        lastUpdate: null,\n        lastFrameTime: null,\n        currentFrame: 0,\n        totalFrames: 0,\n        recentVehicles: [],\n        maxRecentFrames: 10\n      }\n    });\n\n    // 计算属性\n    const connectionStatusType = computed(() => {\n      return isConnected.value ? 'success' : 'danger';\n    });\n    const connectionStatusText = computed(() => {\n      return isConnected.value ? '已连接' : '未连接';\n    });\n    const totalVehicleCount = computed(() => {\n      return Object.values(directionStats).reduce((total, stats) => total + stats.vehicleCount, 0);\n    });\n    const overallProgress = computed(() => {\n      const directions = Object.values(directionStats);\n      let totalProgress = 0;\n      let activeDirections = 0;\n      directions.forEach(direction => {\n        if (direction.status !== 'waiting') {\n          activeDirections++;\n          // 基于帧数计算进度\n          if (direction.totalFrames > 0) {\n            const frameProgress = Math.min((direction.currentFrame || 0) / direction.totalFrames * 100, 100);\n            totalProgress += frameProgress;\n          } else if (direction.status === 'completed') {\n            totalProgress += 100;\n          }\n        }\n      });\n      if (activeDirections === 0) return 0;\n      return Math.round(totalProgress / 4); // 总是除以4个方向\n    });\n    const progressStatus = computed(() => {\n      if (overallProgress.value === 100) return 'success';\n      if (overallProgress.value > 0) return '';\n      return 'exception';\n    });\n\n    // 方法\n    const getDirectionStatusType = direction => {\n      const status = directionStats[direction].status;\n      const typeMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'error': 'danger'\n      };\n      return typeMap[status] || 'info';\n    };\n    const getDirectionStatusText = direction => {\n      const status = directionStats[direction].status;\n      const textMap = {\n        'waiting': '等待中',\n        'processing': '检测中',\n        'completed': '已完成',\n        'error': '检测失败'\n      };\n\n      // 如果是等待状态，检查是否为长视频处理\n      if (status === 'waiting') {\n        const processingTime = getProcessingTime();\n        if (processingTime > 30) {\n          return '长视频处理中...';\n        } else if (processingTime > 10) {\n          return '正在处理...';\n        }\n      }\n      return textMap[status] || '未知';\n    };\n    const formatImageData = imageData => {\n      if (!imageData) {\n        console.warn('🖼️ 图像数据为空');\n        return null;\n      }\n\n      // 如果已经是完整的 data URL，直接返回\n      if (imageData.startsWith('data:image/')) {\n        return imageData;\n      }\n\n      // 如果是 Base64 字符串，添加 data URL 前缀\n      if (typeof imageData === 'string' && imageData.length > 0) {\n        const formattedData = `data:image/jpeg;base64,${imageData}`;\n        console.log('🖼️ 格式化图像数据:', {\n          originalLength: imageData.length,\n          formattedLength: formattedData.length,\n          prefix: formattedData.substring(0, 50) + '...'\n        });\n        return formattedData;\n      }\n      console.warn('🖼️ 无效的图像数据格式:', typeof imageData, imageData ? imageData.substring(0, 50) : 'null');\n      return null;\n    };\n    const handleFrameReceived = (direction, frameData) => {\n      try {\n        console.log(`🎬 处理${direction}方向帧数据:`, {\n          frameNumber: frameData.frameNumber,\n          detectionCount: frameData.detectionCount,\n          hasImageData: !!frameData.imageData,\n          imageDataLength: frameData.imageData ? frameData.imageData.length : 0\n        });\n\n        // 格式化图像数据\n        const formattedImageData = formatImageData(frameData.imageData);\n        if (!formattedImageData) {\n          console.error(`❌ ${direction}方向图像数据格式化失败`);\n          return;\n        }\n\n        // 创建格式化的帧数据\n        const formattedFrameData = {\n          ...frameData,\n          imageData: formattedImageData,\n          direction: direction,\n          timestamp: new Date().toISOString()\n        };\n\n        // 更新方向帧数据\n        directionFrameData[direction] = formattedFrameData;\n        console.log(`✅ ${direction}方向帧数据已更新`);\n\n        // 更新帧数信息\n        if (frameData.frameNumber !== undefined) {\n          directionStats[direction].currentFrame = frameData.frameNumber;\n        }\n        if (frameData.totalFrames !== undefined) {\n          directionStats[direction].totalFrames = frameData.totalFrames;\n        }\n\n        // 更新方向统计 - 使用当前帧的车辆数而不是累加\n        const currentVehicleCount = frameData.detectionCount || 0;\n\n        // 更新移动平均数据\n        const recentVehicles = directionStats[direction].recentVehicles;\n        recentVehicles.push(currentVehicleCount);\n\n        // 保持最近N帧的数据\n        if (recentVehicles.length > directionStats[direction].maxRecentFrames) {\n          recentVehicles.shift();\n        }\n\n        // 计算移动平均\n        const movingAverage = recentVehicles.length > 0 ? Math.round(recentVehicles.reduce((sum, count) => sum + count, 0) / recentVehicles.length * 10) / 10 : 0;\n\n        // 更新车辆计数为当前帧的检测数量\n        directionStats[direction].vehicleCount = currentVehicleCount;\n        directionStats[direction].movingAverage = movingAverage;\n        directionStats[direction].status = 'processing';\n        directionStats[direction].lastUpdate = new Date();\n\n        // 更新最后更新时间\n        lastUpdateTime.value = new Date().toLocaleTimeString();\n\n        // 计算帧率（简化版本）\n        const now = Date.now();\n        if (directionStats[direction].lastFrameTime) {\n          const interval = now - directionStats[direction].lastFrameTime;\n          const fps = Math.round(1000 / interval * 10) / 10;\n          directionStats[direction].frameRate = `${fps} fps`;\n        }\n        directionStats[direction].lastFrameTime = now;\n\n        // 发出检测更新事件\n        emit('detection-update', {\n          direction,\n          frameData: formattedFrameData,\n          directionStats: directionStats[direction],\n          globalStats: {\n            totalVehicles: totalVehicleCount.value,\n            peakDirection: getPeakDirection(),\n            averageSpeed: getAverageFrameRate(),\n            processingTime: getProcessingTime()\n          }\n        });\n      } catch (error) {\n        console.error(`❌ 处理${direction}方向帧数据失败:`, error);\n        directionStats[direction].status = 'error';\n      }\n    };\n    const getPeakDirection = () => {\n      let maxCount = 0;\n      let peakDir = '-';\n      Object.entries(directionStats).forEach(([dir, stats]) => {\n        if (stats.vehicleCount > maxCount) {\n          maxCount = stats.vehicleCount;\n          peakDir = getDirectionName(dir);\n        }\n      });\n      return peakDir;\n    };\n    const getDirectionName = direction => {\n      const names = {\n        north: '北向',\n        south: '南向',\n        east: '东向',\n        west: '西向'\n      };\n      return names[direction] || direction;\n    };\n    const getAverageFrameRate = () => {\n      const rates = Object.values(directionStats).map(stats => parseFloat(stats.frameRate)).filter(rate => !isNaN(rate));\n      if (rates.length === 0) return 0;\n      const average = rates.reduce((sum, rate) => sum + rate, 0) / rates.length;\n      return Math.round(average * 10) / 10;\n    };\n\n    // 新增的智能分析方法\n    const getRecentIncrease = () => {\n      // 模拟最近增长数据\n      return Math.floor(Math.random() * 5) + 1;\n    };\n    const getPeakDirectionPercentage = () => {\n      const total = totalVehicleCount.value;\n      if (total === 0) return 0;\n      const maxCount = Math.max(...Object.values(directionStats).map(s => s.vehicleCount));\n      return Math.round(maxCount / total * 100);\n    };\n    const getEfficiencyLevel = () => {\n      const avgRate = getAverageFrameRate();\n      if (avgRate >= 25) return '高效';\n      if (avgRate >= 15) return '正常';\n      if (avgRate >= 10) return '较慢';\n      return '低效';\n    };\n    const getCongestionLevel = () => {\n      const total = totalVehicleCount.value;\n      if (total >= 50) return '严重拥堵';\n      if (total >= 30) return '中度拥堵';\n      if (total >= 15) return '轻度拥堵';\n      return '畅通';\n    };\n    const getCongestionTrend = () => {\n      const level = getCongestionLevel();\n      if (level === '严重拥堵') return '↗️ 恶化';\n      if (level === '中度拥堵') return '→ 稳定';\n      if (level === '轻度拥堵') return '↘️ 改善';\n      return '✅ 良好';\n    };\n    const getTrafficFlowBalance = () => {\n      const counts = Object.values(directionStats).map(s => s.vehicleCount);\n      const max = Math.max(...counts);\n      const min = Math.min(...counts);\n      if (max === 0) return 100;\n      const balance = (max - min) / max * 100;\n      return Math.round(100 - balance);\n    };\n    const getBalanceTrend = () => {\n      const balance = getTrafficFlowBalance();\n      if (balance >= 80) return '均衡';\n      if (balance >= 60) return '较均衡';\n      if (balance >= 40) return '不均衡';\n      return '严重不均衡';\n    };\n    const getProcessingTime = () => {\n      const startTimes = Object.values(directionStats).map(stats => stats.lastUpdate).filter(time => time);\n      if (startTimes.length === 0) return 0;\n      const earliest = Math.min(...startTimes.map(time => time.getTime()));\n      return Math.round((Date.now() - earliest) / 1000);\n    };\n\n    // 处理四方向整体分析完成\n    const handleAnalysisComplete = completeData => {\n      try {\n        console.log('🎉 四方向分析完成，准备跳转到智能分析模块');\n\n        // 更新所有方向状态为完成\n        Object.keys(directionStats).forEach(direction => {\n          directionStats[direction].status = 'completed';\n        });\n\n        // 显示完成提示\n        ElMessage({\n          message: '所有四个方向的视频分析已完成，即将跳转到智能分析模块',\n          type: 'success',\n          duration: 3000\n        });\n\n        // 发出分析完成事件给父组件\n        emit('analysis-complete', {\n          taskId: props.taskId,\n          summary: completeData.summary,\n          message: completeData.message,\n          timestamp: completeData.timestamp\n        });\n      } catch (error) {\n        console.error('处理分析完成消息失败:', error);\n        ElMessage.error('处理完成消息失败: ' + error.message);\n      }\n    };\n    const initializeWebSocketConnection = async () => {\n      try {\n        console.log(`初始化四方向WebSocket连接: ${props.taskId}`);\n\n        // 订阅四方向帧数据、进度和完成消息\n        frameSubscription.value = await stompService.subscribeFourWayFrameUpdates(props.taskId,\n        // 帧数据回调\n        frameData => {\n          const direction = frameData.direction;\n          if (direction && directionStats[direction] !== undefined) {\n            handleFrameReceived(direction, frameData);\n          }\n        },\n        // 进度数据回调\n        progressData => {\n          console.log('收到四方向进度数据:', progressData);\n          // 处理进度更新\n          if (progressData.direction && directionStats[progressData.direction]) {\n            const direction = progressData.direction;\n            const dirStats = directionStats[direction];\n\n            // 更新状态\n            if (progressData.status) {\n              dirStats.status = progressData.status;\n            }\n\n            // 更新帧数信息\n            if (progressData.currentFrame !== undefined) {\n              dirStats.currentFrame = progressData.currentFrame;\n            }\n            if (progressData.totalFrames !== undefined) {\n              dirStats.totalFrames = progressData.totalFrames;\n            }\n\n            // 更新进度百分比\n            if (progressData.progress !== undefined) {\n              dirStats.progress = progressData.progress;\n            }\n            console.log(`✓ 更新${direction}方向进度:`, {\n              status: dirStats.status,\n              currentFrame: dirStats.currentFrame,\n              totalFrames: dirStats.totalFrames,\n              progress: dirStats.progress\n            });\n          }\n        },\n        // 整体完成回调\n        completeData => {\n          console.log('✓ 收到四方向整体分析完成消息:', completeData);\n          handleAnalysisComplete(completeData);\n        });\n        isConnected.value = true;\n        ElMessage.success('四方向实时检测连接成功');\n      } catch (error) {\n        console.error('WebSocket连接失败:', error);\n        ElMessage.error('连接失败: ' + error.message);\n        isConnected.value = false;\n      }\n    };\n    const cleanup = () => {\n      // 清理WebSocket订阅\n      if (frameSubscription.value) {\n        stompService.unsubscribe(frameSubscription.value);\n        frameSubscription.value = null;\n      }\n\n      // 清理各方向的帧缓冲\n      const directions = ['north', 'south', 'east', 'west'];\n      directions.forEach(direction => {\n        const taskId = getDirectionTaskId(direction);\n        stompService.clearFrameBuffer(taskId);\n      });\n      isConnected.value = false;\n    };\n\n    // 监听taskId变化\n    watch(() => props.taskId, newTaskId => {\n      if (newTaskId) {\n        cleanup();\n        initializeWebSocketConnection();\n      }\n    });\n\n    // 生命周期\n    onMounted(() => {\n      if (props.taskId && props.autoStart) {\n        initializeWebSocketConnection();\n      }\n    });\n    onUnmounted(() => {\n      cleanup();\n    });\n\n    // 暴露方法给父组件\n    const startDetection = () => {\n      initializeWebSocketConnection();\n    };\n    const stopDetection = () => {\n      cleanup();\n    };\n    return {\n      // 响应式数据\n      isConnected,\n      lastUpdateTime,\n      showProgress,\n      northViewer,\n      southViewer,\n      eastViewer,\n      westViewer,\n      directionStats,\n      directionFrameData,\n      // 计算属性\n      connectionStatusType,\n      connectionStatusText,\n      totalVehicleCount,\n      overallProgress,\n      progressStatus,\n      // 方法\n      getDirectionStatusType,\n      getDirectionStatusText,\n      handleFrameReceived,\n      handleAnalysisComplete,\n      getPeakDirection,\n      getDirectionName,\n      getAverageFrameRate,\n      getProcessingTime,\n      getRecentIncrease,\n      getPeakDirectionPercentage,\n      getEfficiencyLevel,\n      getCongestionLevel,\n      getCongestionTrend,\n      getTrafficFlowBalance,\n      getBalanceTrend,\n      cleanup,\n      startDetection,\n      stopDetection\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "onUnmounted", "watch", "ElMessage", "Connection", "Grid", "Top", "Bottom", "ArrowLeft", "Back", "Right", "RealTimeFrameViewer", "stompService", "name", "components", "props", "taskId", "type", "String", "required", "autoStart", "Boolean", "default", "emits", "setup", "emit", "isConnected", "lastUpdateTime", "frameSubscription", "showProgress", "northViewer", "southViewer", "eastViewer", "westViewer", "directionFrameData", "north", "south", "east", "west", "directionStats", "vehicleCount", "frameRate", "status", "lastUpdate", "lastFrameTime", "currentFrame", "totalFrames", "recentVehicles", "maxRecent<PERSON>ram<PERSON>", "connectionStatusType", "value", "connectionStatusText", "totalVehicleCount", "Object", "values", "reduce", "total", "stats", "overallProgress", "directions", "totalProgress", "activeDirections", "for<PERSON>ach", "direction", "frameProgress", "Math", "min", "round", "progressStatus", "getDirectionStatusType", "typeMap", "getDirectionStatusText", "textMap", "processingTime", "getProcessingTime", "formatImageData", "imageData", "console", "warn", "startsWith", "length", "formattedData", "log", "original<PERSON>ength", "formattedLength", "prefix", "substring", "handleFrameReceived", "frameData", "frameNumber", "detectionCount", "hasImageData", "imageDataLength", "formattedImageData", "error", "formattedFrameData", "timestamp", "Date", "toISOString", "undefined", "currentVehicleCount", "push", "shift", "movingAverage", "sum", "count", "toLocaleTimeString", "now", "interval", "fps", "globalStats", "totalVehicles", "peakDirection", "getPeakDirection", "averageSpeed", "getAverageFrameRate", "maxCount", "peakDir", "entries", "dir", "getDirectionName", "names", "rates", "map", "parseFloat", "filter", "rate", "isNaN", "average", "getRecentIncrease", "floor", "random", "getPeakDirectionPercentage", "max", "s", "getEfficiencyLevel", "avgRate", "getCongestionLevel", "getCongestionTrend", "level", "getTrafficFlowBalance", "counts", "balance", "getBalanceTrend", "startTimes", "time", "earliest", "getTime", "handleAnalysisComplete", "completeData", "keys", "message", "duration", "summary", "initializeWebSocketConnection", "subscribeFourWayFrameUpdates", "progressData", "dirStats", "progress", "success", "cleanup", "unsubscribe", "getDirectionTaskId", "clear<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newTaskId", "startDetection", "stopDetection"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\analysis\\FourWayRealtimeViewer.vue"], "sourcesContent": ["<template>\n  <div class=\"four-way-realtime-viewer\">\n    <!-- 连接状态指示器 -->\n    <div class=\"connection-status\">\n      <el-tag :type=\"connectionStatusType\" size=\"small\">\n        <el-icon><Connection /></el-icon>\n        {{ connectionStatusText }}\n      </el-tag>\n      <span class=\"last-update\">\n        最后更新: {{ lastUpdateTime || '未连接' }}\n      </span>\n    </div>\n\n    <!-- 四方向检测网格 - 2x2布局 -->\n    <div class=\"detection-grid\">\n      <!-- 上排：北向和东向 -->\n      <!-- 北向检测 - 左上 -->\n      <div class=\"detection-item north\">\n        <div class=\"direction-header\">\n          <el-icon><Top /></el-icon>\n          <span class=\"direction-label\">北向 (North)</span>\n          <el-tag :type=\"getDirectionStatusType('north')\" size=\"small\">\n            {{ getDirectionStatusText('north') }}\n          </el-tag>\n        </div>\n        <div class=\"detection-content\">\n          <RealTimeFrameViewer\n            ref=\"northViewer\"\n            direction=\"north\"\n            :frame-data=\"directionFrameData.north\"\n            :status=\"directionStats.north.status\"\n            :progress=\"0\"\n            :show-controls=\"false\"\n            @pause-toggled=\"(data) => console.log('North pause toggled:', data)\"\n            @frame-saved=\"(data) => console.log('North frame saved:', data)\"\n            class=\"direction-viewer\"\n          />\n          <div class=\"direction-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.north.vehicleCount }}</span>\n              <span class=\"stat-label\">车辆数</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.north.frameRate }}</span>\n              <span class=\"stat-label\">帧率</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 下排：西向和南向 -->\n      <!-- 西向检测 - 左下 -->\n      <div class=\"detection-item west\">\n        <div class=\"direction-header\">\n          <el-icon><Back /></el-icon>\n          <span class=\"direction-label\">西向 (West)</span>\n          <el-tag :type=\"getDirectionStatusType('west')\" size=\"small\">\n            {{ getDirectionStatusText('west') }}\n          </el-tag>\n        </div>\n        <div class=\"detection-content\">\n          <RealTimeFrameViewer\n            ref=\"westViewer\"\n            direction=\"west\"\n            :frame-data=\"directionFrameData.west\"\n            :status=\"directionStats.west.status\"\n            :progress=\"0\"\n            :show-controls=\"false\"\n            @pause-toggled=\"(data) => console.log('West pause toggled:', data)\"\n            @frame-saved=\"(data) => console.log('West frame saved:', data)\"\n            class=\"direction-viewer\"\n          />\n          <div class=\"direction-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.west.vehicleCount }}</span>\n              <span class=\"stat-label\">车辆数</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.west.frameRate }}</span>\n              <span class=\"stat-label\">帧率</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n\n\n      <!-- 东向检测 - 右上 -->\n      <div class=\"detection-item east\">\n        <div class=\"direction-header\">\n          <el-icon><Right /></el-icon>\n          <span class=\"direction-label\">东向 (East)</span>\n          <el-tag :type=\"getDirectionStatusType('east')\" size=\"small\">\n            {{ getDirectionStatusText('east') }}\n          </el-tag>\n        </div>\n        <div class=\"detection-content\">\n          <RealTimeFrameViewer\n            ref=\"eastViewer\"\n            direction=\"east\"\n            :frame-data=\"directionFrameData.east\"\n            :status=\"directionStats.east.status\"\n            :progress=\"0\"\n            :show-controls=\"false\"\n            @pause-toggled=\"(data) => console.log('East pause toggled:', data)\"\n            @frame-saved=\"(data) => console.log('East frame saved:', data)\"\n            class=\"direction-viewer\"\n          />\n          <div class=\"direction-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.east.vehicleCount }}</span>\n              <span class=\"stat-label\">车辆数</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.east.frameRate }}</span>\n              <span class=\"stat-label\">帧率</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 南向检测 - 右下 -->\n      <div class=\"detection-item south\">\n        <div class=\"direction-header\">\n          <el-icon><Bottom /></el-icon>\n          <span class=\"direction-label\">南向 (South)</span>\n          <el-tag :type=\"getDirectionStatusType('south')\" size=\"small\">\n            {{ getDirectionStatusText('south') }}\n          </el-tag>\n        </div>\n        <div class=\"detection-content\">\n          <RealTimeFrameViewer\n            ref=\"southViewer\"\n            direction=\"south\"\n            :frame-data=\"directionFrameData.south\"\n            :status=\"directionStats.south.status\"\n            :progress=\"0\"\n            :show-controls=\"false\"\n            @pause-toggled=\"(data) => console.log('South pause toggled:', data)\"\n            @frame-saved=\"(data) => console.log('South frame saved:', data)\"\n            class=\"direction-viewer\"\n          />\n          <div class=\"direction-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.south.vehicleCount }}</span>\n              <span class=\"stat-label\">车辆数</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.south.frameRate }}</span>\n              <span class=\"stat-label\">帧率</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 总览统计 -->\n    <div class=\"overview-stats\">\n      <div class=\"total-vehicles-summary\">\n        <el-icon><Grid /></el-icon>\n        <span class=\"total-count\">{{ totalVehicleCount }}</span>\n        <span class=\"total-label\">总车辆数</span>\n      </div>\n      <div class=\"peak-direction-info\">\n        <span class=\"peak-text\">高峰方向: {{ getPeakDirection() }}</span>\n      </div>\n    </div>\n\n    <!-- 检测进度条 -->\n    <div v-if=\"showProgress\" class=\"detection-progress\">\n      <el-progress\n        :percentage=\"overallProgress\"\n        :status=\"progressStatus\"\n        :stroke-width=\"8\"\n        :show-text=\"true\"\n      >\n        <template #default=\"{ percentage }\">\n          <span class=\"progress-text\">{{ percentage }}% 检测进度</span>\n        </template>\n      </el-progress>\n    </div>\n\n    <!-- 智能交通状态面板 -->\n    <IntelligentTrafficStatusPanel\n      :direction-stats=\"directionStats\"\n      :last-update-time=\"lastUpdateTime\"\n      @apply-suggestion=\"handleApplySuggestion\"\n    />\n\n    <!-- 增强的全局统计信息 -->\n    <div class=\"enhanced-global-stats\">\n      <div class=\"stats-header\">\n        <h3>实时交通分析</h3>\n        <div class=\"connection-status-info\">\n          <el-icon :class=\"{ 'connected': isConnected, 'disconnected': !isConnected }\">\n            <Connection />\n          </el-icon>\n          <span>{{ isConnected ? '已连接' : '未连接' }}</span>\n          <el-tag v-if=\"lastUpdateTime\" size=\"small\" type=\"info\">\n            {{ lastUpdateTime }}\n          </el-tag>\n        </div>\n      </div>\n\n      <div class=\"stats-grid\">\n        <div class=\"stat-card primary\">\n          <div class=\"stat-icon\">🚗</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ totalVehicleCount }}</div>\n            <div class=\"stat-label\">总车辆数</div>\n            <div class=\"stat-trend positive\">\n              +{{ getRecentIncrease() }}\n            </div>\n          </div>\n        </div>\n\n        <div class=\"stat-card success\">\n          <div class=\"stat-icon\">📍</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getPeakDirection() }}</div>\n            <div class=\"stat-label\">最繁忙方向</div>\n            <div class=\"stat-trend\">\n              {{ getPeakDirectionPercentage() }}%\n            </div>\n          </div>\n        </div>\n\n        <div class=\"stat-card warning\">\n          <div class=\"stat-icon\">⚡</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getAverageFrameRate() }}</div>\n            <div class=\"stat-label\">平均帧率</div>\n            <div class=\"stat-trend\">实时</div>\n          </div>\n        </div>\n\n        <div class=\"stat-card info\">\n          <div class=\"stat-icon\">⏱️</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getProcessingTime() }}s</div>\n            <div class=\"stat-label\">处理时间</div>\n            <div class=\"stat-trend\">{{ getEfficiencyLevel() }}</div>\n          </div>\n        </div>\n\n        <div class=\"stat-card danger\">\n          <div class=\"stat-icon\">🚨</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getCongestionLevel() }}</div>\n            <div class=\"stat-label\">拥堵等级</div>\n            <div class=\"stat-trend\">{{ getCongestionTrend() }}</div>\n          </div>\n        </div>\n\n        <div class=\"stat-card purple\">\n          <div class=\"stat-icon\">📊</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getTrafficFlowBalance() }}%</div>\n            <div class=\"stat-label\">流量平衡度</div>\n            <div class=\"stat-trend\">{{ getBalanceTrend() }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport {\n  Connection, Grid, Top, Bottom, ArrowLeft as Back, Right\n} from '@element-plus/icons-vue'\nimport RealTimeFrameViewer from '@/components/analysis/RealTimeFrameViewer.vue'\nimport stompService from '@/utils/stomp-service'\n\nexport default {\n  name: 'FourWayRealtimeViewer',\n  components: {\n    Connection, Grid, Top, Bottom, Back, Right,\n    RealTimeFrameViewer\n  },\n  props: {\n    taskId: {\n      type: String,\n      required: true\n    },\n    autoStart: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['detection-update', 'status-change', 'analysis-complete'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isConnected = ref(false)\n    const lastUpdateTime = ref('')\n    const frameSubscription = ref(null)\n    const showProgress = ref(true)\n    \n    // 方向查看器引用\n    const northViewer = ref(null)\n    const southViewer = ref(null)\n    const eastViewer = ref(null)\n    const westViewer = ref(null)\n\n    // 方向帧数据状态\n    const directionFrameData = reactive({\n      north: null,\n      south: null,\n      east: null,\n      west: null\n    })\n\n    // 方向统计数据\n    const directionStats = reactive({\n      north: {\n        vehicleCount: 0,\n        frameRate: '0 fps',\n        status: 'waiting',\n        lastUpdate: null,\n        lastFrameTime: null,\n        currentFrame: 0,\n        totalFrames: 0,\n        recentVehicles: [], // 最近N帧的车辆数，用于移动平均\n        maxRecentFrames: 10 // 保留最近10帧数据\n      },\n      south: {\n        vehicleCount: 0,\n        frameRate: '0 fps',\n        status: 'waiting',\n        lastUpdate: null,\n        lastFrameTime: null,\n        currentFrame: 0,\n        totalFrames: 0,\n        recentVehicles: [],\n        maxRecentFrames: 10\n      },\n      east: {\n        vehicleCount: 0,\n        frameRate: '0 fps',\n        status: 'waiting',\n        lastUpdate: null,\n        lastFrameTime: null,\n        currentFrame: 0,\n        totalFrames: 0,\n        recentVehicles: [],\n        maxRecentFrames: 10\n      },\n      west: {\n        vehicleCount: 0,\n        frameRate: '0 fps',\n        status: 'waiting',\n        lastUpdate: null,\n        lastFrameTime: null,\n        currentFrame: 0,\n        totalFrames: 0,\n        recentVehicles: [],\n        maxRecentFrames: 10\n      }\n    })\n    \n    // 计算属性\n    const connectionStatusType = computed(() => {\n      return isConnected.value ? 'success' : 'danger'\n    })\n    \n    const connectionStatusText = computed(() => {\n      return isConnected.value ? '已连接' : '未连接'\n    })\n    \n    const totalVehicleCount = computed(() => {\n      return Object.values(directionStats).reduce((total, stats) => total + stats.vehicleCount, 0)\n    })\n    \n    const overallProgress = computed(() => {\n      const directions = Object.values(directionStats)\n      let totalProgress = 0\n      let activeDirections = 0\n\n      directions.forEach(direction => {\n        if (direction.status !== 'waiting') {\n          activeDirections++\n          // 基于帧数计算进度\n          if (direction.totalFrames > 0) {\n            const frameProgress = Math.min((direction.currentFrame || 0) / direction.totalFrames * 100, 100)\n            totalProgress += frameProgress\n          } else if (direction.status === 'completed') {\n            totalProgress += 100\n          }\n        }\n      })\n\n      if (activeDirections === 0) return 0\n      return Math.round(totalProgress / 4) // 总是除以4个方向\n    })\n    \n    const progressStatus = computed(() => {\n      if (overallProgress.value === 100) return 'success'\n      if (overallProgress.value > 0) return ''\n      return 'exception'\n    })\n    \n    // 方法\n    const getDirectionStatusType = (direction) => {\n      const status = directionStats[direction].status\n      const typeMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'error': 'danger'\n      }\n      return typeMap[status] || 'info'\n    }\n\n    const getDirectionStatusText = (direction) => {\n      const status = directionStats[direction].status\n      const textMap = {\n        'waiting': '等待中',\n        'processing': '检测中',\n        'completed': '已完成',\n        'error': '检测失败'\n      }\n\n      // 如果是等待状态，检查是否为长视频处理\n      if (status === 'waiting') {\n        const processingTime = getProcessingTime()\n        if (processingTime > 30) {\n          return '长视频处理中...'\n        } else if (processingTime > 10) {\n          return '正在处理...'\n        }\n      }\n\n      return textMap[status] || '未知'\n    }\n    \n    const formatImageData = (imageData) => {\n      if (!imageData) {\n        console.warn('🖼️ 图像数据为空')\n        return null\n      }\n\n      // 如果已经是完整的 data URL，直接返回\n      if (imageData.startsWith('data:image/')) {\n        return imageData\n      }\n\n      // 如果是 Base64 字符串，添加 data URL 前缀\n      if (typeof imageData === 'string' && imageData.length > 0) {\n        const formattedData = `data:image/jpeg;base64,${imageData}`\n        console.log('🖼️ 格式化图像数据:', {\n          originalLength: imageData.length,\n          formattedLength: formattedData.length,\n          prefix: formattedData.substring(0, 50) + '...'\n        })\n        return formattedData\n      }\n\n      console.warn('🖼️ 无效的图像数据格式:', typeof imageData, imageData ? imageData.substring(0, 50) : 'null')\n      return null\n    }\n\n    const handleFrameReceived = (direction, frameData) => {\n      try {\n        console.log(`🎬 处理${direction}方向帧数据:`, {\n          frameNumber: frameData.frameNumber,\n          detectionCount: frameData.detectionCount,\n          hasImageData: !!frameData.imageData,\n          imageDataLength: frameData.imageData ? frameData.imageData.length : 0\n        })\n\n        // 格式化图像数据\n        const formattedImageData = formatImageData(frameData.imageData)\n        if (!formattedImageData) {\n          console.error(`❌ ${direction}方向图像数据格式化失败`)\n          return\n        }\n\n        // 创建格式化的帧数据\n        const formattedFrameData = {\n          ...frameData,\n          imageData: formattedImageData,\n          direction: direction,\n          timestamp: new Date().toISOString()\n        }\n\n        // 更新方向帧数据\n        directionFrameData[direction] = formattedFrameData\n        console.log(`✅ ${direction}方向帧数据已更新`)\n\n        // 更新帧数信息\n        if (frameData.frameNumber !== undefined) {\n          directionStats[direction].currentFrame = frameData.frameNumber\n        }\n        if (frameData.totalFrames !== undefined) {\n          directionStats[direction].totalFrames = frameData.totalFrames\n        }\n\n        // 更新方向统计 - 使用当前帧的车辆数而不是累加\n        const currentVehicleCount = frameData.detectionCount || 0\n\n        // 更新移动平均数据\n        const recentVehicles = directionStats[direction].recentVehicles\n        recentVehicles.push(currentVehicleCount)\n\n        // 保持最近N帧的数据\n        if (recentVehicles.length > directionStats[direction].maxRecentFrames) {\n          recentVehicles.shift()\n        }\n\n        // 计算移动平均\n        const movingAverage = recentVehicles.length > 0\n          ? Math.round(recentVehicles.reduce((sum, count) => sum + count, 0) / recentVehicles.length * 10) / 10\n          : 0\n\n        // 更新车辆计数为当前帧的检测数量\n        directionStats[direction].vehicleCount = currentVehicleCount\n        directionStats[direction].movingAverage = movingAverage\n        directionStats[direction].status = 'processing'\n        directionStats[direction].lastUpdate = new Date()\n\n        // 更新最后更新时间\n        lastUpdateTime.value = new Date().toLocaleTimeString()\n\n        // 计算帧率（简化版本）\n        const now = Date.now()\n        if (directionStats[direction].lastFrameTime) {\n          const interval = now - directionStats[direction].lastFrameTime\n          const fps = Math.round(1000 / interval * 10) / 10\n          directionStats[direction].frameRate = `${fps} fps`\n        }\n        directionStats[direction].lastFrameTime = now\n\n        // 发出检测更新事件\n        emit('detection-update', {\n          direction,\n          frameData: formattedFrameData,\n          directionStats: directionStats[direction],\n          globalStats: {\n            totalVehicles: totalVehicleCount.value,\n            peakDirection: getPeakDirection(),\n            averageSpeed: getAverageFrameRate(),\n            processingTime: getProcessingTime()\n          }\n        })\n\n      } catch (error) {\n        console.error(`❌ 处理${direction}方向帧数据失败:`, error)\n        directionStats[direction].status = 'error'\n      }\n    }\n    \n    const getPeakDirection = () => {\n      let maxCount = 0\n      let peakDir = '-'\n      \n      Object.entries(directionStats).forEach(([dir, stats]) => {\n        if (stats.vehicleCount > maxCount) {\n          maxCount = stats.vehicleCount\n          peakDir = getDirectionName(dir)\n        }\n      })\n      \n      return peakDir\n    }\n    \n    const getDirectionName = (direction) => {\n      const names = {\n        north: '北向',\n        south: '南向',\n        east: '东向',\n        west: '西向'\n      }\n      return names[direction] || direction\n    }\n    \n    const getAverageFrameRate = () => {\n      const rates = Object.values(directionStats)\n        .map(stats => parseFloat(stats.frameRate))\n        .filter(rate => !isNaN(rate))\n\n      if (rates.length === 0) return 0\n\n      const average = rates.reduce((sum, rate) => sum + rate, 0) / rates.length\n      return Math.round(average * 10) / 10\n    }\n\n    // 新增的智能分析方法\n    const getRecentIncrease = () => {\n      // 模拟最近增长数据\n      return Math.floor(Math.random() * 5) + 1\n    }\n\n    const getPeakDirectionPercentage = () => {\n      const total = totalVehicleCount.value\n      if (total === 0) return 0\n\n      const maxCount = Math.max(...Object.values(directionStats).map(s => s.vehicleCount))\n      return Math.round((maxCount / total) * 100)\n    }\n\n    const getEfficiencyLevel = () => {\n      const avgRate = getAverageFrameRate()\n      if (avgRate >= 25) return '高效'\n      if (avgRate >= 15) return '正常'\n      if (avgRate >= 10) return '较慢'\n      return '低效'\n    }\n\n    const getCongestionLevel = () => {\n      const total = totalVehicleCount.value\n      if (total >= 50) return '严重拥堵'\n      if (total >= 30) return '中度拥堵'\n      if (total >= 15) return '轻度拥堵'\n      return '畅通'\n    }\n\n    const getCongestionTrend = () => {\n      const level = getCongestionLevel()\n      if (level === '严重拥堵') return '↗️ 恶化'\n      if (level === '中度拥堵') return '→ 稳定'\n      if (level === '轻度拥堵') return '↘️ 改善'\n      return '✅ 良好'\n    }\n\n    const getTrafficFlowBalance = () => {\n      const counts = Object.values(directionStats).map(s => s.vehicleCount)\n      const max = Math.max(...counts)\n      const min = Math.min(...counts)\n\n      if (max === 0) return 100\n\n      const balance = ((max - min) / max) * 100\n      return Math.round(100 - balance)\n    }\n\n    const getBalanceTrend = () => {\n      const balance = getTrafficFlowBalance()\n      if (balance >= 80) return '均衡'\n      if (balance >= 60) return '较均衡'\n      if (balance >= 40) return '不均衡'\n      return '严重不均衡'\n    }\n    \n    const getProcessingTime = () => {\n      const startTimes = Object.values(directionStats)\n        .map(stats => stats.lastUpdate)\n        .filter(time => time)\n\n      if (startTimes.length === 0) return 0\n\n      const earliest = Math.min(...startTimes.map(time => time.getTime()))\n      return Math.round((Date.now() - earliest) / 1000)\n    }\n\n    // 处理四方向整体分析完成\n    const handleAnalysisComplete = (completeData) => {\n      try {\n        console.log('🎉 四方向分析完成，准备跳转到智能分析模块')\n\n        // 更新所有方向状态为完成\n        Object.keys(directionStats).forEach(direction => {\n          directionStats[direction].status = 'completed'\n        })\n\n        // 显示完成提示\n        ElMessage({\n          message: '所有四个方向的视频分析已完成，即将跳转到智能分析模块',\n          type: 'success',\n          duration: 3000\n        })\n\n        // 发出分析完成事件给父组件\n        emit('analysis-complete', {\n          taskId: props.taskId,\n          summary: completeData.summary,\n          message: completeData.message,\n          timestamp: completeData.timestamp\n        })\n\n      } catch (error) {\n        console.error('处理分析完成消息失败:', error)\n        ElMessage.error('处理完成消息失败: ' + error.message)\n      }\n    }\n\n    const initializeWebSocketConnection = async () => {\n      try {\n        console.log(`初始化四方向WebSocket连接: ${props.taskId}`)\n\n        // 订阅四方向帧数据、进度和完成消息\n        frameSubscription.value = await stompService.subscribeFourWayFrameUpdates(\n          props.taskId,\n          // 帧数据回调\n          (frameData) => {\n            const direction = frameData.direction\n            if (direction && directionStats[direction] !== undefined) {\n              handleFrameReceived(direction, frameData)\n            }\n          },\n          // 进度数据回调\n          (progressData) => {\n            console.log('收到四方向进度数据:', progressData)\n            // 处理进度更新\n            if (progressData.direction && directionStats[progressData.direction]) {\n              const direction = progressData.direction\n              const dirStats = directionStats[direction]\n\n              // 更新状态\n              if (progressData.status) {\n                dirStats.status = progressData.status\n              }\n\n              // 更新帧数信息\n              if (progressData.currentFrame !== undefined) {\n                dirStats.currentFrame = progressData.currentFrame\n              }\n              if (progressData.totalFrames !== undefined) {\n                dirStats.totalFrames = progressData.totalFrames\n              }\n\n              // 更新进度百分比\n              if (progressData.progress !== undefined) {\n                dirStats.progress = progressData.progress\n              }\n\n              console.log(`✓ 更新${direction}方向进度:`, {\n                status: dirStats.status,\n                currentFrame: dirStats.currentFrame,\n                totalFrames: dirStats.totalFrames,\n                progress: dirStats.progress\n              })\n            }\n          },\n          // 整体完成回调\n          (completeData) => {\n            console.log('✓ 收到四方向整体分析完成消息:', completeData)\n            handleAnalysisComplete(completeData)\n          }\n        )\n\n        isConnected.value = true\n        ElMessage.success('四方向实时检测连接成功')\n\n      } catch (error) {\n        console.error('WebSocket连接失败:', error)\n        ElMessage.error('连接失败: ' + error.message)\n        isConnected.value = false\n      }\n    }\n    \n    const cleanup = () => {\n      // 清理WebSocket订阅\n      if (frameSubscription.value) {\n        stompService.unsubscribe(frameSubscription.value)\n        frameSubscription.value = null\n      }\n      \n      // 清理各方向的帧缓冲\n      const directions = ['north', 'south', 'east', 'west']\n      directions.forEach(direction => {\n        const taskId = getDirectionTaskId(direction)\n        stompService.clearFrameBuffer(taskId)\n      })\n      \n      isConnected.value = false\n    }\n    \n    // 监听taskId变化\n    watch(() => props.taskId, (newTaskId) => {\n      if (newTaskId) {\n        cleanup()\n        initializeWebSocketConnection()\n      }\n    })\n    \n    // 生命周期\n    onMounted(() => {\n      if (props.taskId && props.autoStart) {\n        initializeWebSocketConnection()\n      }\n    })\n    \n    onUnmounted(() => {\n      cleanup()\n    })\n    \n    // 暴露方法给父组件\n    const startDetection = () => {\n      initializeWebSocketConnection()\n    }\n    \n    const stopDetection = () => {\n      cleanup()\n    }\n    \n    return {\n      // 响应式数据\n      isConnected,\n      lastUpdateTime,\n      showProgress,\n      northViewer,\n      southViewer,\n      eastViewer,\n      westViewer,\n      directionStats,\n      directionFrameData,\n\n      // 计算属性\n      connectionStatusType,\n      connectionStatusText,\n      totalVehicleCount,\n      overallProgress,\n      progressStatus,\n\n      // 方法\n      getDirectionStatusType,\n      getDirectionStatusText,\n      handleFrameReceived,\n      handleAnalysisComplete,\n      getPeakDirection,\n      getDirectionName,\n      getAverageFrameRate,\n      getProcessingTime,\n      getRecentIncrease,\n      getPeakDirectionPercentage,\n      getEfficiencyLevel,\n      getCongestionLevel,\n      getCongestionTrend,\n      getTrafficFlowBalance,\n      getBalanceTrend,\n      cleanup,\n      startDetection,\n      stopDetection\n    }\n  }\n}\n</script>\n\n<style scoped>\n.four-way-realtime-viewer {\n  background: #ffffff;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.connection-status {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding: 12px 16px;\n  background: #f8fafc;\n  border-radius: 8px;\n}\n\n.last-update {\n  font-size: 14px;\n  color: #6b7280;\n}\n\n.detection-grid {\n  display: grid !important;\n  grid-template-columns: 1fr 1fr !important;\n  grid-template-rows: 1fr 1fr !important;\n  gap: 24px !important;\n  margin-bottom: 24px;\n  min-height: 600px;\n}\n\n.detection-item {\n  background: #f9fafb;\n  border-radius: 8px;\n  padding: 16px;\n  border: 2px solid #e5e7eb;\n  transition: all 0.3s ease;\n}\n\n.detection-item:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.detection-item.north {\n  grid-column: 1 !important;\n  grid-row: 1 !important;\n}\n\n.detection-item.east {\n  grid-column: 2 !important;\n  grid-row: 1 !important;\n}\n\n.detection-item.west {\n  grid-column: 1 !important;\n  grid-row: 2 !important;\n}\n\n.detection-item.south {\n  grid-column: 2 !important;\n  grid-row: 2 !important;\n}\n\n\n\n.direction-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n}\n\n.direction-label {\n  font-weight: 500;\n  color: #374151;\n}\n\n.detection-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.direction-viewer {\n  flex: 1;\n  min-height: 300px;\n  margin-bottom: 12px;\n}\n\n/* 总览统计样式 */\n.overview-stats {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 20px 0;\n  padding: 16px 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  color: white;\n}\n\n.total-vehicles-summary {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.total-vehicles-summary .el-icon {\n  font-size: 24px;\n}\n\n.total-count {\n  font-size: 28px;\n  font-weight: 700;\n  line-height: 1;\n}\n\n.total-label {\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.peak-direction-info {\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.peak-text {\n  opacity: 0.95;\n}\n\n.direction-stats {\n  display: flex;\n  justify-content: space-around;\n  padding: 8px;\n  background: #ffffff;\n  border-radius: 6px;\n  border: 1px solid #e5e7eb;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  display: block;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.stat-label {\n  display: block;\n  font-size: 12px;\n  color: #6b7280;\n  margin-top: 2px;\n}\n\n.detection-progress {\n  margin-top: 16px;\n}\n\n.progress-text {\n  font-size: 14px;\n  font-weight: 500;\n}\n\n/* 增强统计信息样式 */\n.enhanced-global-stats {\n  margin-top: 24px;\n  background: #f8fafc;\n  border-radius: 12px;\n  padding: 20px;\n}\n\n.stats-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.stats-header h3 {\n  margin: 0;\n  color: #1f2937;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.connection-status-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n}\n\n.connection-status-info .el-icon {\n  font-size: 16px;\n}\n\n.connection-status-info .el-icon.connected {\n  color: #10b981;\n}\n\n.connection-status-info .el-icon.disconnected {\n  color: #ef4444;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 8px;\n  padding: 16px;\n  border-left: 4px solid #e5e7eb;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n}\n\n.stat-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.stat-card.primary {\n  border-left-color: #3b82f6;\n}\n\n.stat-card.success {\n  border-left-color: #10b981;\n}\n\n.stat-card.warning {\n  border-left-color: #f59e0b;\n}\n\n.stat-card.info {\n  border-left-color: #06b6d4;\n}\n\n.stat-card.danger {\n  border-left-color: #ef4444;\n}\n\n.stat-card.purple {\n  border-left-color: #8b5cf6;\n}\n\n.stat-card {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.stat-icon {\n  font-size: 24px;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f3f4f6;\n  border-radius: 8px;\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-value {\n  font-size: 20px;\n  font-weight: 700;\n  color: #1f2937;\n  line-height: 1.2;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #6b7280;\n  margin-top: 2px;\n}\n\n.stat-trend {\n  font-size: 11px;\n  font-weight: 500;\n  margin-top: 4px;\n  padding: 2px 6px;\n  border-radius: 4px;\n  background: #f3f4f6;\n  color: #6b7280;\n}\n\n.stat-trend.positive {\n  background: #dcfce7;\n  color: #16a34a;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .detection-grid {\n    grid-template-columns: 1fr;\n    grid-template-rows: auto;\n  }\n  \n  .detection-item {\n    grid-column: 1 !important;\n    grid-row: auto !important;\n  }\n}\n\n@media (max-width: 768px) {\n  .four-way-realtime-viewer {\n    padding: 16px;\n  }\n  \n  .detection-grid {\n    gap: 16px;\n  }\n  \n  .direction-viewer {\n    min-height: 150px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;AA4QA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAI,QAAS,KAAI;AAC3E,SAASC,SAAQ,QAAS,cAAa;AACvC,SACEC,UAAU,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,SAAQ,IAAKC,IAAI,EAAEC,KAAI,QACjD,yBAAwB;AAC/B,OAAOC,mBAAkB,MAAO,+CAA8C;AAC9E,OAAOC,YAAW,MAAO,uBAAsB;AAE/C,eAAe;EACbC,IAAI,EAAE,uBAAuB;EAC7BC,UAAU,EAAE;IACVV,UAAU;IAAEC,IAAI;IAAEC,GAAG;IAAEC,MAAM;IAAEE,IAAI;IAAEC,KAAK;IAC1CC;EACF,CAAC;EACDI,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAEI,OAAO;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,kBAAkB,EAAE,eAAe,EAAE,mBAAmB,CAAC;EACjEC,KAAKA,CAACT,KAAK,EAAE;IAAEU;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,WAAU,GAAI7B,GAAG,CAAC,KAAK;IAC7B,MAAM8B,cAAa,GAAI9B,GAAG,CAAC,EAAE;IAC7B,MAAM+B,iBAAgB,GAAI/B,GAAG,CAAC,IAAI;IAClC,MAAMgC,YAAW,GAAIhC,GAAG,CAAC,IAAI;;IAE7B;IACA,MAAMiC,WAAU,GAAIjC,GAAG,CAAC,IAAI;IAC5B,MAAMkC,WAAU,GAAIlC,GAAG,CAAC,IAAI;IAC5B,MAAMmC,UAAS,GAAInC,GAAG,CAAC,IAAI;IAC3B,MAAMoC,UAAS,GAAIpC,GAAG,CAAC,IAAI;;IAE3B;IACA,MAAMqC,kBAAiB,GAAIpC,QAAQ,CAAC;MAClCqC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE;IACR,CAAC;;IAED;IACA,MAAMC,cAAa,GAAIzC,QAAQ,CAAC;MAC9BqC,KAAK,EAAE;QACLK,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,IAAI;QAChBC,aAAa,EAAE,IAAI;QACnBC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,EAAE;QAAE;QACpBC,eAAe,EAAE,EAAC,CAAE;MACtB,CAAC;MACDZ,KAAK,EAAE;QACLI,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,IAAI;QAChBC,aAAa,EAAE,IAAI;QACnBC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,EAAE;QAClBC,eAAe,EAAE;MACnB,CAAC;MACDX,IAAI,EAAE;QACJG,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,IAAI;QAChBC,aAAa,EAAE,IAAI;QACnBC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,EAAE;QAClBC,eAAe,EAAE;MACnB,CAAC;MACDV,IAAI,EAAE;QACJE,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,IAAI;QAChBC,aAAa,EAAE,IAAI;QACnBC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,EAAE;QAClBC,eAAe,EAAE;MACnB;IACF,CAAC;;IAED;IACA,MAAMC,oBAAmB,GAAIlD,QAAQ,CAAC,MAAM;MAC1C,OAAO2B,WAAW,CAACwB,KAAI,GAAI,SAAQ,GAAI,QAAO;IAChD,CAAC;IAED,MAAMC,oBAAmB,GAAIpD,QAAQ,CAAC,MAAM;MAC1C,OAAO2B,WAAW,CAACwB,KAAI,GAAI,KAAI,GAAI,KAAI;IACzC,CAAC;IAED,MAAME,iBAAgB,GAAIrD,QAAQ,CAAC,MAAM;MACvC,OAAOsD,MAAM,CAACC,MAAM,CAACf,cAAc,CAAC,CAACgB,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAKD,KAAI,GAAIC,KAAK,CAACjB,YAAY,EAAE,CAAC;IAC7F,CAAC;IAED,MAAMkB,eAAc,GAAI3D,QAAQ,CAAC,MAAM;MACrC,MAAM4D,UAAS,GAAIN,MAAM,CAACC,MAAM,CAACf,cAAc;MAC/C,IAAIqB,aAAY,GAAI;MACpB,IAAIC,gBAAe,GAAI;MAEvBF,UAAU,CAACG,OAAO,CAACC,SAAQ,IAAK;QAC9B,IAAIA,SAAS,CAACrB,MAAK,KAAM,SAAS,EAAE;UAClCmB,gBAAgB,EAAC;UACjB;UACA,IAAIE,SAAS,CAACjB,WAAU,GAAI,CAAC,EAAE;YAC7B,MAAMkB,aAAY,GAAIC,IAAI,CAACC,GAAG,CAAC,CAACH,SAAS,CAAClB,YAAW,IAAK,CAAC,IAAIkB,SAAS,CAACjB,WAAU,GAAI,GAAG,EAAE,GAAG;YAC/Fc,aAAY,IAAKI,aAAY;UAC/B,OAAO,IAAID,SAAS,CAACrB,MAAK,KAAM,WAAW,EAAE;YAC3CkB,aAAY,IAAK,GAAE;UACrB;QACF;MACF,CAAC;MAED,IAAIC,gBAAe,KAAM,CAAC,EAAE,OAAO;MACnC,OAAOI,IAAI,CAACE,KAAK,CAACP,aAAY,GAAI,CAAC,GAAE;IACvC,CAAC;IAED,MAAMQ,cAAa,GAAIrE,QAAQ,CAAC,MAAM;MACpC,IAAI2D,eAAe,CAACR,KAAI,KAAM,GAAG,EAAE,OAAO,SAAQ;MAClD,IAAIQ,eAAe,CAACR,KAAI,GAAI,CAAC,EAAE,OAAO,EAAC;MACvC,OAAO,WAAU;IACnB,CAAC;;IAED;IACA,MAAMmB,sBAAqB,GAAKN,SAAS,IAAK;MAC5C,MAAMrB,MAAK,GAAIH,cAAc,CAACwB,SAAS,CAAC,CAACrB,MAAK;MAC9C,MAAM4B,OAAM,GAAI;QACd,SAAS,EAAE,MAAM;QACjB,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,SAAS;QACtB,OAAO,EAAE;MACX;MACA,OAAOA,OAAO,CAAC5B,MAAM,KAAK,MAAK;IACjC;IAEA,MAAM6B,sBAAqB,GAAKR,SAAS,IAAK;MAC5C,MAAMrB,MAAK,GAAIH,cAAc,CAACwB,SAAS,CAAC,CAACrB,MAAK;MAC9C,MAAM8B,OAAM,GAAI;QACd,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,KAAK;QAClB,OAAO,EAAE;MACX;;MAEA;MACA,IAAI9B,MAAK,KAAM,SAAS,EAAE;QACxB,MAAM+B,cAAa,GAAIC,iBAAiB,CAAC;QACzC,IAAID,cAAa,GAAI,EAAE,EAAE;UACvB,OAAO,WAAU;QACnB,OAAO,IAAIA,cAAa,GAAI,EAAE,EAAE;UAC9B,OAAO,SAAQ;QACjB;MACF;MAEA,OAAOD,OAAO,CAAC9B,MAAM,KAAK,IAAG;IAC/B;IAEA,MAAMiC,eAAc,GAAKC,SAAS,IAAK;MACrC,IAAI,CAACA,SAAS,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,YAAY;QACzB,OAAO,IAAG;MACZ;;MAEA;MACA,IAAIF,SAAS,CAACG,UAAU,CAAC,aAAa,CAAC,EAAE;QACvC,OAAOH,SAAQ;MACjB;;MAEA;MACA,IAAI,OAAOA,SAAQ,KAAM,QAAO,IAAKA,SAAS,CAACI,MAAK,GAAI,CAAC,EAAE;QACzD,MAAMC,aAAY,GAAI,0BAA0BL,SAAS,EAAC;QAC1DC,OAAO,CAACK,GAAG,CAAC,cAAc,EAAE;UAC1BC,cAAc,EAAEP,SAAS,CAACI,MAAM;UAChCI,eAAe,EAAEH,aAAa,CAACD,MAAM;UACrCK,MAAM,EAAEJ,aAAa,CAACK,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI;QAC3C,CAAC;QACD,OAAOL,aAAY;MACrB;MAEAJ,OAAO,CAACC,IAAI,CAAC,gBAAgB,EAAE,OAAOF,SAAS,EAAEA,SAAQ,GAAIA,SAAS,CAACU,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI,MAAM;MAChG,OAAO,IAAG;IACZ;IAEA,MAAMC,mBAAkB,GAAIA,CAACxB,SAAS,EAAEyB,SAAS,KAAK;MACpD,IAAI;QACFX,OAAO,CAACK,GAAG,CAAC,QAAQnB,SAAS,QAAQ,EAAE;UACrC0B,WAAW,EAAED,SAAS,CAACC,WAAW;UAClCC,cAAc,EAAEF,SAAS,CAACE,cAAc;UACxCC,YAAY,EAAE,CAAC,CAACH,SAAS,CAACZ,SAAS;UACnCgB,eAAe,EAAEJ,SAAS,CAACZ,SAAQ,GAAIY,SAAS,CAACZ,SAAS,CAACI,MAAK,GAAI;QACtE,CAAC;;QAED;QACA,MAAMa,kBAAiB,GAAIlB,eAAe,CAACa,SAAS,CAACZ,SAAS;QAC9D,IAAI,CAACiB,kBAAkB,EAAE;UACvBhB,OAAO,CAACiB,KAAK,CAAC,KAAK/B,SAAS,aAAa;UACzC;QACF;;QAEA;QACA,MAAMgC,kBAAiB,GAAI;UACzB,GAAGP,SAAS;UACZZ,SAAS,EAAEiB,kBAAkB;UAC7B9B,SAAS,EAAEA,SAAS;UACpBiC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC;;QAEA;QACAhE,kBAAkB,CAAC6B,SAAS,IAAIgC,kBAAiB;QACjDlB,OAAO,CAACK,GAAG,CAAC,KAAKnB,SAAS,UAAU;;QAEpC;QACA,IAAIyB,SAAS,CAACC,WAAU,KAAMU,SAAS,EAAE;UACvC5D,cAAc,CAACwB,SAAS,CAAC,CAAClB,YAAW,GAAI2C,SAAS,CAACC,WAAU;QAC/D;QACA,IAAID,SAAS,CAAC1C,WAAU,KAAMqD,SAAS,EAAE;UACvC5D,cAAc,CAACwB,SAAS,CAAC,CAACjB,WAAU,GAAI0C,SAAS,CAAC1C,WAAU;QAC9D;;QAEA;QACA,MAAMsD,mBAAkB,GAAIZ,SAAS,CAACE,cAAa,IAAK;;QAExD;QACA,MAAM3C,cAAa,GAAIR,cAAc,CAACwB,SAAS,CAAC,CAAChB,cAAa;QAC9DA,cAAc,CAACsD,IAAI,CAACD,mBAAmB;;QAEvC;QACA,IAAIrD,cAAc,CAACiC,MAAK,GAAIzC,cAAc,CAACwB,SAAS,CAAC,CAACf,eAAe,EAAE;UACrED,cAAc,CAACuD,KAAK,CAAC;QACvB;;QAEA;QACA,MAAMC,aAAY,GAAIxD,cAAc,CAACiC,MAAK,GAAI,IAC1Cf,IAAI,CAACE,KAAK,CAACpB,cAAc,CAACQ,MAAM,CAAC,CAACiD,GAAG,EAAEC,KAAK,KAAKD,GAAE,GAAIC,KAAK,EAAE,CAAC,IAAI1D,cAAc,CAACiC,MAAK,GAAI,EAAE,IAAI,EAAC,GAClG;;QAEJ;QACAzC,cAAc,CAACwB,SAAS,CAAC,CAACvB,YAAW,GAAI4D,mBAAkB;QAC3D7D,cAAc,CAACwB,SAAS,CAAC,CAACwC,aAAY,GAAIA,aAAY;QACtDhE,cAAc,CAACwB,SAAS,CAAC,CAACrB,MAAK,GAAI,YAAW;QAC9CH,cAAc,CAACwB,SAAS,CAAC,CAACpB,UAAS,GAAI,IAAIsD,IAAI,CAAC;;QAEhD;QACAtE,cAAc,CAACuB,KAAI,GAAI,IAAI+C,IAAI,CAAC,CAAC,CAACS,kBAAkB,CAAC;;QAErD;QACA,MAAMC,GAAE,GAAIV,IAAI,CAACU,GAAG,CAAC;QACrB,IAAIpE,cAAc,CAACwB,SAAS,CAAC,CAACnB,aAAa,EAAE;UAC3C,MAAMgE,QAAO,GAAID,GAAE,GAAIpE,cAAc,CAACwB,SAAS,CAAC,CAACnB,aAAY;UAC7D,MAAMiE,GAAE,GAAI5C,IAAI,CAACE,KAAK,CAAC,IAAG,GAAIyC,QAAO,GAAI,EAAE,IAAI,EAAC;UAChDrE,cAAc,CAACwB,SAAS,CAAC,CAACtB,SAAQ,GAAI,GAAGoE,GAAG,MAAK;QACnD;QACAtE,cAAc,CAACwB,SAAS,CAAC,CAACnB,aAAY,GAAI+D,GAAE;;QAE5C;QACAlF,IAAI,CAAC,kBAAkB,EAAE;UACvBsC,SAAS;UACTyB,SAAS,EAAEO,kBAAkB;UAC7BxD,cAAc,EAAEA,cAAc,CAACwB,SAAS,CAAC;UACzC+C,WAAW,EAAE;YACXC,aAAa,EAAE3D,iBAAiB,CAACF,KAAK;YACtC8D,aAAa,EAAEC,gBAAgB,CAAC,CAAC;YACjCC,YAAY,EAAEC,mBAAmB,CAAC,CAAC;YACnC1C,cAAc,EAAEC,iBAAiB,CAAC;UACpC;QACF,CAAC;MAEH,EAAE,OAAOoB,KAAK,EAAE;QACdjB,OAAO,CAACiB,KAAK,CAAC,OAAO/B,SAAS,UAAU,EAAE+B,KAAK;QAC/CvD,cAAc,CAACwB,SAAS,CAAC,CAACrB,MAAK,GAAI,OAAM;MAC3C;IACF;IAEA,MAAMuE,gBAAe,GAAIA,CAAA,KAAM;MAC7B,IAAIG,QAAO,GAAI;MACf,IAAIC,OAAM,GAAI,GAAE;MAEhBhE,MAAM,CAACiE,OAAO,CAAC/E,cAAc,CAAC,CAACuB,OAAO,CAAC,CAAC,CAACyD,GAAG,EAAE9D,KAAK,CAAC,KAAK;QACvD,IAAIA,KAAK,CAACjB,YAAW,GAAI4E,QAAQ,EAAE;UACjCA,QAAO,GAAI3D,KAAK,CAACjB,YAAW;UAC5B6E,OAAM,GAAIG,gBAAgB,CAACD,GAAG;QAChC;MACF,CAAC;MAED,OAAOF,OAAM;IACf;IAEA,MAAMG,gBAAe,GAAKzD,SAAS,IAAK;MACtC,MAAM0D,KAAI,GAAI;QACZtF,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;MACR;MACA,OAAOmF,KAAK,CAAC1D,SAAS,KAAKA,SAAQ;IACrC;IAEA,MAAMoD,mBAAkB,GAAIA,CAAA,KAAM;MAChC,MAAMO,KAAI,GAAIrE,MAAM,CAACC,MAAM,CAACf,cAAc,EACvCoF,GAAG,CAAClE,KAAI,IAAKmE,UAAU,CAACnE,KAAK,CAAChB,SAAS,CAAC,EACxCoF,MAAM,CAACC,IAAG,IAAK,CAACC,KAAK,CAACD,IAAI,CAAC;MAE9B,IAAIJ,KAAK,CAAC1C,MAAK,KAAM,CAAC,EAAE,OAAO;MAE/B,MAAMgD,OAAM,GAAIN,KAAK,CAACnE,MAAM,CAAC,CAACiD,GAAG,EAAEsB,IAAI,KAAKtB,GAAE,GAAIsB,IAAI,EAAE,CAAC,IAAIJ,KAAK,CAAC1C,MAAK;MACxE,OAAOf,IAAI,CAACE,KAAK,CAAC6D,OAAM,GAAI,EAAE,IAAI,EAAC;IACrC;;IAEA;IACA,MAAMC,iBAAgB,GAAIA,CAAA,KAAM;MAC9B;MACA,OAAOhE,IAAI,CAACiE,KAAK,CAACjE,IAAI,CAACkE,MAAM,CAAC,IAAI,CAAC,IAAI;IACzC;IAEA,MAAMC,0BAAyB,GAAIA,CAAA,KAAM;MACvC,MAAM5E,KAAI,GAAIJ,iBAAiB,CAACF,KAAI;MACpC,IAAIM,KAAI,KAAM,CAAC,EAAE,OAAO;MAExB,MAAM4D,QAAO,GAAInD,IAAI,CAACoE,GAAG,CAAC,GAAGhF,MAAM,CAACC,MAAM,CAACf,cAAc,CAAC,CAACoF,GAAG,CAACW,CAAA,IAAKA,CAAC,CAAC9F,YAAY,CAAC;MACnF,OAAOyB,IAAI,CAACE,KAAK,CAAEiD,QAAO,GAAI5D,KAAK,GAAI,GAAG;IAC5C;IAEA,MAAM+E,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,OAAM,GAAIrB,mBAAmB,CAAC;MACpC,IAAIqB,OAAM,IAAK,EAAE,EAAE,OAAO,IAAG;MAC7B,IAAIA,OAAM,IAAK,EAAE,EAAE,OAAO,IAAG;MAC7B,IAAIA,OAAM,IAAK,EAAE,EAAE,OAAO,IAAG;MAC7B,OAAO,IAAG;IACZ;IAEA,MAAMC,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMjF,KAAI,GAAIJ,iBAAiB,CAACF,KAAI;MACpC,IAAIM,KAAI,IAAK,EAAE,EAAE,OAAO,MAAK;MAC7B,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,MAAK;MAC7B,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,MAAK;MAC7B,OAAO,IAAG;IACZ;IAEA,MAAMkF,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,KAAI,GAAIF,kBAAkB,CAAC;MACjC,IAAIE,KAAI,KAAM,MAAM,EAAE,OAAO,OAAM;MACnC,IAAIA,KAAI,KAAM,MAAM,EAAE,OAAO,MAAK;MAClC,IAAIA,KAAI,KAAM,MAAM,EAAE,OAAO,OAAM;MACnC,OAAO,MAAK;IACd;IAEA,MAAMC,qBAAoB,GAAIA,CAAA,KAAM;MAClC,MAAMC,MAAK,GAAIxF,MAAM,CAACC,MAAM,CAACf,cAAc,CAAC,CAACoF,GAAG,CAACW,CAAA,IAAKA,CAAC,CAAC9F,YAAY;MACpE,MAAM6F,GAAE,GAAIpE,IAAI,CAACoE,GAAG,CAAC,GAAGQ,MAAM;MAC9B,MAAM3E,GAAE,GAAID,IAAI,CAACC,GAAG,CAAC,GAAG2E,MAAM;MAE9B,IAAIR,GAAE,KAAM,CAAC,EAAE,OAAO,GAAE;MAExB,MAAMS,OAAM,GAAK,CAACT,GAAE,GAAInE,GAAG,IAAImE,GAAG,GAAI,GAAE;MACxC,OAAOpE,IAAI,CAACE,KAAK,CAAC,GAAE,GAAI2E,OAAO;IACjC;IAEA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5B,MAAMD,OAAM,GAAIF,qBAAqB,CAAC;MACtC,IAAIE,OAAM,IAAK,EAAE,EAAE,OAAO,IAAG;MAC7B,IAAIA,OAAM,IAAK,EAAE,EAAE,OAAO,KAAI;MAC9B,IAAIA,OAAM,IAAK,EAAE,EAAE,OAAO,KAAI;MAC9B,OAAO,OAAM;IACf;IAEA,MAAMpE,iBAAgB,GAAIA,CAAA,KAAM;MAC9B,MAAMsE,UAAS,GAAI3F,MAAM,CAACC,MAAM,CAACf,cAAc,EAC5CoF,GAAG,CAAClE,KAAI,IAAKA,KAAK,CAACd,UAAU,EAC7BkF,MAAM,CAACoB,IAAG,IAAKA,IAAI;MAEtB,IAAID,UAAU,CAAChE,MAAK,KAAM,CAAC,EAAE,OAAO;MAEpC,MAAMkE,QAAO,GAAIjF,IAAI,CAACC,GAAG,CAAC,GAAG8E,UAAU,CAACrB,GAAG,CAACsB,IAAG,IAAKA,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC;MACnE,OAAOlF,IAAI,CAACE,KAAK,CAAC,CAAC8B,IAAI,CAACU,GAAG,CAAC,IAAIuC,QAAQ,IAAI,IAAI;IAClD;;IAEA;IACA,MAAME,sBAAqB,GAAKC,YAAY,IAAK;MAC/C,IAAI;QACFxE,OAAO,CAACK,GAAG,CAAC,wBAAwB;;QAEpC;QACA7B,MAAM,CAACiG,IAAI,CAAC/G,cAAc,CAAC,CAACuB,OAAO,CAACC,SAAQ,IAAK;UAC/CxB,cAAc,CAACwB,SAAS,CAAC,CAACrB,MAAK,GAAI,WAAU;QAC/C,CAAC;;QAED;QACAvC,SAAS,CAAC;UACRoJ,OAAO,EAAE,4BAA4B;UACrCtI,IAAI,EAAE,SAAS;UACfuI,QAAQ,EAAE;QACZ,CAAC;;QAED;QACA/H,IAAI,CAAC,mBAAmB,EAAE;UACxBT,MAAM,EAAED,KAAK,CAACC,MAAM;UACpByI,OAAO,EAAEJ,YAAY,CAACI,OAAO;UAC7BF,OAAO,EAAEF,YAAY,CAACE,OAAO;UAC7BvD,SAAS,EAAEqD,YAAY,CAACrD;QAC1B,CAAC;MAEH,EAAE,OAAOF,KAAK,EAAE;QACdjB,OAAO,CAACiB,KAAK,CAAC,aAAa,EAAEA,KAAK;QAClC3F,SAAS,CAAC2F,KAAK,CAAC,YAAW,GAAIA,KAAK,CAACyD,OAAO;MAC9C;IACF;IAEA,MAAMG,6BAA4B,GAAI,MAAAA,CAAA,KAAY;MAChD,IAAI;QACF7E,OAAO,CAACK,GAAG,CAAC,sBAAsBnE,KAAK,CAACC,MAAM,EAAE;;QAEhD;QACAY,iBAAiB,CAACsB,KAAI,GAAI,MAAMtC,YAAY,CAAC+I,4BAA4B,CACvE5I,KAAK,CAACC,MAAM;QACZ;QACCwE,SAAS,IAAK;UACb,MAAMzB,SAAQ,GAAIyB,SAAS,CAACzB,SAAQ;UACpC,IAAIA,SAAQ,IAAKxB,cAAc,CAACwB,SAAS,MAAMoC,SAAS,EAAE;YACxDZ,mBAAmB,CAACxB,SAAS,EAAEyB,SAAS;UAC1C;QACF,CAAC;QACD;QACCoE,YAAY,IAAK;UAChB/E,OAAO,CAACK,GAAG,CAAC,YAAY,EAAE0E,YAAY;UACtC;UACA,IAAIA,YAAY,CAAC7F,SAAQ,IAAKxB,cAAc,CAACqH,YAAY,CAAC7F,SAAS,CAAC,EAAE;YACpE,MAAMA,SAAQ,GAAI6F,YAAY,CAAC7F,SAAQ;YACvC,MAAM8F,QAAO,GAAItH,cAAc,CAACwB,SAAS;;YAEzC;YACA,IAAI6F,YAAY,CAAClH,MAAM,EAAE;cACvBmH,QAAQ,CAACnH,MAAK,GAAIkH,YAAY,CAAClH,MAAK;YACtC;;YAEA;YACA,IAAIkH,YAAY,CAAC/G,YAAW,KAAMsD,SAAS,EAAE;cAC3C0D,QAAQ,CAAChH,YAAW,GAAI+G,YAAY,CAAC/G,YAAW;YAClD;YACA,IAAI+G,YAAY,CAAC9G,WAAU,KAAMqD,SAAS,EAAE;cAC1C0D,QAAQ,CAAC/G,WAAU,GAAI8G,YAAY,CAAC9G,WAAU;YAChD;;YAEA;YACA,IAAI8G,YAAY,CAACE,QAAO,KAAM3D,SAAS,EAAE;cACvC0D,QAAQ,CAACC,QAAO,GAAIF,YAAY,CAACE,QAAO;YAC1C;YAEAjF,OAAO,CAACK,GAAG,CAAC,OAAOnB,SAAS,OAAO,EAAE;cACnCrB,MAAM,EAAEmH,QAAQ,CAACnH,MAAM;cACvBG,YAAY,EAAEgH,QAAQ,CAAChH,YAAY;cACnCC,WAAW,EAAE+G,QAAQ,CAAC/G,WAAW;cACjCgH,QAAQ,EAAED,QAAQ,CAACC;YACrB,CAAC;UACH;QACF,CAAC;QACD;QACCT,YAAY,IAAK;UAChBxE,OAAO,CAACK,GAAG,CAAC,kBAAkB,EAAEmE,YAAY;UAC5CD,sBAAsB,CAACC,YAAY;QACrC,CACF;QAEA3H,WAAW,CAACwB,KAAI,GAAI,IAAG;QACvB/C,SAAS,CAAC4J,OAAO,CAAC,aAAa;MAEjC,EAAE,OAAOjE,KAAK,EAAE;QACdjB,OAAO,CAACiB,KAAK,CAAC,gBAAgB,EAAEA,KAAK;QACrC3F,SAAS,CAAC2F,KAAK,CAAC,QAAO,GAAIA,KAAK,CAACyD,OAAO;QACxC7H,WAAW,CAACwB,KAAI,GAAI,KAAI;MAC1B;IACF;IAEA,MAAM8G,OAAM,GAAIA,CAAA,KAAM;MACpB;MACA,IAAIpI,iBAAiB,CAACsB,KAAK,EAAE;QAC3BtC,YAAY,CAACqJ,WAAW,CAACrI,iBAAiB,CAACsB,KAAK;QAChDtB,iBAAiB,CAACsB,KAAI,GAAI,IAAG;MAC/B;;MAEA;MACA,MAAMS,UAAS,GAAI,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM;MACpDA,UAAU,CAACG,OAAO,CAACC,SAAQ,IAAK;QAC9B,MAAM/C,MAAK,GAAIkJ,kBAAkB,CAACnG,SAAS;QAC3CnD,YAAY,CAACuJ,gBAAgB,CAACnJ,MAAM;MACtC,CAAC;MAEDU,WAAW,CAACwB,KAAI,GAAI,KAAI;IAC1B;;IAEA;IACAhD,KAAK,CAAC,MAAMa,KAAK,CAACC,MAAM,EAAGoJ,SAAS,IAAK;MACvC,IAAIA,SAAS,EAAE;QACbJ,OAAO,CAAC;QACRN,6BAA6B,CAAC;MAChC;IACF,CAAC;;IAED;IACA1J,SAAS,CAAC,MAAM;MACd,IAAIe,KAAK,CAACC,MAAK,IAAKD,KAAK,CAACK,SAAS,EAAE;QACnCsI,6BAA6B,CAAC;MAChC;IACF,CAAC;IAEDzJ,WAAW,CAAC,MAAM;MAChB+J,OAAO,CAAC;IACV,CAAC;;IAED;IACA,MAAMK,cAAa,GAAIA,CAAA,KAAM;MAC3BX,6BAA6B,CAAC;IAChC;IAEA,MAAMY,aAAY,GAAIA,CAAA,KAAM;MAC1BN,OAAO,CAAC;IACV;IAEA,OAAO;MACL;MACAtI,WAAW;MACXC,cAAc;MACdE,YAAY;MACZC,WAAW;MACXC,WAAW;MACXC,UAAU;MACVC,UAAU;MACVM,cAAc;MACdL,kBAAkB;MAElB;MACAe,oBAAoB;MACpBE,oBAAoB;MACpBC,iBAAiB;MACjBM,eAAe;MACfU,cAAc;MAEd;MACAC,sBAAsB;MACtBE,sBAAsB;MACtBgB,mBAAmB;MACnB6D,sBAAsB;MACtBnC,gBAAgB;MAChBO,gBAAgB;MAChBL,mBAAmB;MACnBzC,iBAAiB;MACjBuD,iBAAiB;MACjBG,0BAA0B;MAC1BG,kBAAkB;MAClBE,kBAAkB;MAClBC,kBAAkB;MAClBE,qBAAqB;MACrBG,eAAe;MACfiB,OAAO;MACPK,cAAc;MACdC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}