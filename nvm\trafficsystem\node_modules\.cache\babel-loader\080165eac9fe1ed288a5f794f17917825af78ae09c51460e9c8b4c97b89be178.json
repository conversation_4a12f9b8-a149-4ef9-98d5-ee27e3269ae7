{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, computed, watch, onMounted, onUnmounted } from 'vue';\nimport { Refresh } from '@element-plus/icons-vue';\nimport CongestionGradeIndicator from './CongestionGradeIndicator.vue';\nimport TrafficTrendChart from './TrafficTrendChart.vue';\nimport StrategyRecommendation from './StrategyRecommendation.vue';\nimport { calculateCongestionGrade, calculateMultipleMovingAverages, analyzeTrend, generateTrafficStrategy } from '@/utils/trafficAnalysisUtils';\nexport default {\n  name: 'IntelligentTrafficPanel',\n  components: {\n    Refresh,\n    CongestionGradeIndicator,\n    TrafficTrendChart,\n    StrategyRecommendation\n  },\n  props: {\n    // 当前帧的车辆检测数量\n    currentVehicleCount: {\n      type: Number,\n      default: 0\n    },\n    // 是否自动更新\n    autoUpdate: {\n      type: Boolean,\n      default: true\n    },\n    // 历史数据最大长度\n    maxHistoryLength: {\n      type: Number,\n      default: 50\n    }\n  },\n  emits: ['strategy-applied', 'data-updated'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const vehicleHistory = ref([]);\n    const lastUpdateTime = ref('');\n\n    // 计算属性\n    const currentGrade = computed(() => calculateCongestionGrade(props.currentVehicleCount));\n    const movingAverages = computed(() => calculateMultipleMovingAverages(vehicleHistory.value));\n    const trendInfo = computed(() => analyzeTrend(vehicleHistory.value));\n    const currentStrategy = computed(() => generateTrafficStrategy(currentGrade.value, trendInfo.value, movingAverages.value));\n\n    // 方法\n    const updateVehicleHistory = newCount => {\n      vehicleHistory.value.push(newCount);\n\n      // 限制历史数据长度\n      if (vehicleHistory.value.length > props.maxHistoryLength) {\n        vehicleHistory.value.shift();\n      }\n\n      // 更新时间戳\n      lastUpdateTime.value = new Date().toLocaleTimeString();\n\n      // 发出数据更新事件\n      emit('data-updated', {\n        currentCount: newCount,\n        grade: currentGrade.value,\n        movingAverages: movingAverages.value,\n        trend: trendInfo.value,\n        strategy: currentStrategy.value\n      });\n    };\n    const handleApplyStrategy = strategy => {\n      emit('strategy-applied', {\n        strategy,\n        grade: currentGrade.value,\n        vehicleCount: props.currentVehicleCount,\n        timestamp: new Date().toISOString()\n      });\n    };\n    const handleViewDetails = details => {\n      console.log('查看策略详情:', details);\n    };\n\n    // 监听车辆数量变化\n    watch(() => props.currentVehicleCount, newCount => {\n      if (props.autoUpdate) {\n        updateVehicleHistory(newCount);\n      }\n    });\n\n    // 手动更新数据的方法\n    const manualUpdate = () => {\n      updateVehicleHistory(props.currentVehicleCount);\n    };\n\n    // 清空历史数据\n    const clearHistory = () => {\n      vehicleHistory.value = [];\n      lastUpdateTime.value = '';\n    };\n\n    // 获取当前状态摘要\n    const getStatusSummary = () => {\n      return {\n        currentVehicleCount: props.currentVehicleCount,\n        grade: currentGrade.value,\n        movingAverages: movingAverages.value,\n        trend: trendInfo.value,\n        strategy: currentStrategy.value,\n        historyLength: vehicleHistory.value.length,\n        lastUpdate: lastUpdateTime.value\n      };\n    };\n\n    // 组件挂载时初始化\n    onMounted(() => {\n      if (props.currentVehicleCount > 0) {\n        updateVehicleHistory(props.currentVehicleCount);\n      }\n    });\n\n    // 暴露方法给父组件\n    const updateData = manualUpdate;\n    const clearData = clearHistory;\n    const getStatus = getStatusSummary;\n    return {\n      // 响应式数据\n      vehicleHistory,\n      lastUpdateTime,\n      // 计算属性\n      currentGrade,\n      movingAverages,\n      trendInfo,\n      currentStrategy,\n      // 方法\n      handleApplyStrategy,\n      handleViewDetails,\n      // 暴露给父组件的方法\n      updateData,\n      clearData,\n      getStatus\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "watch", "onMounted", "onUnmounted", "Refresh", "CongestionGradeIndicator", "TrafficTrendChart", "StrategyRecommendation", "calculateCongestionGrade", "calculateMultipleMovingAverages", "analyzeTrend", "generateTrafficStrategy", "name", "components", "props", "currentVehicleCount", "type", "Number", "default", "autoUpdate", "Boolean", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emits", "setup", "emit", "vehicleHistory", "lastUpdateTime", "currentGrade", "movingAverages", "value", "trendInfo", "currentStrategy", "updateVehicleHistory", "newCount", "push", "length", "shift", "Date", "toLocaleTimeString", "currentCount", "grade", "trend", "strategy", "handleApplyStrategy", "vehicleCount", "timestamp", "toISOString", "handleViewDetails", "details", "console", "log", "manualUpdate", "clearHistory", "getStatusSummary", "history<PERSON><PERSON><PERSON>", "lastUpdate", "updateData", "clearData", "getStatus"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\traffic\\IntelligentTrafficPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"intelligent-traffic-panel\">\n    <div class=\"panel-header\">\n      <h3>智能交通状态面板</h3>\n      <div class=\"update-info\">\n        <el-icon><Refresh /></el-icon>\n        <span>{{ lastUpdateTime }}</span>\n      </div>\n    </div>\n    \n    <div class=\"panel-content\">\n      <!-- 拥挤等级指示器 -->\n      <div class=\"section\">\n        <CongestionGradeIndicator \n          :grade=\"currentGrade\"\n          :vehicle-count=\"currentVehicleCount\"\n        />\n      </div>\n      \n      <!-- 交通趋势图表 -->\n      <div class=\"section\">\n        <TrafficTrendChart \n          :vehicle-data=\"vehicleHistory\"\n          :moving-averages=\"movingAverages\"\n          :trend-info=\"trendInfo\"\n        />\n      </div>\n      \n      <!-- 策略推荐 -->\n      <div class=\"section\">\n        <StrategyRecommendation \n          :strategy=\"currentStrategy\"\n          :grade=\"currentGrade\"\n          @apply-strategy=\"handleApplyStrategy\"\n          @view-details=\"handleViewDetails\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, watch, onMounted, onUnmounted } from 'vue'\nimport { Refresh } from '@element-plus/icons-vue'\nimport CongestionGradeIndicator from './CongestionGradeIndicator.vue'\nimport TrafficTrendChart from './TrafficTrendChart.vue'\nimport StrategyRecommendation from './StrategyRecommendation.vue'\nimport {\n  calculateCongestionGrade,\n  calculateMultipleMovingAverages,\n  analyzeTrend,\n  generateTrafficStrategy\n} from '@/utils/trafficAnalysisUtils'\n\nexport default {\n  name: 'IntelligentTrafficPanel',\n  components: {\n    Refresh,\n    CongestionGradeIndicator,\n    TrafficTrendChart,\n    StrategyRecommendation\n  },\n  props: {\n    // 当前帧的车辆检测数量\n    currentVehicleCount: {\n      type: Number,\n      default: 0\n    },\n    // 是否自动更新\n    autoUpdate: {\n      type: Boolean,\n      default: true\n    },\n    // 历史数据最大长度\n    maxHistoryLength: {\n      type: Number,\n      default: 50\n    }\n  },\n  emits: ['strategy-applied', 'data-updated'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const vehicleHistory = ref([])\n    const lastUpdateTime = ref('')\n    \n    // 计算属性\n    const currentGrade = computed(() => \n      calculateCongestionGrade(props.currentVehicleCount)\n    )\n    \n    const movingAverages = computed(() => \n      calculateMultipleMovingAverages(vehicleHistory.value)\n    )\n    \n    const trendInfo = computed(() => \n      analyzeTrend(vehicleHistory.value)\n    )\n    \n    const currentStrategy = computed(() => \n      generateTrafficStrategy(currentGrade.value, trendInfo.value, movingAverages.value)\n    )\n    \n    // 方法\n    const updateVehicleHistory = (newCount) => {\n      vehicleHistory.value.push(newCount)\n      \n      // 限制历史数据长度\n      if (vehicleHistory.value.length > props.maxHistoryLength) {\n        vehicleHistory.value.shift()\n      }\n      \n      // 更新时间戳\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n      \n      // 发出数据更新事件\n      emit('data-updated', {\n        currentCount: newCount,\n        grade: currentGrade.value,\n        movingAverages: movingAverages.value,\n        trend: trendInfo.value,\n        strategy: currentStrategy.value\n      })\n    }\n    \n    const handleApplyStrategy = (strategy) => {\n      emit('strategy-applied', {\n        strategy,\n        grade: currentGrade.value,\n        vehicleCount: props.currentVehicleCount,\n        timestamp: new Date().toISOString()\n      })\n    }\n    \n    const handleViewDetails = (details) => {\n      console.log('查看策略详情:', details)\n    }\n    \n    // 监听车辆数量变化\n    watch(() => props.currentVehicleCount, (newCount) => {\n      if (props.autoUpdate) {\n        updateVehicleHistory(newCount)\n      }\n    })\n    \n    // 手动更新数据的方法\n    const manualUpdate = () => {\n      updateVehicleHistory(props.currentVehicleCount)\n    }\n    \n    // 清空历史数据\n    const clearHistory = () => {\n      vehicleHistory.value = []\n      lastUpdateTime.value = ''\n    }\n    \n    // 获取当前状态摘要\n    const getStatusSummary = () => {\n      return {\n        currentVehicleCount: props.currentVehicleCount,\n        grade: currentGrade.value,\n        movingAverages: movingAverages.value,\n        trend: trendInfo.value,\n        strategy: currentStrategy.value,\n        historyLength: vehicleHistory.value.length,\n        lastUpdate: lastUpdateTime.value\n      }\n    }\n    \n    // 组件挂载时初始化\n    onMounted(() => {\n      if (props.currentVehicleCount > 0) {\n        updateVehicleHistory(props.currentVehicleCount)\n      }\n    })\n    \n    // 暴露方法给父组件\n    const updateData = manualUpdate\n    const clearData = clearHistory\n    const getStatus = getStatusSummary\n    \n    return {\n      // 响应式数据\n      vehicleHistory,\n      lastUpdateTime,\n      \n      // 计算属性\n      currentGrade,\n      movingAverages,\n      trendInfo,\n      currentStrategy,\n      \n      // 方法\n      handleApplyStrategy,\n      handleViewDetails,\n      \n      // 暴露给父组件的方法\n      updateData,\n      clearData,\n      getStatus\n    }\n  }\n}\n</script>\n\n<style scoped>\n.intelligent-traffic-panel {\n  background: #f8f9fa;\n  border-radius: 16px;\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  border: 1px solid #e8e8e8;\n}\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.panel-header h3 {\n  margin: 0;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.update-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.panel-content {\n  padding: 24px;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.section {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.section:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .panel-content {\n    gap: 20px;\n  }\n}\n\n@media (max-width: 768px) {\n  .panel-header {\n    flex-direction: column;\n    gap: 12px;\n    align-items: flex-start;\n    padding: 16px 20px;\n  }\n  \n  .panel-header h3 {\n    font-size: 18px;\n  }\n  \n  .panel-content {\n    padding: 20px;\n    gap: 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .panel-header {\n    padding: 12px 16px;\n  }\n  \n  .panel-content {\n    padding: 16px;\n    gap: 12px;\n  }\n}\n\n/* 动画效果 */\n.section {\n  animation: fadeInUp 0.6s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 为不同等级添加主题色彩 */\n.intelligent-traffic-panel[data-grade=\"A\"] .panel-header {\n  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);\n}\n\n.intelligent-traffic-panel[data-grade=\"B\"] .panel-header {\n  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);\n}\n\n.intelligent-traffic-panel[data-grade=\"C\"] .panel-header {\n  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);\n}\n\n.intelligent-traffic-panel[data-grade=\"D\"] .panel-header {\n  background: linear-gradient(135deg, #fa8c16 0%, #ff9c6e 100%);\n}\n\n.intelligent-traffic-panel[data-grade=\"E\"] .panel-header {\n  background: linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%);\n}\n\n.intelligent-traffic-panel[data-grade=\"F\"] .panel-header {\n  background: linear-gradient(135deg, #a8071a 0%, #cf1322 100%);\n}\n</style>\n"], "mappings": ";AA0CA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AACjE,SAASC,OAAM,QAAS,yBAAwB;AAChD,OAAOC,wBAAuB,MAAO,gCAA+B;AACpE,OAAOC,iBAAgB,MAAO,yBAAwB;AACtD,OAAOC,sBAAqB,MAAO,8BAA6B;AAChE,SACEC,wBAAwB,EACxBC,+BAA+B,EAC/BC,YAAY,EACZC,uBAAsB,QACjB,8BAA6B;AAEpC,eAAe;EACbC,IAAI,EAAE,yBAAyB;EAC/BC,UAAU,EAAE;IACVT,OAAO;IACPC,wBAAwB;IACxBC,iBAAiB;IACjBC;EACF,CAAC;EACDO,KAAK,EAAE;IACL;IACAC,mBAAmB,EAAE;MACnBC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACD;IACAC,UAAU,EAAE;MACVH,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE;IACX,CAAC;IACD;IACAG,gBAAgB,EAAE;MAChBL,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EACDI,KAAK,EAAE,CAAC,kBAAkB,EAAE,cAAc,CAAC;EAC3CC,KAAKA,CAACT,KAAK,EAAE;IAAEU;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,cAAa,GAAI1B,GAAG,CAAC,EAAE;IAC7B,MAAM2B,cAAa,GAAI3B,GAAG,CAAC,EAAE;;IAE7B;IACA,MAAM4B,YAAW,GAAI3B,QAAQ,CAAC,MAC5BQ,wBAAwB,CAACM,KAAK,CAACC,mBAAmB,CACpD;IAEA,MAAMa,cAAa,GAAI5B,QAAQ,CAAC,MAC9BS,+BAA+B,CAACgB,cAAc,CAACI,KAAK,CACtD;IAEA,MAAMC,SAAQ,GAAI9B,QAAQ,CAAC,MACzBU,YAAY,CAACe,cAAc,CAACI,KAAK,CACnC;IAEA,MAAME,eAAc,GAAI/B,QAAQ,CAAC,MAC/BW,uBAAuB,CAACgB,YAAY,CAACE,KAAK,EAAEC,SAAS,CAACD,KAAK,EAAED,cAAc,CAACC,KAAK,CACnF;;IAEA;IACA,MAAMG,oBAAmB,GAAKC,QAAQ,IAAK;MACzCR,cAAc,CAACI,KAAK,CAACK,IAAI,CAACD,QAAQ;;MAElC;MACA,IAAIR,cAAc,CAACI,KAAK,CAACM,MAAK,GAAIrB,KAAK,CAACO,gBAAgB,EAAE;QACxDI,cAAc,CAACI,KAAK,CAACO,KAAK,CAAC;MAC7B;;MAEA;MACAV,cAAc,CAACG,KAAI,GAAI,IAAIQ,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;;MAErD;MACAd,IAAI,CAAC,cAAc,EAAE;QACnBe,YAAY,EAAEN,QAAQ;QACtBO,KAAK,EAAEb,YAAY,CAACE,KAAK;QACzBD,cAAc,EAAEA,cAAc,CAACC,KAAK;QACpCY,KAAK,EAAEX,SAAS,CAACD,KAAK;QACtBa,QAAQ,EAAEX,eAAe,CAACF;MAC5B,CAAC;IACH;IAEA,MAAMc,mBAAkB,GAAKD,QAAQ,IAAK;MACxClB,IAAI,CAAC,kBAAkB,EAAE;QACvBkB,QAAQ;QACRF,KAAK,EAAEb,YAAY,CAACE,KAAK;QACzBe,YAAY,EAAE9B,KAAK,CAACC,mBAAmB;QACvC8B,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MACpC,CAAC;IACH;IAEA,MAAMC,iBAAgB,GAAKC,OAAO,IAAK;MACrCC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEF,OAAO;IAChC;;IAEA;IACA/C,KAAK,CAAC,MAAMa,KAAK,CAACC,mBAAmB,EAAGkB,QAAQ,IAAK;MACnD,IAAInB,KAAK,CAACK,UAAU,EAAE;QACpBa,oBAAoB,CAACC,QAAQ;MAC/B;IACF,CAAC;;IAED;IACA,MAAMkB,YAAW,GAAIA,CAAA,KAAM;MACzBnB,oBAAoB,CAAClB,KAAK,CAACC,mBAAmB;IAChD;;IAEA;IACA,MAAMqC,YAAW,GAAIA,CAAA,KAAM;MACzB3B,cAAc,CAACI,KAAI,GAAI,EAAC;MACxBH,cAAc,CAACG,KAAI,GAAI,EAAC;IAC1B;;IAEA;IACA,MAAMwB,gBAAe,GAAIA,CAAA,KAAM;MAC7B,OAAO;QACLtC,mBAAmB,EAAED,KAAK,CAACC,mBAAmB;QAC9CyB,KAAK,EAAEb,YAAY,CAACE,KAAK;QACzBD,cAAc,EAAEA,cAAc,CAACC,KAAK;QACpCY,KAAK,EAAEX,SAAS,CAACD,KAAK;QACtBa,QAAQ,EAAEX,eAAe,CAACF,KAAK;QAC/ByB,aAAa,EAAE7B,cAAc,CAACI,KAAK,CAACM,MAAM;QAC1CoB,UAAU,EAAE7B,cAAc,CAACG;MAC7B;IACF;;IAEA;IACA3B,SAAS,CAAC,MAAM;MACd,IAAIY,KAAK,CAACC,mBAAkB,GAAI,CAAC,EAAE;QACjCiB,oBAAoB,CAAClB,KAAK,CAACC,mBAAmB;MAChD;IACF,CAAC;;IAED;IACA,MAAMyC,UAAS,GAAIL,YAAW;IAC9B,MAAMM,SAAQ,GAAIL,YAAW;IAC7B,MAAMM,SAAQ,GAAIL,gBAAe;IAEjC,OAAO;MACL;MACA5B,cAAc;MACdC,cAAc;MAEd;MACAC,YAAY;MACZC,cAAc;MACdE,SAAS;MACTC,eAAe;MAEf;MACAY,mBAAmB;MACnBI,iBAAiB;MAEjB;MACAS,UAAU;MACVC,SAAS;MACTC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}