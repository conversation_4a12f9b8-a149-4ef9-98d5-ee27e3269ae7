{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createBlock as _createBlock, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, resolveDynamicComponent as _resolveDynamicComponent } from \"vue\";\nconst _hoisted_1 = {\n  class: \"intelligent-traffic-status-panel\"\n};\nconst _hoisted_2 = {\n  class: \"panel-header\"\n};\nconst _hoisted_3 = {\n  class: \"panel-title\"\n};\nconst _hoisted_4 = {\n  class: \"update-info\"\n};\nconst _hoisted_5 = {\n  class: \"realtime-vehicle-section\"\n};\nconst _hoisted_6 = {\n  class: \"vehicle-grid\"\n};\nconst _hoisted_7 = {\n  class: \"direction-header\"\n};\nconst _hoisted_8 = {\n  class: \"direction-name\"\n};\nconst _hoisted_9 = {\n  class: \"vehicle-metrics\"\n};\nconst _hoisted_10 = {\n  class: \"metric-item primary\"\n};\nconst _hoisted_11 = {\n  class: \"metric-value\"\n};\nconst _hoisted_12 = {\n  class: \"metric-item secondary\"\n};\nconst _hoisted_13 = {\n  class: \"metric-value\"\n};\nconst _hoisted_14 = {\n  class: \"metric-item progress\"\n};\nconst _hoisted_15 = {\n  class: \"metric-label\"\n};\nconst _hoisted_16 = {\n  class: \"congestion-analysis-section\"\n};\nconst _hoisted_17 = {\n  class: \"congestion-grid\"\n};\nconst _hoisted_18 = {\n  class: \"congestion-overview\"\n};\nconst _hoisted_19 = {\n  class: \"level-indicator\"\n};\nconst _hoisted_20 = {\n  class: \"level-description\"\n};\nconst _hoisted_21 = {\n  class: \"congestion-metrics\"\n};\nconst _hoisted_22 = {\n  class: \"metric\"\n};\nconst _hoisted_23 = {\n  class: \"metric-value\"\n};\nconst _hoisted_24 = {\n  class: \"metric\"\n};\nconst _hoisted_25 = {\n  class: \"metric-value trend\"\n};\nconst _hoisted_26 = {\n  class: \"flow-balance\"\n};\nconst _hoisted_27 = {\n  class: \"balance-indicator\"\n};\nconst _hoisted_28 = {\n  class: \"balance-value\"\n};\nconst _hoisted_29 = {\n  class: \"balance-status\"\n};\nconst _hoisted_30 = {\n  class: \"management-suggestions-section\"\n};\nconst _hoisted_31 = {\n  class: \"suggestions-container\"\n};\nconst _hoisted_32 = {\n  class: \"suggestion-header\"\n};\nconst _hoisted_33 = {\n  class: \"suggestion-title\"\n};\nconst _hoisted_34 = {\n  class: \"suggestion-content\"\n};\nconst _hoisted_35 = {\n  key: 0,\n  class: \"suggestion-action\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_TrendCharts = _resolveComponent(\"TrendCharts\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_Warning = _resolveComponent(\"Warning\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 面板标题 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"h3\", _hoisted_3, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_TrendCharts)]),\n    _: 1 /* STABLE */\n  }), _cache[0] || (_cache[0] = _createTextVNode(\" 智能交通状态面板 \"))]), _createElementVNode(\"div\", _hoisted_4, [$props.lastUpdateTime ? (_openBlock(), _createBlock(_component_el_tag, {\n    key: 0,\n    size: \"small\",\n    type: \"info\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($props.lastUpdateTime), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" 实时车辆计数区域 \"), _createElementVNode(\"div\", _hoisted_5, [_cache[3] || (_cache[3] = _createElementVNode(\"h4\", {\n    class: \"section-title\"\n  }, \"实时车辆检测\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.directions, direction => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: direction.key,\n      class: _normalizeClass([\"vehicle-card\", $setup.getDirectionCardClass(direction.key)])\n    }, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"span\", _hoisted_8, _toDisplayString(direction.name), 1 /* TEXT */), _createVNode(_component_el_tag, {\n      type: $setup.getDirectionStatusType(direction.key),\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getDirectionStatusText(direction.key)), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]), _createElementVNode(\"div\", _hoisted_9, [_createCommentVNode(\" 当前帧车辆数 \"), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString($setup.getCurrentVehicleCount(direction.key)), 1 /* TEXT */), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n      class: \"metric-label\"\n    }, \"当前帧车辆\", -1 /* HOISTED */))]), _createCommentVNode(\" 移动平均 \"), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, _toDisplayString($setup.getMovingAverage(direction.key)), 1 /* TEXT */), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n      class: \"metric-label\"\n    }, \"移动平均\", -1 /* HOISTED */))]), _createCommentVNode(\" 进度显示 \"), _createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_progress, {\n      percentage: $setup.getDirectionProgress(direction.key),\n      \"stroke-width\": 6,\n      \"show-text\": false,\n      status: $setup.getProgressStatus(direction.key)\n    }, null, 8 /* PROPS */, [\"percentage\", \"status\"]), _createElementVNode(\"div\", _hoisted_15, _toDisplayString($setup.getDirectionProgress(direction.key)) + \"% (\" + _toDisplayString($setup.getCurrentFrame(direction.key)) + \"/\" + _toDisplayString($setup.getTotalFrames(direction.key)) + \") \", 1 /* TEXT */)])])], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 拥堵等级分析 \"), _createElementVNode(\"div\", _hoisted_16, [_cache[7] || (_cache[7] = _createElementVNode(\"h4\", {\n    class: \"section-title\"\n  }, \"拥堵等级分析\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"congestion-level\", $setup.getCongestionLevelClass()])\n  }, [_createElementVNode(\"div\", _hoisted_19, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Warning)]),\n    _: 1 /* STABLE */\n  }), _createTextVNode(\" \" + _toDisplayString($setup.getCongestionLevel()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_20, _toDisplayString($setup.getCongestionDescription()), 1 /* TEXT */)], 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_cache[4] || (_cache[4] = _createElementVNode(\"span\", {\n    class: \"metric-label\"\n  }, \"拥堵指数\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_23, _toDisplayString($setup.getCongestionIndex()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_24, [_cache[5] || (_cache[5] = _createElementVNode(\"span\", {\n    class: \"metric-label\"\n  }, \"变化趋势\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_25, _toDisplayString($setup.getCongestionTrend()), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"balance-title\"\n  }, \"交通流平衡度\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_28, _toDisplayString($setup.getTrafficFlowBalance()) + \"%\", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_29, _toDisplayString($setup.getBalanceStatus()), 1 /* TEXT */)]), _createVNode(_component_el_progress, {\n    type: \"circle\",\n    percentage: $setup.getTrafficFlowBalance(),\n    width: 80,\n    \"stroke-width\": 8,\n    color: $setup.getBalanceColor()\n  }, null, 8 /* PROPS */, [\"percentage\", \"color\"])])])]), _createCommentVNode(\" 交通管理建议 \"), _createElementVNode(\"div\", _hoisted_30, [_cache[8] || (_cache[8] = _createElementVNode(\"h4\", {\n    class: \"section-title\"\n  }, \"智能管理建议\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_31, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.getManagementSuggestions(), suggestion => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: suggestion.id,\n      class: _normalizeClass([\"suggestion-card\", suggestion.priority])\n    }, [_createElementVNode(\"div\", _hoisted_32, [_createVNode(_component_el_icon, {\n      class: _normalizeClass(suggestion.iconClass)\n    }, {\n      default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(suggestion.icon)))]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"class\"]), _createElementVNode(\"span\", _hoisted_33, _toDisplayString(suggestion.title), 1 /* TEXT */), _createVNode(_component_el_tag, {\n      type: suggestion.tagType,\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString(suggestion.priority), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]), _createElementVNode(\"div\", _hoisted_34, _toDisplayString(suggestion.content), 1 /* TEXT */), suggestion.action ? (_openBlock(), _createElementBlock(\"div\", _hoisted_35, [_createVNode(_component_el_button, {\n      type: suggestion.actionType,\n      size: \"small\",\n      onClick: $event => $setup.applySuggestion(suggestion)\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString(suggestion.action), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\", \"onClick\"])])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_Trend<PERSON><PERSON>s", "_", "_createTextVNode", "_hoisted_4", "$props", "lastUpdateTime", "_createBlock", "_component_el_tag", "size", "type", "_toDisplayString", "_hoisted_5", "_hoisted_6", "_Fragment", "_renderList", "$setup", "directions", "direction", "_normalizeClass", "getDirectionCardClass", "_hoisted_7", "_hoisted_8", "name", "getDirectionStatusType", "getDirectionStatusText", "_hoisted_9", "_hoisted_10", "_hoisted_11", "getCurrentVehicleCount", "_hoisted_12", "_hoisted_13", "getMovingAverage", "_hoisted_14", "_component_el_progress", "percentage", "getDirectionProgress", "status", "getProgressStatus", "_hoisted_15", "getCurrentFrame", "getTotalFrames", "_hoisted_16", "_hoisted_17", "_hoisted_18", "getCongestionLevelClass", "_hoisted_19", "_component_Warning", "getCongestionLevel", "_hoisted_20", "getCongestionDescription", "_hoisted_21", "_hoisted_22", "_hoisted_23", "getCongestionIndex", "_hoisted_24", "_hoisted_25", "getCongestionTrend", "_hoisted_26", "_hoisted_27", "_hoisted_28", "getTrafficFlowBalance", "_hoisted_29", "getBalanceStatus", "width", "color", "getBalanceColor", "_hoisted_30", "_hoisted_31", "getManagementSuggestions", "suggestion", "id", "priority", "_hoisted_32", "iconClass", "_resolveDynamicComponent", "icon", "_hoisted_33", "title", "tagType", "_hoisted_34", "content", "action", "_hoisted_35", "_component_el_button", "actionType", "onClick", "$event", "applySuggestion"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\analysis\\IntelligentTrafficStatusPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"intelligent-traffic-status-panel\">\n    <!-- 面板标题 -->\n    <div class=\"panel-header\">\n      <h3 class=\"panel-title\">\n        <el-icon><TrendCharts /></el-icon>\n        智能交通状态面板\n      </h3>\n      <div class=\"update-info\">\n        <el-tag v-if=\"lastUpdateTime\" size=\"small\" type=\"info\">\n          {{ lastUpdateTime }}\n        </el-tag>\n      </div>\n    </div>\n\n    <!-- 实时车辆计数区域 -->\n    <div class=\"realtime-vehicle-section\">\n      <h4 class=\"section-title\">实时车辆检测</h4>\n      <div class=\"vehicle-grid\">\n        <div \n          v-for=\"direction in directions\" \n          :key=\"direction.key\"\n          class=\"vehicle-card\"\n          :class=\"getDirectionCardClass(direction.key)\"\n        >\n          <div class=\"direction-header\">\n            <span class=\"direction-name\">{{ direction.name }}</span>\n            <el-tag \n              :type=\"getDirectionStatusType(direction.key)\" \n              size=\"small\"\n            >\n              {{ getDirectionStatusText(direction.key) }}\n            </el-tag>\n          </div>\n          \n          <div class=\"vehicle-metrics\">\n            <!-- 当前帧车辆数 -->\n            <div class=\"metric-item primary\">\n              <div class=\"metric-value\">{{ getCurrentVehicleCount(direction.key) }}</div>\n              <div class=\"metric-label\">当前帧车辆</div>\n            </div>\n            \n            <!-- 移动平均 -->\n            <div class=\"metric-item secondary\">\n              <div class=\"metric-value\">{{ getMovingAverage(direction.key) }}</div>\n              <div class=\"metric-label\">移动平均</div>\n            </div>\n            \n            <!-- 进度显示 -->\n            <div class=\"metric-item progress\">\n              <el-progress \n                :percentage=\"getDirectionProgress(direction.key)\"\n                :stroke-width=\"6\"\n                :show-text=\"false\"\n                :status=\"getProgressStatus(direction.key)\"\n              />\n              <div class=\"metric-label\">\n                {{ getDirectionProgress(direction.key) }}% \n                ({{ getCurrentFrame(direction.key) }}/{{ getTotalFrames(direction.key) }})\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 拥堵等级分析 -->\n    <div class=\"congestion-analysis-section\">\n      <h4 class=\"section-title\">拥堵等级分析</h4>\n      <div class=\"congestion-grid\">\n        <div class=\"congestion-overview\">\n          <div class=\"congestion-level\" :class=\"getCongestionLevelClass()\">\n            <div class=\"level-indicator\">\n              <el-icon><Warning /></el-icon>\n              {{ getCongestionLevel() }}\n            </div>\n            <div class=\"level-description\">{{ getCongestionDescription() }}</div>\n          </div>\n          \n          <div class=\"congestion-metrics\">\n            <div class=\"metric\">\n              <span class=\"metric-label\">拥堵指数</span>\n              <span class=\"metric-value\">{{ getCongestionIndex() }}</span>\n            </div>\n            <div class=\"metric\">\n              <span class=\"metric-label\">变化趋势</span>\n              <span class=\"metric-value trend\">{{ getCongestionTrend() }}</span>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"flow-balance\">\n          <div class=\"balance-indicator\">\n            <div class=\"balance-title\">交通流平衡度</div>\n            <div class=\"balance-value\">{{ getTrafficFlowBalance() }}%</div>\n            <div class=\"balance-status\">{{ getBalanceStatus() }}</div>\n          </div>\n          \n          <el-progress \n            type=\"circle\" \n            :percentage=\"getTrafficFlowBalance()\"\n            :width=\"80\"\n            :stroke-width=\"8\"\n            :color=\"getBalanceColor()\"\n          />\n        </div>\n      </div>\n    </div>\n\n    <!-- 交通管理建议 -->\n    <div class=\"management-suggestions-section\">\n      <h4 class=\"section-title\">智能管理建议</h4>\n      <div class=\"suggestions-container\">\n        <div \n          v-for=\"suggestion in getManagementSuggestions()\" \n          :key=\"suggestion.id\"\n          class=\"suggestion-card\"\n          :class=\"suggestion.priority\"\n        >\n          <div class=\"suggestion-header\">\n            <el-icon :class=\"suggestion.iconClass\">\n              <component :is=\"suggestion.icon\" />\n            </el-icon>\n            <span class=\"suggestion-title\">{{ suggestion.title }}</span>\n            <el-tag :type=\"suggestion.tagType\" size=\"small\">\n              {{ suggestion.priority }}\n            </el-tag>\n          </div>\n          <div class=\"suggestion-content\">{{ suggestion.content }}</div>\n          <div class=\"suggestion-action\" v-if=\"suggestion.action\">\n            <el-button \n              :type=\"suggestion.actionType\" \n              size=\"small\"\n              @click=\"applySuggestion(suggestion)\"\n            >\n              {{ suggestion.action }}\n            </el-button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed, ref } from 'vue'\nimport { \n  TrendCharts, Warning, Setting, InfoFilled, \n  SuccessFilled, WarnTriangleFilled, Clock \n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'IntelligentTrafficStatusPanel',\n  components: {\n    TrendCharts, Warning, Setting, InfoFilled,\n    SuccessFilled, WarnTriangleFilled, Clock\n  },\n  props: {\n    directionStats: {\n      type: Object,\n      required: true\n    },\n    lastUpdateTime: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['apply-suggestion'],\n  setup(props, { emit }) {\n    const directions = [\n      { key: 'north', name: '北向' },\n      { key: 'east', name: '东向' },\n      { key: 'south', name: '南向' },\n      { key: 'west', name: '西向' }\n    ]\n\n    // 获取当前帧车辆数\n    const getCurrentVehicleCount = (direction) => {\n      return props.directionStats[direction]?.vehicleCount || 0\n    }\n\n    // 获取移动平均\n    const getMovingAverage = (direction) => {\n      return props.directionStats[direction]?.movingAverage || 0\n    }\n\n    // 获取方向进度\n    const getDirectionProgress = (direction) => {\n      const stats = props.directionStats[direction]\n      if (!stats || !stats.totalFrames) return 0\n      return Math.round((stats.currentFrame || 0) / stats.totalFrames * 100)\n    }\n\n    // 获取当前帧数\n    const getCurrentFrame = (direction) => {\n      return props.directionStats[direction]?.currentFrame || 0\n    }\n\n    // 获取总帧数\n    const getTotalFrames = (direction) => {\n      return props.directionStats[direction]?.totalFrames || 0\n    }\n\n    // 获取方向状态类型\n    const getDirectionStatusType = (direction) => {\n      const status = props.directionStats[direction]?.status || 'waiting'\n      const typeMap = {\n        'waiting': 'info',\n        'processing': 'warning', \n        'completed': 'success',\n        'error': 'danger'\n      }\n      return typeMap[status] || 'info'\n    }\n\n    // 获取方向状态文本\n    const getDirectionStatusText = (direction) => {\n      const status = props.directionStats[direction]?.status || 'waiting'\n      const textMap = {\n        'waiting': '等待中',\n        'processing': '检测中',\n        'completed': '已完成', \n        'error': '检测失败'\n      }\n      return textMap[status] || '未知'\n    }\n\n    // 获取进度状态\n    const getProgressStatus = (direction) => {\n      const progress = getDirectionProgress(direction)\n      if (progress === 100) return 'success'\n      if (progress > 0) return undefined\n      return 'exception'\n    }\n\n    // 获取方向卡片样式类\n    const getDirectionCardClass = (direction) => {\n      const status = props.directionStats[direction]?.status || 'waiting'\n      return `status-${status}`\n    }\n\n    // 拥堵等级计算\n    const getCongestionLevel = () => {\n      const totalVehicles = directions.reduce((sum, dir) =>\n        sum + getCurrentVehicleCount(dir.key), 0)\n      const avgMoving = directions.reduce((sum, dir) =>\n        sum + getMovingAverage(dir.key), 0) / 4\n\n      if (avgMoving >= 8 || totalVehicles >= 20) return '严重拥堵'\n      if (avgMoving >= 5 || totalVehicles >= 12) return '中度拥堵'\n      if (avgMoving >= 3 || totalVehicles >= 6) return '轻度拥堵'\n      return '畅通'\n    }\n\n    const getCongestionLevelClass = () => {\n      const level = getCongestionLevel()\n      const classMap = {\n        '严重拥堵': 'severe',\n        '中度拥堵': 'moderate',\n        '轻度拥堵': 'light',\n        '畅通': 'smooth'\n      }\n      return classMap[level] || 'smooth'\n    }\n\n    const getCongestionDescription = () => {\n      const level = getCongestionLevel()\n      const descMap = {\n        '严重拥堵': '交通严重拥堵，建议立即采取疏导措施',\n        '中度拥堵': '交通较为拥堵，需要关注流量变化',\n        '轻度拥堵': '交通略有拥堵，保持正常监控',\n        '畅通': '交通状况良好，运行顺畅'\n      }\n      return descMap[level] || '状态未知'\n    }\n\n    const getCongestionIndex = () => {\n      const totalVehicles = directions.reduce((sum, dir) =>\n        sum + getCurrentVehicleCount(dir.key), 0)\n      const avgMoving = directions.reduce((sum, dir) =>\n        sum + getMovingAverage(dir.key), 0) / 4\n\n      // 综合计算拥堵指数 (0-100)\n      const vehicleIndex = Math.min(totalVehicles * 2.5, 50)\n      const movingIndex = Math.min(avgMoving * 6, 50)\n      return Math.round(vehicleIndex + movingIndex)\n    }\n\n    const getCongestionTrend = () => {\n      const index = getCongestionIndex()\n      if (index >= 80) return '↗️ 恶化'\n      if (index >= 60) return '→ 稳定'\n      if (index >= 40) return '↘️ 改善'\n      return '✅ 良好'\n    }\n\n    // 交通流平衡度计算\n    const getTrafficFlowBalance = () => {\n      const counts = directions.map(dir => getMovingAverage(dir.key))\n      const max = Math.max(...counts)\n      const min = Math.min(...counts)\n\n      if (max === 0) return 100\n      const balance = ((max - min) / max) * 100\n      return Math.round(100 - balance)\n    }\n\n    const getBalanceStatus = () => {\n      const balance = getTrafficFlowBalance()\n      if (balance >= 80) return '非常均衡'\n      if (balance >= 60) return '较为均衡'\n      if (balance >= 40) return '不够均衡'\n      return '严重不均衡'\n    }\n\n    const getBalanceColor = () => {\n      const balance = getTrafficFlowBalance()\n      if (balance >= 80) return '#10b981'\n      if (balance >= 60) return '#f59e0b'\n      if (balance >= 40) return '#f97316'\n      return '#ef4444'\n    }\n\n    // 管理建议生成\n    const getManagementSuggestions = () => {\n      const suggestions = []\n      const congestionLevel = getCongestionLevel()\n      const balance = getTrafficFlowBalance()\n      const totalVehicles = directions.reduce((sum, dir) =>\n        sum + getCurrentVehicleCount(dir.key), 0)\n\n      // 基于拥堵等级的建议\n      if (congestionLevel === '严重拥堵') {\n        suggestions.push({\n          id: 'severe-congestion',\n          title: '紧急疏导措施',\n          content: '检测到严重拥堵，建议立即启动应急预案，增加信号灯绿灯时长，派遣交警现场疏导。',\n          priority: 'high',\n          tagType: 'danger',\n          icon: WarnTriangleFilled,\n          iconClass: 'danger-icon',\n          action: '启动应急预案',\n          actionType: 'danger'\n        })\n      } else if (congestionLevel === '中度拥堵') {\n        suggestions.push({\n          id: 'moderate-congestion',\n          title: '优化信号配时',\n          content: '交通流量较大，建议调整信号灯配时方案，延长主要方向绿灯时间。',\n          priority: 'medium',\n          tagType: 'warning',\n          icon: Setting,\n          iconClass: 'warning-icon',\n          action: '调整配时',\n          actionType: 'warning'\n        })\n      }\n\n      // 基于流量平衡的建议\n      if (balance < 60) {\n        suggestions.push({\n          id: 'flow-imbalance',\n          title: '流量均衡优化',\n          content: '各方向流量不均衡，建议动态调整信号配时，优化交通流分配。',\n          priority: 'medium',\n          tagType: 'warning',\n          icon: TrendCharts,\n          iconClass: 'warning-icon',\n          action: '均衡流量',\n          actionType: 'primary'\n        })\n      }\n\n      // 基于总体流量的建议\n      if (totalVehicles > 15) {\n        suggestions.push({\n          id: 'high-volume',\n          title: '高峰期管理',\n          content: '当前处于交通高峰期，建议启动高峰期管理模式，加强现场监控。',\n          priority: 'medium',\n          tagType: 'info',\n          icon: Clock,\n          iconClass: 'info-icon',\n          action: '启动高峰模式',\n          actionType: 'primary'\n        })\n      }\n\n      // 如果没有问题，给出正面建议\n      if (suggestions.length === 0) {\n        suggestions.push({\n          id: 'normal-operation',\n          title: '运行状态良好',\n          content: '当前交通状况良好，建议保持现有信号配时方案，继续监控。',\n          priority: 'low',\n          tagType: 'success',\n          icon: SuccessFilled,\n          iconClass: 'success-icon',\n          action: '保持监控',\n          actionType: 'success'\n        })\n      }\n\n      return suggestions\n    }\n\n    // 应用建议\n    const applySuggestion = (suggestion) => {\n      emit('apply-suggestion', suggestion)\n    }\n\n    return {\n      directions,\n      getCurrentVehicleCount,\n      getMovingAverage,\n      getDirectionProgress,\n      getCurrentFrame,\n      getTotalFrames,\n      getDirectionStatusType,\n      getDirectionStatusText,\n      getProgressStatus,\n      getDirectionCardClass,\n      getCongestionLevel,\n      getCongestionLevelClass,\n      getCongestionDescription,\n      getCongestionIndex,\n      getCongestionTrend,\n      getTrafficFlowBalance,\n      getBalanceStatus,\n      getBalanceColor,\n      getManagementSuggestions,\n      applySuggestion\n    }\n  }\n}\n</script>\n\n<style scoped>\n.intelligent-traffic-status-panel {\n  background: #ffffff;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 2px solid #f0f2f5;\n}\n\n.panel-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.section-title {\n  margin: 0 0 16px 0;\n  font-size: 16px;\n  font-weight: 500;\n  color: #374151;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.realtime-vehicle-section {\n  margin-bottom: 32px;\n}\n\n.vehicle-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 16px;\n}\n\n@media (max-width: 768px) {\n  .vehicle-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .congestion-grid {\n    grid-template-columns: 1fr !important;\n  }\n}\n\n.vehicle-card {\n  background: #f9fafb;\n  border-radius: 8px;\n  padding: 16px;\n  border: 2px solid #e5e7eb;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  transform: translateY(0);\n}\n\n.vehicle-card:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);\n  transform: translateY(-2px);\n}\n\n.vehicle-card.status-processing {\n  border-color: #f59e0b;\n  background: #fffbeb;\n}\n\n.vehicle-card.status-completed {\n  border-color: #10b981;\n  background: #ecfdf5;\n}\n\n.vehicle-card.status-error {\n  border-color: #ef4444;\n  background: #fef2f2;\n}\n\n.direction-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.direction-name {\n  font-weight: 500;\n  color: #374151;\n}\n\n.vehicle-metrics {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.metric-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.metric-item.primary .metric-value {\n  font-size: 24px;\n  font-weight: 700;\n  color: #1f2937;\n  transition: all 0.3s ease;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n}\n\n.metric-item.primary .metric-value:hover {\n  color: #3b82f6;\n}\n\n.metric-item.secondary .metric-value {\n  font-size: 16px;\n  font-weight: 500;\n  color: #6b7280;\n}\n\n.metric-label {\n  font-size: 12px;\n  color: #9ca3af;\n}\n\n.metric-item.progress {\n  flex-direction: column;\n  align-items: stretch;\n  gap: 4px;\n}\n\n.congestion-analysis-section {\n  margin-bottom: 32px;\n}\n\n.congestion-grid {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 24px;\n}\n\n.congestion-overview {\n  background: #f9fafb;\n  border-radius: 8px;\n  padding: 20px;\n}\n\n.congestion-level {\n  text-align: center;\n  margin-bottom: 16px;\n}\n\n.congestion-level.severe .level-indicator {\n  color: #ef4444;\n}\n\n.congestion-level.moderate .level-indicator {\n  color: #f59e0b;\n}\n\n.congestion-level.light .level-indicator {\n  color: #f97316;\n}\n\n.congestion-level.smooth .level-indicator {\n  color: #10b981;\n}\n\n.level-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  font-size: 20px;\n  font-weight: 600;\n  margin-bottom: 8px;\n}\n\n.level-description {\n  font-size: 14px;\n  color: #6b7280;\n}\n\n.congestion-metrics {\n  display: flex;\n  justify-content: space-around;\n}\n\n.metric {\n  text-align: center;\n}\n\n.metric .metric-label {\n  display: block;\n  font-size: 12px;\n  color: #9ca3af;\n  margin-bottom: 4px;\n}\n\n.metric .metric-value {\n  font-size: 16px;\n  font-weight: 500;\n  color: #374151;\n}\n\n.flow-balance {\n  background: #f9fafb;\n  border-radius: 8px;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16px;\n}\n\n.balance-indicator {\n  text-align: center;\n}\n\n.balance-title {\n  font-size: 14px;\n  color: #6b7280;\n  margin-bottom: 8px;\n}\n\n.balance-value {\n  font-size: 24px;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 4px;\n}\n\n.balance-status {\n  font-size: 12px;\n  color: #9ca3af;\n}\n\n.management-suggestions-section {\n  margin-bottom: 16px;\n}\n\n.suggestions-container {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.suggestion-card {\n  background: #f9fafb;\n  border-radius: 8px;\n  padding: 16px;\n  border-left: 4px solid #e5e7eb;\n}\n\n.suggestion-card.high {\n  border-left-color: #ef4444;\n  background: #fef2f2;\n}\n\n.suggestion-card.medium {\n  border-left-color: #f59e0b;\n  background: #fffbeb;\n}\n\n.suggestion-card.low {\n  border-left-color: #10b981;\n  background: #ecfdf5;\n}\n\n.suggestion-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n}\n\n.suggestion-title {\n  flex: 1;\n  font-weight: 500;\n  color: #374151;\n}\n\n.suggestion-content {\n  font-size: 14px;\n  color: #6b7280;\n  margin-bottom: 12px;\n  line-height: 1.5;\n}\n\n.suggestion-action {\n  text-align: right;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkC;;EAEtCA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAa;;EAIlBA,KAAK,EAAC;AAAa;;EAQrBA,KAAK,EAAC;AAA0B;;EAE9BA,KAAK,EAAC;AAAc;;EAOhBA,KAAK,EAAC;AAAkB;;EACrBA,KAAK,EAAC;AAAgB;;EASzBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAc;;EAKtBA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAc;;EAKtBA,KAAK,EAAC;AAAsB;;EAO1BA,KAAK,EAAC;AAAc;;EAW9BA,KAAK,EAAC;AAA6B;;EAEjCA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAiB;;EAIvBA,KAAK,EAAC;AAAmB;;EAG3BA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAQ;;EAEXA,KAAK,EAAC;AAAc;;EAEvBA,KAAK,EAAC;AAAQ;;EAEXA,KAAK,EAAC;AAAoB;;EAKjCA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAgB;;EAe9BA,KAAK,EAAC;AAAgC;;EAEpCA,KAAK,EAAC;AAAuB;;EAOzBA,KAAK,EAAC;AAAmB;;EAItBA,KAAK,EAAC;AAAkB;;EAK3BA,KAAK,EAAC;AAAoB;;EAhIzCC,GAAA;EAiIeD,KAAK,EAAC;;;;;;;;;uBAhInBE,mBAAA,CA4IM,OA5INC,UA4IM,GA3IJC,mBAAA,UAAa,EACbC,mBAAA,CAUM,OAVNC,UAUM,GATJD,mBAAA,CAGK,MAHLE,UAGK,GAFHC,YAAA,CAAkCC,kBAAA;IAL1CC,OAAA,EAAAC,QAAA,CAKiB,MAAe,CAAfH,YAAA,CAAeI,sBAAA,E;IALhCC,CAAA;gCAAAC,gBAAA,CAK0C,YAEpC,G,GACAT,mBAAA,CAIM,OAJNU,UAIM,GAHUC,MAAA,CAAAC,cAAc,I,cAA5BC,YAAA,CAESC,iBAAA;IAXjBlB,GAAA;IASsCmB,IAAI,EAAC,OAAO;IAACC,IAAI,EAAC;;IATxDX,OAAA,EAAAC,QAAA,CAUU,MAAoB,CAV9BG,gBAAA,CAAAQ,gBAAA,CAUaN,MAAA,CAAAC,cAAc,iB;IAV3BJ,CAAA;QAAAT,mBAAA,e,KAeIA,mBAAA,cAAiB,EACjBC,mBAAA,CAgDM,OAhDNkB,UAgDM,G,0BA/CJlB,mBAAA,CAAqC;IAAjCL,KAAK,EAAC;EAAe,GAAC,QAAM,sBAChCK,mBAAA,CA6CM,OA7CNmB,UA6CM,I,kBA5CJtB,mBAAA,CA2CMuB,SAAA,QA9DdC,WAAA,CAoB8BC,MAAA,CAAAC,UAAU,EAAvBC,SAAS;yBADlB3B,mBAAA,CA2CM;MAzCHD,GAAG,EAAE4B,SAAS,CAAC5B,GAAG;MACnBD,KAAK,EAtBf8B,eAAA,EAsBgB,cAAc,EACZH,MAAA,CAAAI,qBAAqB,CAACF,SAAS,CAAC5B,GAAG;QAE3CI,mBAAA,CAQM,OARN2B,UAQM,GAPJ3B,mBAAA,CAAwD,QAAxD4B,UAAwD,EAAAX,gBAAA,CAAxBO,SAAS,CAACK,IAAI,kBAC9C1B,YAAA,CAKSW,iBAAA;MAJNE,IAAI,EAAEM,MAAA,CAAAQ,sBAAsB,CAACN,SAAS,CAAC5B,GAAG;MAC3CmB,IAAI,EAAC;;MA7BnBV,OAAA,EAAAC,QAAA,CA+Bc,MAA2C,CA/BzDG,gBAAA,CAAAQ,gBAAA,CA+BiBK,MAAA,CAAAS,sBAAsB,CAACP,SAAS,CAAC5B,GAAG,kB;MA/BrDY,CAAA;qDAmCUR,mBAAA,CA0BM,OA1BNgC,UA0BM,GAzBJjC,mBAAA,YAAe,EACfC,mBAAA,CAGM,OAHNiC,WAGM,GAFJjC,mBAAA,CAA2E,OAA3EkC,WAA2E,EAAAjB,gBAAA,CAA9CK,MAAA,CAAAa,sBAAsB,CAACX,SAAS,CAAC5B,GAAG,mB,0BACjEI,mBAAA,CAAqC;MAAhCL,KAAK,EAAC;IAAc,GAAC,OAAK,qB,GAGjCI,mBAAA,UAAa,EACbC,mBAAA,CAGM,OAHNoC,WAGM,GAFJpC,mBAAA,CAAqE,OAArEqC,WAAqE,EAAApB,gBAAA,CAAxCK,MAAA,CAAAgB,gBAAgB,CAACd,SAAS,CAAC5B,GAAG,mB,0BAC3DI,mBAAA,CAAoC;MAA/BL,KAAK,EAAC;IAAc,GAAC,MAAI,qB,GAGhCI,mBAAA,UAAa,EACbC,mBAAA,CAWM,OAXNuC,WAWM,GAVJpC,YAAA,CAKEqC,sBAAA;MAJCC,UAAU,EAAEnB,MAAA,CAAAoB,oBAAoB,CAAClB,SAAS,CAAC5B,GAAG;MAC9C,cAAY,EAAE,CAAC;MACf,WAAS,EAAE,KAAK;MAChB+C,MAAM,EAAErB,MAAA,CAAAsB,iBAAiB,CAACpB,SAAS,CAAC5B,GAAG;uDAE1CI,mBAAA,CAGM,OAHN6C,WAGM,EAAA5B,gBAAA,CAFDK,MAAA,CAAAoB,oBAAoB,CAAClB,SAAS,CAAC5B,GAAG,KAAI,KACxC,GAAAqB,gBAAA,CAAGK,MAAA,CAAAwB,eAAe,CAACtB,SAAS,CAAC5B,GAAG,KAAI,GAAC,GAAAqB,gBAAA,CAAGK,MAAA,CAAAyB,cAAc,CAACvB,SAAS,CAAC5B,GAAG,KAAI,IAC3E,gB;sCAOVG,mBAAA,YAAe,EACfC,mBAAA,CAwCM,OAxCNgD,WAwCM,G,0BAvCJhD,mBAAA,CAAqC;IAAjCL,KAAK,EAAC;EAAe,GAAC,QAAM,sBAChCK,mBAAA,CAqCM,OArCNiD,WAqCM,GApCJjD,mBAAA,CAmBM,OAnBNkD,WAmBM,GAlBJlD,mBAAA,CAMM;IANDL,KAAK,EAvEpB8B,eAAA,EAuEqB,kBAAkB,EAASH,MAAA,CAAA6B,uBAAuB;MAC3DnD,mBAAA,CAGM,OAHNoD,WAGM,GAFJjD,YAAA,CAA8BC,kBAAA;IAzE5CC,OAAA,EAAAC,QAAA,CAyEuB,MAAW,CAAXH,YAAA,CAAWkD,kBAAA,E;IAzElC7C,CAAA;MAAAC,gBAAA,CAyE4C,GAC9B,GAAAQ,gBAAA,CAAGK,MAAA,CAAAgC,kBAAkB,mB,GAEvBtD,mBAAA,CAAqE,OAArEuD,WAAqE,EAAAtC,gBAAA,CAAnCK,MAAA,CAAAkC,wBAAwB,mB,kBAG5DxD,mBAAA,CASM,OATNyD,WASM,GARJzD,mBAAA,CAGM,OAHN0D,WAGM,G,0BAFJ1D,mBAAA,CAAsC;IAAhCL,KAAK,EAAC;EAAc,GAAC,MAAI,sBAC/BK,mBAAA,CAA4D,QAA5D2D,WAA4D,EAAA1C,gBAAA,CAA9BK,MAAA,CAAAsC,kBAAkB,mB,GAElD5D,mBAAA,CAGM,OAHN6D,WAGM,G,0BAFJ7D,mBAAA,CAAsC;IAAhCL,KAAK,EAAC;EAAc,GAAC,MAAI,sBAC/BK,mBAAA,CAAkE,QAAlE8D,WAAkE,EAAA7C,gBAAA,CAA9BK,MAAA,CAAAyC,kBAAkB,mB,OAK5D/D,mBAAA,CAcM,OAdNgE,WAcM,GAbJhE,mBAAA,CAIM,OAJNiE,WAIM,G,0BAHJjE,mBAAA,CAAuC;IAAlCL,KAAK,EAAC;EAAe,GAAC,QAAM,sBACjCK,mBAAA,CAA+D,OAA/DkE,WAA+D,EAAAjD,gBAAA,CAAjCK,MAAA,CAAA6C,qBAAqB,MAAK,GAAC,iBACzDnE,mBAAA,CAA0D,OAA1DoE,WAA0D,EAAAnD,gBAAA,CAA3BK,MAAA,CAAA+C,gBAAgB,mB,GAGjDlE,YAAA,CAMEqC,sBAAA;IALAxB,IAAI,EAAC,QAAQ;IACZyB,UAAU,EAAEnB,MAAA,CAAA6C,qBAAqB;IACjCG,KAAK,EAAE,EAAE;IACT,cAAY,EAAE,CAAC;IACfC,KAAK,EAAEjD,MAAA,CAAAkD,eAAe;0DAM/BzE,mBAAA,YAAe,EACfC,mBAAA,CA8BM,OA9BNyE,WA8BM,G,0BA7BJzE,mBAAA,CAAqC;IAAjCL,KAAK,EAAC;EAAe,GAAC,QAAM,sBAChCK,mBAAA,CA2BM,OA3BN0E,WA2BM,I,kBA1BJ7E,mBAAA,CAyBMuB,SAAA,QA1IdC,WAAA,CAkH+BC,MAAA,CAAAqD,wBAAwB,IAAtCC,UAAU;yBADnB/E,mBAAA,CAyBM;MAvBHD,GAAG,EAAEgF,UAAU,CAACC,EAAE;MACnBlF,KAAK,EApHf8B,eAAA,EAoHgB,iBAAiB,EACfmD,UAAU,CAACE,QAAQ;QAE3B9E,mBAAA,CAQM,OARN+E,WAQM,GAPJ5E,YAAA,CAEUC,kBAAA;MAFAT,KAAK,EAxH3B8B,eAAA,CAwH6BmD,UAAU,CAACI,SAAS;;MAxHjD3E,OAAA,EAAAC,QAAA,CAyHc,MAAmC,E,cAAnCO,YAAA,CAAmCoE,wBAzHjD,CAyH8BL,UAAU,CAACM,IAAI,I;MAzH7C1E,CAAA;oDA2HYR,mBAAA,CAA4D,QAA5DmF,WAA4D,EAAAlE,gBAAA,CAA1B2D,UAAU,CAACQ,KAAK,kBAClDjF,YAAA,CAESW,iBAAA;MAFAE,IAAI,EAAE4D,UAAU,CAACS,OAAO;MAAEtE,IAAI,EAAC;;MA5HpDV,OAAA,EAAAC,QAAA,CA6Hc,MAAyB,CA7HvCG,gBAAA,CAAAQ,gBAAA,CA6HiB2D,UAAU,CAACE,QAAQ,iB;MA7HpCtE,CAAA;qDAgIUR,mBAAA,CAA8D,OAA9DsF,WAA8D,EAAArE,gBAAA,CAA3B2D,UAAU,CAACW,OAAO,kBAChBX,UAAU,CAACY,MAAM,I,cAAtD3F,mBAAA,CAQM,OARN4F,WAQM,GAPJtF,YAAA,CAMYuF,oBAAA;MALT1E,IAAI,EAAE4D,UAAU,CAACe,UAAU;MAC5B5E,IAAI,EAAC,OAAO;MACX6E,OAAK,EAAAC,MAAA,IAAEvE,MAAA,CAAAwE,eAAe,CAAClB,UAAU;;MArIhDvE,OAAA,EAAAC,QAAA,CAuIc,MAAuB,CAvIrCG,gBAAA,CAAAQ,gBAAA,CAuIiB2D,UAAU,CAACY,MAAM,iB;MAvIlChF,CAAA;kEAAAT,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}