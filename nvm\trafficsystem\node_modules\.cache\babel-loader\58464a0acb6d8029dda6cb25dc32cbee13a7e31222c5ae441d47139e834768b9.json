{"ast": null, "code": "import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';\nimport { useRouter, useRoute } from 'vue-router';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Grid, Upload, VideoCamera, DataAnalysis, Document, Plus, MoreFilled, Refresh, InfoFilled, Loading } from '@element-plus/icons-vue';\n\n// 导入组件\nimport FourWayVideoUpload from '@/components/analysis/FourWayVideoUpload.vue';\nimport FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue';\nimport TrafficAnalysisDashboard from '@/components/analysis/TrafficAnalysisDashboard.vue';\nimport IntelligentTrafficReport from '@/components/analysis/IntelligentTrafficReport.vue';\nimport StepNavigator from '@/components/analysis/StepNavigator.vue';\nexport default {\n  name: 'FourWayAnalysisConsole',\n  components: {\n    Grid,\n    Upload,\n    VideoCamera,\n    DataAnalysis,\n    Document,\n    Plus,\n    MoreFilled,\n    Refresh,\n    InfoFilled,\n    Loading,\n    FourWayVideoUpload,\n    FourWayRealtimeViewer,\n    TrafficAnalysisDashboard,\n    IntelligentTrafficReport,\n    StepNavigator\n  },\n  setup() {\n    const router = useRouter();\n    const route = useRoute();\n\n    // 响应式数据\n    const currentStep = ref(0);\n    const currentTaskId = ref('');\n    const currentTask = ref({\n      id: '',\n      name: '四方向交通分析任务',\n      status: 'waiting',\n      progress: 0,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    const reportData = ref(null);\n    const activeConnections = ref(0);\n    const lastUpdateTime = ref('');\n    const isInitializing = ref(true);\n    const taskStatusPolling = ref(null);\n\n    // 系统状态\n    const systemStatus = reactive({\n      type: 'success',\n      text: '正常运行'\n    });\n\n    // 计算属性\n    const totalTasks = computed(() => 1);\n    const activeTasks = computed(() => currentStep.value > 0 && currentStep.value < 4 ? 1 : 0);\n    const completedTasks = computed(() => currentStep.value === 4 ? 1 : 0);\n    const canUpload = computed(() => true);\n    const canDetect = computed(() => currentStep.value >= 1);\n    const canAnalyze = computed(() => currentStep.value >= 2);\n    const canExport = computed(() => currentStep.value >= 3);\n\n    // 方法\n\n    // 初始化和状态检测方法\n    const initializeFromRoute = async () => {\n      try {\n        isInitializing.value = true;\n\n        // 检查URL参数中的mode参数\n        const sessionMode = route.query.mode;\n        const urlTaskId = route.query.taskId || route.params.taskId;\n        console.log('页面初始化 - 模式:', sessionMode, '任务ID:', urlTaskId);\n\n        // 根据模式参数决定初始化策略\n        if (sessionMode === 'new') {\n          console.log('检测到新会话模式，清理之前的状态');\n          await initializeNewSession();\n        } else if (urlTaskId) {\n          console.log('从URL参数检测到任务ID:', urlTaskId);\n          currentTaskId.value = urlTaskId;\n          // 获取任务状态并设置对应步骤\n          await fetchTaskStatusAndSetStep(urlTaskId);\n        } else {\n          // 智能检测会话模式\n          await detectSessionMode();\n        }\n      } catch (error) {\n        console.error('初始化失败:', error);\n        ElMessage.error('初始化页面状态失败: ' + error.message);\n      } finally {\n        isInitializing.value = false;\n      }\n    };\n    const fetchTaskStatusAndSetStep = async taskId => {\n      try {\n        const token = localStorage.getItem('auth_token');\n        if (!token) {\n          throw new Error('未找到认证令牌');\n        }\n        const response = await fetch(`/api/video-analysis/four-way/${taskId}/status`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`获取任务状态失败: ${response.status}`);\n        }\n        const taskData = await response.json();\n        console.log('获取到任务状态:', taskData);\n\n        // 更新当前任务信息\n        currentTask.value = {\n          id: taskData.taskId || taskId,\n          name: taskData.name || '四方向交通分析任务',\n          status: taskData.status || 'waiting',\n          progress: taskData.progress || 0,\n          createdAt: taskData.createdAt ? new Date(taskData.createdAt) : new Date(),\n          updatedAt: taskData.updatedAt ? new Date(taskData.updatedAt) : new Date()\n        };\n\n        // 根据任务状态设置对应步骤\n        setStepByTaskStatus(taskData.status, taskData.progress);\n\n        // 如果任务正在处理中，开始轮询状态\n        if (['queued', 'processing'].includes(taskData.status)) {\n          startTaskStatusPolling(taskId);\n        }\n      } catch (error) {\n        console.error('获取任务状态失败:', error);\n        ElMessage.warning('无法获取任务状态，将从上传步骤开始');\n        currentStep.value = 0;\n      }\n    };\n    const setStepByTaskStatus = (status, progress = 0) => {\n      console.log('根据任务状态设置步骤:', {\n        status,\n        progress\n      });\n      switch (status) {\n        case 'queued':\n        case 'uploading':\n          currentStep.value = 0; // 上传步骤\n          ElMessage.info('任务正在排队中，请等待处理');\n          break;\n        case 'processing':\n          if (progress < 50) {\n            currentStep.value = 1; // 实时检测步骤\n            ElMessage.info('任务正在进行实时检测');\n          } else if (progress < 90) {\n            currentStep.value = 1; // 仍在检测阶段\n            ElMessage.info('实时检测进行中');\n          } else {\n            currentStep.value = 2; // 智能分析步骤\n            ElMessage.info('正在进行智能分析');\n          }\n          break;\n        case 'completed':\n          currentStep.value = 2; // 跳转到智能分析步骤\n          ElMessage.success('任务已完成，可以查看分析结果');\n          break;\n        case 'failed':\n          currentStep.value = 0; // 回到上传步骤\n          ElMessage.error('任务处理失败，请重新上传');\n          break;\n        default:\n          currentStep.value = 0; // 默认从上传开始\n          break;\n      }\n    };\n\n    // 智能会话检测机制\n    const detectSessionMode = async () => {\n      try {\n        // 检查会话存储中是否有活跃任务\n        const sessionData = sessionStorage.getItem('fourWayActiveTask');\n        if (sessionData) {\n          const taskInfo = JSON.parse(sessionData);\n          const sessionAge = Date.now() - taskInfo.timestamp;\n          const maxSessionAge = 24 * 60 * 60 * 1000; // 24小时\n\n          console.log('检测到会话数据:', taskInfo, '会话年龄:', sessionAge / 1000 / 60, '分钟');\n\n          // 如果会话数据过期，清理并开始新会话\n          if (sessionAge > maxSessionAge) {\n            console.log('会话数据已过期，开始新会话');\n            await initializeNewSession();\n            return;\n          }\n\n          // 显示用户选择界面：继续任务 vs 开始新任务\n          await showSessionChoiceDialog(taskInfo);\n        } else {\n          // 没有活跃任务，开始新会话\n          console.log('没有检测到活跃任务，开始新会话');\n          await initializeNewSession();\n        }\n      } catch (error) {\n        console.error('会话检测失败:', error);\n        await initializeNewSession();\n      }\n    };\n\n    // 初始化新会话\n    const initializeNewSession = async () => {\n      try {\n        console.log('初始化新会话');\n\n        // 清理之前的会话数据\n        await clearPreviousSession();\n\n        // 重置页面状态\n        currentStep.value = 0;\n        currentTaskId.value = null;\n        currentTask.value = {};\n        reportData.value = null;\n\n        // 停止任何正在进行的轮询\n        stopTaskStatusPolling();\n\n        // 清理URL参数中的taskId\n        if (route.query.taskId) {\n          router.replace({\n            path: route.path,\n            query: {\n              ...route.query,\n              taskId: undefined\n            }\n          });\n        }\n        ElMessage.success('已开始新的分析会话');\n      } catch (error) {\n        console.error('初始化新会话失败:', error);\n        ElMessage.error('初始化新会话失败: ' + error.message);\n      }\n    };\n\n    // 清理之前的会话\n    const clearPreviousSession = async () => {\n      try {\n        console.log('清理之前的会话数据');\n\n        // 清理会话存储\n        sessionStorage.removeItem('fourWayActiveTask');\n\n        // 清理其他相关的存储数据\n        const keysToRemove = ['uploadState', 'fourWayProgress', 'fourWayResults'];\n        keysToRemove.forEach(key => {\n          try {\n            sessionStorage.removeItem(key);\n          } catch (e) {\n            console.warn(`清理存储键 ${key} 失败:`, e);\n          }\n        });\n      } catch (error) {\n        console.error('清理会话数据失败:', error);\n      }\n    };\n\n    // 显示会话选择对话框\n    const showSessionChoiceDialog = async taskInfo => {\n      try {\n        const result = await ElMessageBox.confirm(`检测到您有一个未完成的四方向分析任务（任务ID: ${taskInfo.taskId}）。您希望：`, '会话选择', {\n          confirmButtonText: '继续之前的任务',\n          cancelButtonText: '开始新的分析',\n          type: 'question',\n          distinguishCancelAndClose: true,\n          closeOnClickModal: false,\n          closeOnPressEscape: false,\n          showClose: false,\n          customClass: 'session-choice-dialog'\n        });\n\n        // 用户选择继续之前的任务\n        if (result === 'confirm') {\n          console.log('用户选择继续之前的任务');\n          currentTaskId.value = taskInfo.taskId;\n          await fetchTaskStatusAndSetStep(taskInfo.taskId);\n          ElMessage.info('已恢复之前的分析任务');\n        }\n      } catch (action) {\n        // 用户选择开始新分析或关闭对话框\n        if (action === 'cancel') {\n          console.log('用户选择开始新的分析');\n          await initializeNewSession();\n        } else {\n          // 用户关闭对话框，默认开始新会话\n          console.log('用户关闭对话框，开始新会话');\n          await initializeNewSession();\n        }\n      }\n    };\n    const checkActiveTask = async () => {\n      // 这个方法现在被 detectSessionMode 替代，保留用于向后兼容\n      await detectSessionMode();\n    };\n    const startTaskStatusPolling = taskId => {\n      // 清除现有的轮询\n      if (taskStatusPolling.value) {\n        clearInterval(taskStatusPolling.value);\n      }\n      console.log('开始轮询任务状态:', taskId);\n      taskStatusPolling.value = setInterval(async () => {\n        try {\n          await fetchTaskStatusAndSetStep(taskId);\n\n          // 如果任务完成或失败，停止轮询\n          if (['completed', 'failed'].includes(currentTask.value.status)) {\n            clearInterval(taskStatusPolling.value);\n            taskStatusPolling.value = null;\n          }\n        } catch (error) {\n          console.error('轮询任务状态失败:', error);\n        }\n      }, 3000); // 每3秒轮询一次\n    };\n    const stopTaskStatusPolling = () => {\n      if (taskStatusPolling.value) {\n        clearInterval(taskStatusPolling.value);\n        taskStatusPolling.value = null;\n      }\n    };\n\n    // 事件处理\n    const handleUploadSuccess = response => {\n      const taskId = response.data?.taskId || response.taskId || `task_${Date.now()}`;\n      currentTaskId.value = taskId;\n\n      // 保存任务信息到会话存储\n      try {\n        sessionStorage.setItem('fourWayActiveTask', JSON.stringify({\n          taskId: taskId,\n          timestamp: Date.now()\n        }));\n      } catch (error) {\n        console.warn('保存任务信息到会话存储失败:', error);\n      }\n\n      // 更新当前任务信息\n      currentTask.value = {\n        id: taskId,\n        name: '四方向交通分析任务',\n        status: 'processing',\n        progress: 10,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n\n      // 更新URL参数但不导航\n      router.replace({\n        path: route.path,\n        query: {\n          ...route.query,\n          taskId: taskId\n        }\n      });\n      currentStep.value = 1;\n      ElMessage.success('视频上传成功，开始实时检测');\n\n      // 开始轮询任务状态\n      startTaskStatusPolling(taskId);\n    };\n    const handleUploadError = error => {\n      ElMessage.error('视频上传失败: ' + error.message);\n    };\n    const handleUploadProgress = progress => {\n      console.log('上传进度:', progress);\n    };\n    const handleUploadStatusChange = status => {\n      console.log('上传状态变化:', status);\n    };\n    const handleDetectionUpdate = data => {\n      console.log('检测更新:', data);\n\n      // 更新任务进度\n      if (currentTask.value && data.progress) {\n        currentTask.value.progress = Math.min(data.progress, 90); // 检测阶段最多90%\n        currentTask.value.updatedAt = new Date();\n      }\n    };\n    const handleDetectionStatusChange = status => {\n      if (status === 'completed') {\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed';\n          currentTask.value.progress = 100;\n          currentTask.value.updatedAt = new Date();\n        }\n        currentStep.value = 2;\n        ElMessage.success('实时检测完成，开始智能分析');\n      }\n    };\n\n    // 处理四方向分析完成事件\n    const handleAnalysisComplete = completeData => {\n      try {\n        console.log('🎉 收到四方向分析完成事件:', completeData);\n\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed';\n          currentTask.value.progress = 100;\n          currentTask.value.updatedAt = new Date();\n        }\n\n        // 显示完成提示并自动跳转\n        ElMessage({\n          message: `四方向分析完成！检测到 ${completeData.summary?.totalVehicles || 0} 辆车辆，正在跳转到智能分析模块...`,\n          type: 'success',\n          duration: 4000\n        });\n\n        // 延迟跳转到智能分析模块，给用户时间看到完成消息\n        setTimeout(() => {\n          currentStep.value = 2;\n          ElMessage.info('已自动跳转到智能分析模块');\n        }, 2000);\n      } catch (error) {\n        console.error('处理分析完成事件失败:', error);\n        ElMessage.error('处理完成事件失败，请手动跳转到智能分析');\n      }\n    };\n    const handleAnalysisDataUpdate = data => {\n      reportData.value = data;\n      currentStep.value = 3;\n      ElMessage.success('智能分析完成，可以生成报告');\n    };\n    const handleExportReport = taskId => {\n      ElMessage.success('报告导出成功');\n    };\n    const handleRefreshReportData = taskId => {\n      ElMessage.success('报告数据刷新成功');\n    };\n\n    // 快速操作\n    const goToUpload = () => {\n      currentStep.value = 0;\n    };\n    const startDetection = () => {\n      if (canDetect.value) {\n        currentStep.value = 1;\n      } else {\n        ElMessage.warning('请先上传视频文件');\n      }\n    };\n    const generateAnalysis = () => {\n      if (canAnalyze.value) {\n        currentStep.value = 2;\n      } else {\n        ElMessage.warning('请先完成视频检测');\n      }\n    };\n    const exportReport = () => {\n      if (canExport.value) {\n        currentStep.value = 3;\n      } else {\n        ElMessage.warning('请先完成智能分析');\n      }\n    };\n    const refreshSystem = () => {\n      lastUpdateTime.value = new Date().toLocaleTimeString();\n      ElMessage.success('系统状态已刷新');\n    };\n    const showSystemInfo = () => {\n      ElMessageBox.alert('四方向智能交通分析系统 v1.0.0', '系统信息', {\n        confirmButtonText: '确定'\n      });\n    };\n\n    // StepNavigator事件处理\n    const handleStepChange = stepIndex => {\n      console.log('步骤切换请求:', stepIndex);\n\n      // 检查是否可以切换到目标步骤\n      if (stepIndex <= currentStep.value) {\n        currentStep.value = stepIndex;\n        ElMessage.info(`已切换到步骤 ${stepIndex + 1}`);\n      } else {\n        ElMessage.warning('请先完成前面的步骤');\n      }\n    };\n    const handleQuickAction = actionKey => {\n      console.log('快速操作:', actionKey);\n      switch (actionKey) {\n        case 'upload':\n          goToUpload();\n          break;\n        case 'detection':\n          startDetection();\n          break;\n        case 'analysis':\n          generateAnalysis();\n          break;\n        case 'report':\n          exportReport();\n          break;\n        default:\n          console.warn('未知的快速操作:', actionKey);\n      }\n    };\n    const handleRefreshStatus = async () => {\n      if (currentTaskId.value) {\n        ElMessage.info('正在刷新任务状态...');\n        await fetchTaskStatusAndSetStep(currentTaskId.value);\n      } else {\n        ElMessage.warning('没有活跃的任务');\n      }\n    };\n\n    // 任务状态辅助方法\n    const getTaskStatusType = status => {\n      const statusMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'failed': 'danger',\n        'paused': 'info'\n      };\n      return statusMap[status] || 'info';\n    };\n    const getTaskStatusText = status => {\n      const statusMap = {\n        'waiting': '等待中',\n        'processing': '处理中',\n        'completed': '已完成',\n        'failed': '失败',\n        'paused': '已暂停'\n      };\n      return statusMap[status] || '未知';\n    };\n    const getProgressStatus = status => {\n      if (status === 'completed') return 'success';\n      if (status === 'failed') return 'exception';\n      return null;\n    };\n    const formatTime = time => {\n      if (!time) return '-';\n      return new Date(time).toLocaleString();\n    };\n    const getProcessingDuration = task => {\n      if (!task || !task.createdAt) return '-';\n      const start = new Date(task.createdAt);\n      const end = task.updatedAt ? new Date(task.updatedAt) : new Date();\n      const duration = Math.floor((end - start) / 1000);\n      if (duration < 60) return `${duration}秒`;\n      if (duration < 3600) return `${Math.floor(duration / 60)}分钟`;\n      return `${Math.floor(duration / 3600)}小时`;\n    };\n\n    // 监听路由变化\n    watch(() => route.query.taskId, newTaskId => {\n      if (newTaskId && newTaskId !== currentTaskId.value) {\n        console.log('检测到URL中的taskId变化:', newTaskId);\n        currentTaskId.value = newTaskId;\n        fetchTaskStatusAndSetStep(newTaskId);\n      }\n    });\n\n    // 生命周期\n    onMounted(async () => {\n      // 初始化系统状态\n      lastUpdateTime.value = new Date().toLocaleTimeString();\n      activeConnections.value = 1;\n\n      // 初始化页面状态\n      await initializeFromRoute();\n    });\n    onUnmounted(() => {\n      // 清理轮询\n      stopTaskStatusPolling();\n    });\n    return {\n      // 图标组件\n      Upload,\n      VideoCamera,\n      DataAnalysis,\n      Document,\n      Plus,\n      MoreFilled,\n      Refresh,\n      InfoFilled,\n      // 响应式数据\n      currentStep,\n      currentTaskId,\n      currentTask,\n      reportData,\n      activeConnections,\n      lastUpdateTime,\n      systemStatus,\n      isInitializing,\n      // 计算属性\n      totalTasks,\n      activeTasks,\n      completedTasks,\n      canUpload,\n      canDetect,\n      canAnalyze,\n      canExport,\n      // 方法\n      initializeFromRoute,\n      fetchTaskStatusAndSetStep,\n      setStepByTaskStatus,\n      checkActiveTask,\n      startTaskStatusPolling,\n      stopTaskStatusPolling,\n      handleUploadSuccess,\n      handleUploadError,\n      handleUploadProgress,\n      handleUploadStatusChange,\n      handleDetectionUpdate,\n      handleDetectionStatusChange,\n      handleAnalysisComplete,\n      handleAnalysisDataUpdate,\n      handleExportReport,\n      handleRefreshReportData,\n      goToUpload,\n      startDetection,\n      generateAnalysis,\n      exportReport,\n      refreshSystem,\n      showSystemInfo,\n      handleStepChange,\n      handleQuickAction,\n      handleRefreshStatus,\n      // 任务状态辅助方法\n      getTaskStatusType,\n      getTaskStatusText,\n      getProgressStatus,\n      formatTime,\n      getProcessingDuration\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "onUnmounted", "watch", "useRouter", "useRoute", "ElMessage", "ElMessageBox", "Grid", "Upload", "VideoCamera", "DataAnalysis", "Document", "Plus", "MoreFilled", "Refresh", "InfoFilled", "Loading", "FourWayVideoUpload", "FourWayRealtimeViewer", "TrafficAnalysisDashboard", "IntelligentTrafficReport", "StepNavigator", "name", "components", "setup", "router", "route", "currentStep", "currentTaskId", "currentTask", "id", "status", "progress", "createdAt", "Date", "updatedAt", "reportData", "activeConnections", "lastUpdateTime", "isInitializing", "taskStatusPolling", "systemStatus", "type", "text", "totalTasks", "activeTasks", "value", "completedTasks", "canUpload", "canDetect", "canAnalyze", "canExport", "initializeFromRoute", "sessionMode", "query", "mode", "urlTaskId", "taskId", "params", "console", "log", "initializeNewSession", "fetchTaskStatusAndSetStep", "detectSessionMode", "error", "message", "token", "localStorage", "getItem", "Error", "response", "fetch", "headers", "ok", "taskData", "json", "setStepByTaskStatus", "includes", "startTaskStatusPolling", "warning", "info", "success", "sessionData", "sessionStorage", "taskInfo", "JSON", "parse", "sessionAge", "now", "timestamp", "maxSessionAge", "showSessionChoiceDialog", "clearPreviousSession", "stopTaskStatusPolling", "replace", "path", "undefined", "removeItem", "keysToRemove", "for<PERSON>ach", "key", "e", "warn", "result", "confirm", "confirmButtonText", "cancelButtonText", "distinguishCancelAndClose", "closeOnClickModal", "closeOnPressEscape", "showClose", "customClass", "action", "checkActiveTask", "clearInterval", "setInterval", "handleUploadSuccess", "data", "setItem", "stringify", "handleUploadError", "handleUploadProgress", "handleUploadStatusChange", "handleDetectionUpdate", "Math", "min", "handleDetectionStatusChange", "handleAnalysisComplete", "completeData", "summary", "totalVehicles", "duration", "setTimeout", "handleAnalysisDataUpdate", "handleExportReport", "handleRefreshReportData", "goToUpload", "startDetection", "generateAnalysis", "exportReport", "refreshSystem", "toLocaleTimeString", "showSystemInfo", "alert", "handleStepChange", "stepIndex", "handleQuickAction", "action<PERSON>ey", "handleRefreshStatus", "getTaskStatusType", "statusMap", "getTaskStatusText", "getProgressStatus", "formatTime", "time", "toLocaleString", "getProcessingDuration", "task", "start", "end", "floor", "newTaskId"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\FourWayAnalysisConsole.vue"], "sourcesContent": ["<template>\n  <div class=\"four-way-analysis-console\">\n    <!-- 页面头部 -->\n    <div class=\"console-header\">\n      <div class=\"header-content\">\n        <h1 class=\"console-title\">\n          <el-icon><Grid /></el-icon>\n          四方向智能交通分析控制台\n        </h1>\n        <p class=\"console-description\">\n          集成视频上传、实时检测、智能分析和报告生成的一站式交通分析平台\n        </p>\n      </div>\n      \n      <div class=\"header-stats\">\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ totalTasks }}</div>\n          <div class=\"stat-label\">总任务数</div>\n        </div>\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ activeTasks }}</div>\n          <div class=\"stat-label\">活跃任务</div>\n        </div>\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ completedTasks }}</div>\n          <div class=\"stat-label\">已完成</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 工作流程导航 -->\n    <div class=\"workflow-navigation\">\n      <el-steps :active=\"currentStep\" align-center>\n        <el-step \n          title=\"视频上传\" \n          description=\"上传四方向视频文件\"\n          icon=\"Upload\"\n        />\n        <el-step \n          title=\"实时检测\" \n          description=\"AI模型实时分析\"\n          icon=\"VideoCamera\"\n        />\n        <el-step \n          title=\"智能分析\" \n          description=\"生成分析结果\"\n          icon=\"DataAnalysis\"\n        />\n        <el-step \n          title=\"报告生成\" \n          description=\"导出分析报告\"\n          icon=\"Document\"\n        />\n      </el-steps>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"console-content\">\n      <!-- 左侧面板 -->\n      <div class=\"left-panel\">\n        <!-- 步骤导航器 -->\n        <StepNavigator\n          :current-step=\"currentStep\"\n          :task-status=\"currentTask.status\"\n          :task-progress=\"currentTask.progress\"\n          :loading=\"isInitializing\"\n          :allow-navigation=\"true\"\n          @step-change=\"handleStepChange\"\n          @action=\"handleQuickAction\"\n          @refresh=\"handleRefreshStatus\"\n        />\n      </div>\n\n      <!-- 右侧主内容 -->\n      <div class=\"main-content\">\n        <!-- 当前任务信息 -->\n        <div v-if=\"currentTask\" class=\"current-task-info\">\n          <el-card>\n            <template #header>\n              <div class=\"task-header\">\n                <div class=\"task-title-section\">\n                  <h3>{{ currentTask.name }}</h3>\n                  <el-tag :type=\"getTaskStatusType(currentTask.status)\">\n                    {{ getTaskStatusText(currentTask.status) }}\n                  </el-tag>\n                </div>\n                <div class=\"task-progress-section\">\n                  <el-progress \n                    :percentage=\"currentTask.progress\" \n                    :status=\"getProgressStatus(currentTask.status)\"\n                    :stroke-width=\"8\"\n                  />\n                  <span class=\"progress-text\">{{ currentTask.progress }}%</span>\n                </div>\n              </div>\n            </template>\n            \n            <div class=\"task-details\">\n              <el-row :gutter=\"20\">\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">任务ID:</span>\n                    <span class=\"detail-value\">{{ currentTask.id }}</span>\n                  </div>\n                </el-col>\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">创建时间:</span>\n                    <span class=\"detail-value\">{{ formatTime(currentTask.createdAt) }}</span>\n                  </div>\n                </el-col>\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">处理时长:</span>\n                    <span class=\"detail-value\">{{ getProcessingDuration(currentTask) }}</span>\n                  </div>\n                </el-col>\n              </el-row>\n            </div>\n          </el-card>\n        </div>\n\n        <!-- 动态内容区域 -->\n        <div class=\"dynamic-content\">\n          <!-- 初始化加载状态 -->\n          <div v-if=\"isInitializing\" class=\"loading-container\">\n            <el-skeleton :rows=\"8\" animated>\n              <template #template>\n                <div class=\"loading-content\">\n                  <el-skeleton-item variant=\"h1\" style=\"width: 40%; margin-bottom: 20px;\" />\n                  <el-skeleton-item variant=\"text\" style=\"width: 80%; margin-bottom: 10px;\" />\n                  <el-skeleton-item variant=\"text\" style=\"width: 60%; margin-bottom: 20px;\" />\n                  <el-skeleton-item variant=\"rect\" style=\"width: 100%; height: 200px;\" />\n                </div>\n              </template>\n            </el-skeleton>\n            <div class=\"loading-text\">\n              <el-icon class=\"loading-icon\"><Loading /></el-icon>\n              正在初始化页面状态...\n            </div>\n          </div>\n\n          <!-- 步骤1: 视频上传 -->\n          <div v-else-if=\"currentStep === 0\" class=\"step-content\">\n            <FourWayVideoUpload\n              @upload-success=\"handleUploadSuccess\"\n              @upload-error=\"handleUploadError\"\n              @upload-progress=\"handleUploadProgress\"\n              @status-change=\"handleUploadStatusChange\"\n            />\n          </div>\n\n          <!-- 步骤2: 实时检测 -->\n          <div v-if=\"currentStep === 1\" class=\"step-content\">\n            <FourWayRealtimeViewer\n              v-if=\"currentTaskId\"\n              :task-id=\"currentTaskId\"\n              :auto-start=\"true\"\n              @detection-update=\"handleDetectionUpdate\"\n              @status-change=\"handleDetectionStatusChange\"\n              @analysis-complete=\"handleAnalysisComplete\"\n            />\n            <el-empty v-else description=\"请先上传视频文件\">\n              <el-button type=\"primary\" @click=\"currentStep = 0\">\n                返回上传\n              </el-button>\n            </el-empty>\n          </div>\n\n          <!-- 步骤3: 智能分析 -->\n          <div v-if=\"currentStep === 2\" class=\"step-content\">\n            <TrafficAnalysisDashboard\n              v-if=\"currentTaskId\"\n              :task-id=\"currentTaskId\"\n              @data-updated=\"handleAnalysisDataUpdate\"\n            />\n            <el-empty v-else description=\"请先完成视频检测\">\n              <el-button type=\"primary\" @click=\"currentStep = 1\">\n                返回检测\n              </el-button>\n            </el-empty>\n          </div>\n\n          <!-- 步骤4: 报告生成 -->\n          <div v-if=\"currentStep === 3\" class=\"step-content\">\n            <IntelligentTrafficReport\n              v-if=\"currentTaskId && reportData\"\n              :task-id=\"currentTaskId\"\n              :report-data=\"reportData\"\n              @export-report=\"handleExportReport\"\n              @refresh-data=\"handleRefreshReportData\"\n            />\n            <el-empty v-else description=\"请先完成智能分析\">\n              <el-button type=\"primary\" @click=\"currentStep = 2\">\n                返回分析\n              </el-button>\n            </el-empty>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 底部状态栏 -->\n    <div class=\"console-footer\">\n      <div class=\"footer-info\">\n        <span>系统状态: </span>\n        <el-tag :type=\"systemStatus.type\" size=\"small\">{{ systemStatus.text }}</el-tag>\n        <span class=\"separator\">|</span>\n        <span>活跃连接: {{ activeConnections }}</span>\n        <span class=\"separator\">|</span>\n        <span>最后更新: {{ lastUpdateTime }}</span>\n      </div>\n      \n      <div class=\"footer-actions\">\n        <el-button size=\"small\" @click=\"refreshSystem\">\n          <el-icon><Refresh /></el-icon>\n          刷新\n        </el-button>\n        <el-button size=\"small\" @click=\"showSystemInfo\">\n          <el-icon><InfoFilled /></el-icon>\n          系统信息\n        </el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'\nimport { useRouter, useRoute } from 'vue-router'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport {\n  Grid, Upload, VideoCamera, DataAnalysis, Document, Plus,\n  MoreFilled, Refresh, InfoFilled, Loading\n} from '@element-plus/icons-vue'\n\n// 导入组件\nimport FourWayVideoUpload from '@/components/analysis/FourWayVideoUpload.vue'\nimport FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue'\nimport TrafficAnalysisDashboard from '@/components/analysis/TrafficAnalysisDashboard.vue'\nimport IntelligentTrafficReport from '@/components/analysis/IntelligentTrafficReport.vue'\nimport StepNavigator from '@/components/analysis/StepNavigator.vue'\n\nexport default {\n  name: 'FourWayAnalysisConsole',\n  components: {\n    Grid, Upload, VideoCamera, DataAnalysis, Document, Plus,\n    MoreFilled, Refresh, InfoFilled, Loading,\n    FourWayVideoUpload,\n    FourWayRealtimeViewer,\n    TrafficAnalysisDashboard,\n    IntelligentTrafficReport,\n    StepNavigator\n  },\n  setup() {\n    const router = useRouter()\n    const route = useRoute()\n    \n    // 响应式数据\n    const currentStep = ref(0)\n    const currentTaskId = ref('')\n    const currentTask = ref({\n      id: '',\n      name: '四方向交通分析任务',\n      status: 'waiting',\n      progress: 0,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    })\n    const reportData = ref(null)\n    const activeConnections = ref(0)\n    const lastUpdateTime = ref('')\n    const isInitializing = ref(true)\n    const taskStatusPolling = ref(null)\n\n    // 系统状态\n    const systemStatus = reactive({\n      type: 'success',\n      text: '正常运行'\n    })\n\n    // 计算属性\n    const totalTasks = computed(() => 1)\n    const activeTasks = computed(() => currentStep.value > 0 && currentStep.value < 4 ? 1 : 0)\n    const completedTasks = computed(() => currentStep.value === 4 ? 1 : 0)\n\n    const canUpload = computed(() => true)\n    const canDetect = computed(() => currentStep.value >= 1)\n    const canAnalyze = computed(() => currentStep.value >= 2)\n    const canExport = computed(() => currentStep.value >= 3)\n    \n    // 方法\n\n    // 初始化和状态检测方法\n    const initializeFromRoute = async () => {\n      try {\n        isInitializing.value = true\n\n        // 检查URL参数中的mode参数\n        const sessionMode = route.query.mode\n        const urlTaskId = route.query.taskId || route.params.taskId\n\n        console.log('页面初始化 - 模式:', sessionMode, '任务ID:', urlTaskId)\n\n        // 根据模式参数决定初始化策略\n        if (sessionMode === 'new') {\n          console.log('检测到新会话模式，清理之前的状态')\n          await initializeNewSession()\n        } else if (urlTaskId) {\n          console.log('从URL参数检测到任务ID:', urlTaskId)\n          currentTaskId.value = urlTaskId\n          // 获取任务状态并设置对应步骤\n          await fetchTaskStatusAndSetStep(urlTaskId)\n        } else {\n          // 智能检测会话模式\n          await detectSessionMode()\n        }\n\n      } catch (error) {\n        console.error('初始化失败:', error)\n        ElMessage.error('初始化页面状态失败: ' + error.message)\n      } finally {\n        isInitializing.value = false\n      }\n    }\n\n    const fetchTaskStatusAndSetStep = async (taskId) => {\n      try {\n        const token = localStorage.getItem('auth_token')\n        if (!token) {\n          throw new Error('未找到认证令牌')\n        }\n\n        const response = await fetch(`/api/video-analysis/four-way/${taskId}/status`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`获取任务状态失败: ${response.status}`)\n        }\n\n        const taskData = await response.json()\n        console.log('获取到任务状态:', taskData)\n\n        // 更新当前任务信息\n        currentTask.value = {\n          id: taskData.taskId || taskId,\n          name: taskData.name || '四方向交通分析任务',\n          status: taskData.status || 'waiting',\n          progress: taskData.progress || 0,\n          createdAt: taskData.createdAt ? new Date(taskData.createdAt) : new Date(),\n          updatedAt: taskData.updatedAt ? new Date(taskData.updatedAt) : new Date()\n        }\n\n        // 根据任务状态设置对应步骤\n        setStepByTaskStatus(taskData.status, taskData.progress)\n\n        // 如果任务正在处理中，开始轮询状态\n        if (['queued', 'processing'].includes(taskData.status)) {\n          startTaskStatusPolling(taskId)\n        }\n\n      } catch (error) {\n        console.error('获取任务状态失败:', error)\n        ElMessage.warning('无法获取任务状态，将从上传步骤开始')\n        currentStep.value = 0\n      }\n    }\n\n    const setStepByTaskStatus = (status, progress = 0) => {\n      console.log('根据任务状态设置步骤:', { status, progress })\n\n      switch (status) {\n        case 'queued':\n        case 'uploading':\n          currentStep.value = 0 // 上传步骤\n          ElMessage.info('任务正在排队中，请等待处理')\n          break\n\n        case 'processing':\n          if (progress < 50) {\n            currentStep.value = 1 // 实时检测步骤\n            ElMessage.info('任务正在进行实时检测')\n          } else if (progress < 90) {\n            currentStep.value = 1 // 仍在检测阶段\n            ElMessage.info('实时检测进行中')\n          } else {\n            currentStep.value = 2 // 智能分析步骤\n            ElMessage.info('正在进行智能分析')\n          }\n          break\n\n        case 'completed':\n          currentStep.value = 2 // 跳转到智能分析步骤\n          ElMessage.success('任务已完成，可以查看分析结果')\n          break\n\n        case 'failed':\n          currentStep.value = 0 // 回到上传步骤\n          ElMessage.error('任务处理失败，请重新上传')\n          break\n\n        default:\n          currentStep.value = 0 // 默认从上传开始\n          break\n      }\n    }\n\n    // 智能会话检测机制\n    const detectSessionMode = async () => {\n      try {\n        // 检查会话存储中是否有活跃任务\n        const sessionData = sessionStorage.getItem('fourWayActiveTask')\n        if (sessionData) {\n          const taskInfo = JSON.parse(sessionData)\n          const sessionAge = Date.now() - taskInfo.timestamp\n          const maxSessionAge = 24 * 60 * 60 * 1000 // 24小时\n\n          console.log('检测到会话数据:', taskInfo, '会话年龄:', sessionAge / 1000 / 60, '分钟')\n\n          // 如果会话数据过期，清理并开始新会话\n          if (sessionAge > maxSessionAge) {\n            console.log('会话数据已过期，开始新会话')\n            await initializeNewSession()\n            return\n          }\n\n          // 显示用户选择界面：继续任务 vs 开始新任务\n          await showSessionChoiceDialog(taskInfo)\n        } else {\n          // 没有活跃任务，开始新会话\n          console.log('没有检测到活跃任务，开始新会话')\n          await initializeNewSession()\n        }\n\n      } catch (error) {\n        console.error('会话检测失败:', error)\n        await initializeNewSession()\n      }\n    }\n\n    // 初始化新会话\n    const initializeNewSession = async () => {\n      try {\n        console.log('初始化新会话')\n\n        // 清理之前的会话数据\n        await clearPreviousSession()\n\n        // 重置页面状态\n        currentStep.value = 0\n        currentTaskId.value = null\n        currentTask.value = {}\n        reportData.value = null\n\n        // 停止任何正在进行的轮询\n        stopTaskStatusPolling()\n\n        // 清理URL参数中的taskId\n        if (route.query.taskId) {\n          router.replace({\n            path: route.path,\n            query: { ...route.query, taskId: undefined }\n          })\n        }\n\n        ElMessage.success('已开始新的分析会话')\n\n      } catch (error) {\n        console.error('初始化新会话失败:', error)\n        ElMessage.error('初始化新会话失败: ' + error.message)\n      }\n    }\n\n    // 清理之前的会话\n    const clearPreviousSession = async () => {\n      try {\n        console.log('清理之前的会话数据')\n\n        // 清理会话存储\n        sessionStorage.removeItem('fourWayActiveTask')\n\n        // 清理其他相关的存储数据\n        const keysToRemove = ['uploadState', 'fourWayProgress', 'fourWayResults']\n        keysToRemove.forEach(key => {\n          try {\n            sessionStorage.removeItem(key)\n          } catch (e) {\n            console.warn(`清理存储键 ${key} 失败:`, e)\n          }\n        })\n\n      } catch (error) {\n        console.error('清理会话数据失败:', error)\n      }\n    }\n\n    // 显示会话选择对话框\n    const showSessionChoiceDialog = async (taskInfo) => {\n      try {\n        const result = await ElMessageBox.confirm(\n          `检测到您有一个未完成的四方向分析任务（任务ID: ${taskInfo.taskId}）。您希望：`,\n          '会话选择',\n          {\n            confirmButtonText: '继续之前的任务',\n            cancelButtonText: '开始新的分析',\n            type: 'question',\n            distinguishCancelAndClose: true,\n            closeOnClickModal: false,\n            closeOnPressEscape: false,\n            showClose: false,\n            customClass: 'session-choice-dialog'\n          }\n        )\n\n        // 用户选择继续之前的任务\n        if (result === 'confirm') {\n          console.log('用户选择继续之前的任务')\n          currentTaskId.value = taskInfo.taskId\n          await fetchTaskStatusAndSetStep(taskInfo.taskId)\n          ElMessage.info('已恢复之前的分析任务')\n        }\n\n      } catch (action) {\n        // 用户选择开始新分析或关闭对话框\n        if (action === 'cancel') {\n          console.log('用户选择开始新的分析')\n          await initializeNewSession()\n        } else {\n          // 用户关闭对话框，默认开始新会话\n          console.log('用户关闭对话框，开始新会话')\n          await initializeNewSession()\n        }\n      }\n    }\n\n    const checkActiveTask = async () => {\n      // 这个方法现在被 detectSessionMode 替代，保留用于向后兼容\n      await detectSessionMode()\n    }\n\n    const startTaskStatusPolling = (taskId) => {\n      // 清除现有的轮询\n      if (taskStatusPolling.value) {\n        clearInterval(taskStatusPolling.value)\n      }\n\n      console.log('开始轮询任务状态:', taskId)\n\n      taskStatusPolling.value = setInterval(async () => {\n        try {\n          await fetchTaskStatusAndSetStep(taskId)\n\n          // 如果任务完成或失败，停止轮询\n          if (['completed', 'failed'].includes(currentTask.value.status)) {\n            clearInterval(taskStatusPolling.value)\n            taskStatusPolling.value = null\n          }\n\n        } catch (error) {\n          console.error('轮询任务状态失败:', error)\n        }\n      }, 3000) // 每3秒轮询一次\n    }\n\n    const stopTaskStatusPolling = () => {\n      if (taskStatusPolling.value) {\n        clearInterval(taskStatusPolling.value)\n        taskStatusPolling.value = null\n      }\n    }\n\n    // 事件处理\n    const handleUploadSuccess = (response) => {\n      const taskId = response.data?.taskId || response.taskId || `task_${Date.now()}`\n      currentTaskId.value = taskId\n\n      // 保存任务信息到会话存储\n      try {\n        sessionStorage.setItem('fourWayActiveTask', JSON.stringify({\n          taskId: taskId,\n          timestamp: Date.now()\n        }))\n      } catch (error) {\n        console.warn('保存任务信息到会话存储失败:', error)\n      }\n\n      // 更新当前任务信息\n      currentTask.value = {\n        id: taskId,\n        name: '四方向交通分析任务',\n        status: 'processing',\n        progress: 10,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      }\n\n      // 更新URL参数但不导航\n      router.replace({\n        path: route.path,\n        query: { ...route.query, taskId: taskId }\n      })\n\n      currentStep.value = 1\n      ElMessage.success('视频上传成功，开始实时检测')\n\n      // 开始轮询任务状态\n      startTaskStatusPolling(taskId)\n    }\n\n    const handleUploadError = (error) => {\n      ElMessage.error('视频上传失败: ' + error.message)\n    }\n\n    const handleUploadProgress = (progress) => {\n      console.log('上传进度:', progress)\n    }\n\n    const handleUploadStatusChange = (status) => {\n      console.log('上传状态变化:', status)\n    }\n\n    const handleDetectionUpdate = (data) => {\n      console.log('检测更新:', data)\n\n      // 更新任务进度\n      if (currentTask.value && data.progress) {\n        currentTask.value.progress = Math.min(data.progress, 90) // 检测阶段最多90%\n        currentTask.value.updatedAt = new Date()\n      }\n    }\n\n    const handleDetectionStatusChange = (status) => {\n      if (status === 'completed') {\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed'\n          currentTask.value.progress = 100\n          currentTask.value.updatedAt = new Date()\n        }\n\n        currentStep.value = 2\n        ElMessage.success('实时检测完成，开始智能分析')\n      }\n    }\n\n    // 处理四方向分析完成事件\n    const handleAnalysisComplete = (completeData) => {\n      try {\n        console.log('🎉 收到四方向分析完成事件:', completeData)\n\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed'\n          currentTask.value.progress = 100\n          currentTask.value.updatedAt = new Date()\n        }\n\n        // 显示完成提示并自动跳转\n        ElMessage({\n          message: `四方向分析完成！检测到 ${completeData.summary?.totalVehicles || 0} 辆车辆，正在跳转到智能分析模块...`,\n          type: 'success',\n          duration: 4000\n        })\n\n        // 延迟跳转到智能分析模块，给用户时间看到完成消息\n        setTimeout(() => {\n          currentStep.value = 2\n          ElMessage.info('已自动跳转到智能分析模块')\n        }, 2000)\n\n      } catch (error) {\n        console.error('处理分析完成事件失败:', error)\n        ElMessage.error('处理完成事件失败，请手动跳转到智能分析')\n      }\n    }\n\n    const handleAnalysisDataUpdate = (data) => {\n      reportData.value = data\n      currentStep.value = 3\n      ElMessage.success('智能分析完成，可以生成报告')\n    }\n\n    const handleExportReport = (taskId) => {\n      ElMessage.success('报告导出成功')\n    }\n\n    const handleRefreshReportData = (taskId) => {\n      ElMessage.success('报告数据刷新成功')\n    }\n    \n    // 快速操作\n    const goToUpload = () => {\n      currentStep.value = 0\n    }\n    \n    const startDetection = () => {\n      if (canDetect.value) {\n        currentStep.value = 1\n      } else {\n        ElMessage.warning('请先上传视频文件')\n      }\n    }\n    \n    const generateAnalysis = () => {\n      if (canAnalyze.value) {\n        currentStep.value = 2\n      } else {\n        ElMessage.warning('请先完成视频检测')\n      }\n    }\n    \n    const exportReport = () => {\n      if (canExport.value) {\n        currentStep.value = 3\n      } else {\n        ElMessage.warning('请先完成智能分析')\n      }\n    }\n\n    const refreshSystem = () => {\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n      ElMessage.success('系统状态已刷新')\n    }\n\n    const showSystemInfo = () => {\n      ElMessageBox.alert('四方向智能交通分析系统 v1.0.0', '系统信息', {\n        confirmButtonText: '确定'\n      })\n    }\n\n    // StepNavigator事件处理\n    const handleStepChange = (stepIndex) => {\n      console.log('步骤切换请求:', stepIndex)\n\n      // 检查是否可以切换到目标步骤\n      if (stepIndex <= currentStep.value) {\n        currentStep.value = stepIndex\n        ElMessage.info(`已切换到步骤 ${stepIndex + 1}`)\n      } else {\n        ElMessage.warning('请先完成前面的步骤')\n      }\n    }\n\n    const handleQuickAction = (actionKey) => {\n      console.log('快速操作:', actionKey)\n\n      switch (actionKey) {\n        case 'upload':\n          goToUpload()\n          break\n        case 'detection':\n          startDetection()\n          break\n        case 'analysis':\n          generateAnalysis()\n          break\n        case 'report':\n          exportReport()\n          break\n        default:\n          console.warn('未知的快速操作:', actionKey)\n      }\n    }\n\n    const handleRefreshStatus = async () => {\n      if (currentTaskId.value) {\n        ElMessage.info('正在刷新任务状态...')\n        await fetchTaskStatusAndSetStep(currentTaskId.value)\n      } else {\n        ElMessage.warning('没有活跃的任务')\n      }\n    }\n\n    // 任务状态辅助方法\n    const getTaskStatusType = (status) => {\n      const statusMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'failed': 'danger',\n        'paused': 'info'\n      }\n      return statusMap[status] || 'info'\n    }\n\n    const getTaskStatusText = (status) => {\n      const statusMap = {\n        'waiting': '等待中',\n        'processing': '处理中',\n        'completed': '已完成',\n        'failed': '失败',\n        'paused': '已暂停'\n      }\n      return statusMap[status] || '未知'\n    }\n\n    const getProgressStatus = (status) => {\n      if (status === 'completed') return 'success'\n      if (status === 'failed') return 'exception'\n      return null\n    }\n\n    const formatTime = (time) => {\n      if (!time) return '-'\n      return new Date(time).toLocaleString()\n    }\n\n    const getProcessingDuration = (task) => {\n      if (!task || !task.createdAt) return '-'\n      const start = new Date(task.createdAt)\n      const end = task.updatedAt ? new Date(task.updatedAt) : new Date()\n      const duration = Math.floor((end - start) / 1000)\n\n      if (duration < 60) return `${duration}秒`\n      if (duration < 3600) return `${Math.floor(duration / 60)}分钟`\n      return `${Math.floor(duration / 3600)}小时`\n    }\n\n    // 监听路由变化\n    watch(() => route.query.taskId, (newTaskId) => {\n      if (newTaskId && newTaskId !== currentTaskId.value) {\n        console.log('检测到URL中的taskId变化:', newTaskId)\n        currentTaskId.value = newTaskId\n        fetchTaskStatusAndSetStep(newTaskId)\n      }\n    })\n\n    // 生命周期\n    onMounted(async () => {\n      // 初始化系统状态\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n      activeConnections.value = 1\n\n      // 初始化页面状态\n      await initializeFromRoute()\n    })\n\n    onUnmounted(() => {\n      // 清理轮询\n      stopTaskStatusPolling()\n    })\n    \n    return {\n      // 图标组件\n      Upload,\n      VideoCamera,\n      DataAnalysis,\n      Document,\n      Plus,\n      MoreFilled,\n      Refresh,\n      InfoFilled,\n\n      // 响应式数据\n      currentStep,\n      currentTaskId,\n      currentTask,\n      reportData,\n      activeConnections,\n      lastUpdateTime,\n      systemStatus,\n      isInitializing,\n\n      // 计算属性\n      totalTasks,\n      activeTasks,\n      completedTasks,\n      canUpload,\n      canDetect,\n      canAnalyze,\n      canExport,\n\n      // 方法\n      initializeFromRoute,\n      fetchTaskStatusAndSetStep,\n      setStepByTaskStatus,\n      checkActiveTask,\n      startTaskStatusPolling,\n      stopTaskStatusPolling,\n      handleUploadSuccess,\n      handleUploadError,\n      handleUploadProgress,\n      handleUploadStatusChange,\n      handleDetectionUpdate,\n      handleDetectionStatusChange,\n      handleAnalysisComplete,\n      handleAnalysisDataUpdate,\n      handleExportReport,\n      handleRefreshReportData,\n      goToUpload,\n      startDetection,\n      generateAnalysis,\n      exportReport,\n      refreshSystem,\n      showSystemInfo,\n      handleStepChange,\n      handleQuickAction,\n      handleRefreshStatus,\n\n      // 任务状态辅助方法\n      getTaskStatusType,\n      getTaskStatusText,\n      getProgressStatus,\n      formatTime,\n      getProcessingDuration\n    }\n  }\n}\n</script>\n\n<style scoped>\n.four-way-analysis-console {\n  min-height: 100vh;\n  background: #f5f7fa;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 控制台头部 */\n.console-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 24px 32px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.console-title {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 28px;\n  font-weight: 700;\n  margin: 0 0 8px 0;\n}\n\n.console-description {\n  font-size: 16px;\n  opacity: 0.9;\n  margin: 0;\n  line-height: 1.5;\n}\n\n.header-stats {\n  display: flex;\n  gap: 32px;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: 700;\n  line-height: 1.2;\n}\n\n.stat-label {\n  font-size: 14px;\n  opacity: 0.8;\n  margin-top: 4px;\n}\n\n/* 工作流程导航 */\n.workflow-navigation {\n  background: white;\n  padding: 24px 32px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n/* 主要内容区域 */\n.console-content {\n  flex: 1;\n  display: grid;\n  grid-template-columns: 280px 1fr;\n  gap: 24px;\n  padding: 24px 32px;\n  min-height: 0;\n}\n\n/* 左侧面板 */\n.left-panel {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.quick-actions-card {\n  height: fit-content;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.quick-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.quick-actions .el-button {\n  justify-content: flex-start;\n}\n\n/* 主内容区域 */\n.main-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  min-height: 0;\n}\n\n.current-task-info {\n  flex-shrink: 0;\n}\n\n.task-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.task-title-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.task-title-section h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.task-progress-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  min-width: 200px;\n}\n\n.progress-text {\n  font-size: 14px;\n  font-weight: 500;\n  color: #6b7280;\n  min-width: 40px;\n}\n\n.task-details {\n  margin-top: 16px;\n}\n\n.detail-item {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.detail-label {\n  font-size: 12px;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.detail-value {\n  font-size: 14px;\n  color: #1f2937;\n  font-weight: 500;\n}\n\n.dynamic-content {\n  flex: 1;\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  overflow: auto;\n}\n\n.step-content {\n  height: 100%;\n}\n\n/* 加载状态样式 */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 400px;\n  gap: 20px;\n}\n\n.loading-content {\n  width: 100%;\n  max-width: 600px;\n}\n\n.loading-text {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  color: #6b7280;\n  margin-top: 20px;\n}\n\n.loading-icon {\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n/* 底部状态栏 */\n.console-footer {\n  background: white;\n  border-top: 1px solid #e5e7eb;\n  padding: 12px 32px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 14px;\n}\n\n.footer-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #6b7280;\n}\n\n.separator {\n  color: #d1d5db;\n}\n\n.footer-actions {\n  display: flex;\n  gap: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .console-content {\n    grid-template-columns: 300px 1fr;\n  }\n\n  .header-stats {\n    gap: 16px;\n  }\n}\n\n@media (max-width: 768px) {\n  .console-header {\n    flex-direction: column;\n    gap: 16px;\n    text-align: center;\n  }\n\n  .header-stats {\n    justify-content: center;\n  }\n\n  .console-content {\n    grid-template-columns: 1fr;\n    padding: 16px;\n  }\n\n  .workflow-navigation {\n    padding: 16px;\n  }\n\n  .console-footer {\n    flex-direction: column;\n    gap: 12px;\n    padding: 16px;\n  }\n\n  .task-header {\n    flex-direction: column;\n    gap: 12px;\n    align-items: flex-start;\n  }\n\n  .task-progress-section {\n    width: 100%;\n    min-width: auto;\n  }\n}\n\n/* 滚动条样式 */\n.task-list::-webkit-scrollbar {\n  width: 6px;\n}\n\n.task-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.task-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.task-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 动画效果 */\n.task-item {\n  animation: slideIn 0.3s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.step-content {\n  animation: fadeIn 0.5s ease-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n</style>\n"], "mappings": "AAoOA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAI,QAAS,KAAI;AAC3E,SAASC,SAAS,EAAEC,QAAO,QAAS,YAAW;AAC/C,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SACEC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,IAAI,EACvDC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAM,QAClC,yBAAwB;;AAE/B;AACA,OAAOC,kBAAiB,MAAO,8CAA6C;AAC5E,OAAOC,qBAAoB,MAAO,iDAAgD;AAClF,OAAOC,wBAAuB,MAAO,oDAAmD;AACxF,OAAOC,wBAAuB,MAAO,oDAAmD;AACxF,OAAOC,aAAY,MAAO,yCAAwC;AAElE,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,UAAU,EAAE;IACVhB,IAAI;IAAEC,MAAM;IAAEC,WAAW;IAAEC,YAAY;IAAEC,QAAQ;IAAEC,IAAI;IACvDC,UAAU;IAAEC,OAAO;IAAEC,UAAU;IAAEC,OAAO;IACxCC,kBAAkB;IAClBC,qBAAqB;IACrBC,wBAAwB;IACxBC,wBAAwB;IACxBC;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAItB,SAAS,CAAC;IACzB,MAAMuB,KAAI,GAAItB,QAAQ,CAAC;;IAEvB;IACA,MAAMuB,WAAU,GAAI9B,GAAG,CAAC,CAAC;IACzB,MAAM+B,aAAY,GAAI/B,GAAG,CAAC,EAAE;IAC5B,MAAMgC,WAAU,GAAIhC,GAAG,CAAC;MACtBiC,EAAE,EAAE,EAAE;MACNR,IAAI,EAAE,WAAW;MACjBS,MAAM,EAAE,SAAS;MACjBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;IACtB,CAAC;IACD,MAAME,UAAS,GAAIvC,GAAG,CAAC,IAAI;IAC3B,MAAMwC,iBAAgB,GAAIxC,GAAG,CAAC,CAAC;IAC/B,MAAMyC,cAAa,GAAIzC,GAAG,CAAC,EAAE;IAC7B,MAAM0C,cAAa,GAAI1C,GAAG,CAAC,IAAI;IAC/B,MAAM2C,iBAAgB,GAAI3C,GAAG,CAAC,IAAI;;IAElC;IACA,MAAM4C,YAAW,GAAI3C,QAAQ,CAAC;MAC5B4C,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE;IACR,CAAC;;IAED;IACA,MAAMC,UAAS,GAAI7C,QAAQ,CAAC,MAAM,CAAC;IACnC,MAAM8C,WAAU,GAAI9C,QAAQ,CAAC,MAAM4B,WAAW,CAACmB,KAAI,GAAI,KAAKnB,WAAW,CAACmB,KAAI,GAAI,IAAI,IAAI,CAAC;IACzF,MAAMC,cAAa,GAAIhD,QAAQ,CAAC,MAAM4B,WAAW,CAACmB,KAAI,KAAM,IAAI,IAAI,CAAC;IAErE,MAAME,SAAQ,GAAIjD,QAAQ,CAAC,MAAM,IAAI;IACrC,MAAMkD,SAAQ,GAAIlD,QAAQ,CAAC,MAAM4B,WAAW,CAACmB,KAAI,IAAK,CAAC;IACvD,MAAMI,UAAS,GAAInD,QAAQ,CAAC,MAAM4B,WAAW,CAACmB,KAAI,IAAK,CAAC;IACxD,MAAMK,SAAQ,GAAIpD,QAAQ,CAAC,MAAM4B,WAAW,CAACmB,KAAI,IAAK,CAAC;;IAEvD;;IAEA;IACA,MAAMM,mBAAkB,GAAI,MAAAA,CAAA,KAAY;MACtC,IAAI;QACFb,cAAc,CAACO,KAAI,GAAI,IAAG;;QAE1B;QACA,MAAMO,WAAU,GAAI3B,KAAK,CAAC4B,KAAK,CAACC,IAAG;QACnC,MAAMC,SAAQ,GAAI9B,KAAK,CAAC4B,KAAK,CAACG,MAAK,IAAK/B,KAAK,CAACgC,MAAM,CAACD,MAAK;QAE1DE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEP,WAAW,EAAE,OAAO,EAAEG,SAAS;;QAE1D;QACA,IAAIH,WAAU,KAAM,KAAK,EAAE;UACzBM,OAAO,CAACC,GAAG,CAAC,kBAAkB;UAC9B,MAAMC,oBAAoB,CAAC;QAC7B,OAAO,IAAIL,SAAS,EAAE;UACpBG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEJ,SAAS;UACvC5B,aAAa,CAACkB,KAAI,GAAIU,SAAQ;UAC9B;UACA,MAAMM,yBAAyB,CAACN,SAAS;QAC3C,OAAO;UACL;UACA,MAAMO,iBAAiB,CAAC;QAC1B;MAEF,EAAE,OAAOC,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7B3D,SAAS,CAAC2D,KAAK,CAAC,aAAY,GAAIA,KAAK,CAACC,OAAO;MAC/C,UAAU;QACR1B,cAAc,CAACO,KAAI,GAAI,KAAI;MAC7B;IACF;IAEA,MAAMgB,yBAAwB,GAAI,MAAOL,MAAM,IAAK;MAClD,IAAI;QACF,MAAMS,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,YAAY;QAC/C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIG,KAAK,CAAC,SAAS;QAC3B;QAEA,MAAMC,QAAO,GAAI,MAAMC,KAAK,CAAC,gCAAgCd,MAAM,SAAS,EAAE;UAC5Ee,OAAO,EAAE;YACP,eAAe,EAAE,UAAUN,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC;QAED,IAAI,CAACI,QAAQ,CAACG,EAAE,EAAE;UAChB,MAAM,IAAIJ,KAAK,CAAC,aAAaC,QAAQ,CAACvC,MAAM,EAAE;QAChD;QAEA,MAAM2C,QAAO,GAAI,MAAMJ,QAAQ,CAACK,IAAI,CAAC;QACrChB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEc,QAAQ;;QAEhC;QACA7C,WAAW,CAACiB,KAAI,GAAI;UAClBhB,EAAE,EAAE4C,QAAQ,CAACjB,MAAK,IAAKA,MAAM;UAC7BnC,IAAI,EAAEoD,QAAQ,CAACpD,IAAG,IAAK,WAAW;UAClCS,MAAM,EAAE2C,QAAQ,CAAC3C,MAAK,IAAK,SAAS;UACpCC,QAAQ,EAAE0C,QAAQ,CAAC1C,QAAO,IAAK,CAAC;UAChCC,SAAS,EAAEyC,QAAQ,CAACzC,SAAQ,GAAI,IAAIC,IAAI,CAACwC,QAAQ,CAACzC,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC;UACzEC,SAAS,EAAEuC,QAAQ,CAACvC,SAAQ,GAAI,IAAID,IAAI,CAACwC,QAAQ,CAACvC,SAAS,IAAI,IAAID,IAAI,CAAC;QAC1E;;QAEA;QACA0C,mBAAmB,CAACF,QAAQ,CAAC3C,MAAM,EAAE2C,QAAQ,CAAC1C,QAAQ;;QAEtD;QACA,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC6C,QAAQ,CAACH,QAAQ,CAAC3C,MAAM,CAAC,EAAE;UACtD+C,sBAAsB,CAACrB,MAAM;QAC/B;MAEF,EAAE,OAAOO,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC3D,SAAS,CAAC0E,OAAO,CAAC,mBAAmB;QACrCpD,WAAW,CAACmB,KAAI,GAAI;MACtB;IACF;IAEA,MAAM8B,mBAAkB,GAAIA,CAAC7C,MAAM,EAAEC,QAAO,GAAI,CAAC,KAAK;MACpD2B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QAAE7B,MAAM;QAAEC;MAAS,CAAC;MAE/C,QAAQD,MAAM;QACZ,KAAK,QAAQ;QACb,KAAK,WAAW;UACdJ,WAAW,CAACmB,KAAI,GAAI,GAAE;UACtBzC,SAAS,CAAC2E,IAAI,CAAC,eAAe;UAC9B;QAEF,KAAK,YAAY;UACf,IAAIhD,QAAO,GAAI,EAAE,EAAE;YACjBL,WAAW,CAACmB,KAAI,GAAI,GAAE;YACtBzC,SAAS,CAAC2E,IAAI,CAAC,YAAY;UAC7B,OAAO,IAAIhD,QAAO,GAAI,EAAE,EAAE;YACxBL,WAAW,CAACmB,KAAI,GAAI,GAAE;YACtBzC,SAAS,CAAC2E,IAAI,CAAC,SAAS;UAC1B,OAAO;YACLrD,WAAW,CAACmB,KAAI,GAAI,GAAE;YACtBzC,SAAS,CAAC2E,IAAI,CAAC,UAAU;UAC3B;UACA;QAEF,KAAK,WAAW;UACdrD,WAAW,CAACmB,KAAI,GAAI,GAAE;UACtBzC,SAAS,CAAC4E,OAAO,CAAC,gBAAgB;UAClC;QAEF,KAAK,QAAQ;UACXtD,WAAW,CAACmB,KAAI,GAAI,GAAE;UACtBzC,SAAS,CAAC2D,KAAK,CAAC,cAAc;UAC9B;QAEF;UACErC,WAAW,CAACmB,KAAI,GAAI,GAAE;UACtB;MACJ;IACF;;IAEA;IACA,MAAMiB,iBAAgB,GAAI,MAAAA,CAAA,KAAY;MACpC,IAAI;QACF;QACA,MAAMmB,WAAU,GAAIC,cAAc,CAACf,OAAO,CAAC,mBAAmB;QAC9D,IAAIc,WAAW,EAAE;UACf,MAAME,QAAO,GAAIC,IAAI,CAACC,KAAK,CAACJ,WAAW;UACvC,MAAMK,UAAS,GAAIrD,IAAI,CAACsD,GAAG,CAAC,IAAIJ,QAAQ,CAACK,SAAQ;UACjD,MAAMC,aAAY,GAAI,EAAC,GAAI,EAAC,GAAI,EAAC,GAAI,IAAG,EAAE;;UAE1C/B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEwB,QAAQ,EAAE,OAAO,EAAEG,UAAS,GAAI,IAAG,GAAI,EAAE,EAAE,IAAI;;UAEvE;UACA,IAAIA,UAAS,GAAIG,aAAa,EAAE;YAC9B/B,OAAO,CAACC,GAAG,CAAC,eAAe;YAC3B,MAAMC,oBAAoB,CAAC;YAC3B;UACF;;UAEA;UACA,MAAM8B,uBAAuB,CAACP,QAAQ;QACxC,OAAO;UACL;UACAzB,OAAO,CAACC,GAAG,CAAC,iBAAiB;UAC7B,MAAMC,oBAAoB,CAAC;QAC7B;MAEF,EAAE,OAAOG,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B,MAAMH,oBAAoB,CAAC;MAC7B;IACF;;IAEA;IACA,MAAMA,oBAAmB,GAAI,MAAAA,CAAA,KAAY;MACvC,IAAI;QACFF,OAAO,CAACC,GAAG,CAAC,QAAQ;;QAEpB;QACA,MAAMgC,oBAAoB,CAAC;;QAE3B;QACAjE,WAAW,CAACmB,KAAI,GAAI;QACpBlB,aAAa,CAACkB,KAAI,GAAI,IAAG;QACzBjB,WAAW,CAACiB,KAAI,GAAI,CAAC;QACrBV,UAAU,CAACU,KAAI,GAAI,IAAG;;QAEtB;QACA+C,qBAAqB,CAAC;;QAEtB;QACA,IAAInE,KAAK,CAAC4B,KAAK,CAACG,MAAM,EAAE;UACtBhC,MAAM,CAACqE,OAAO,CAAC;YACbC,IAAI,EAAErE,KAAK,CAACqE,IAAI;YAChBzC,KAAK,EAAE;cAAE,GAAG5B,KAAK,CAAC4B,KAAK;cAAEG,MAAM,EAAEuC;YAAU;UAC7C,CAAC;QACH;QAEA3F,SAAS,CAAC4E,OAAO,CAAC,WAAW;MAE/B,EAAE,OAAOjB,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC3D,SAAS,CAAC2D,KAAK,CAAC,YAAW,GAAIA,KAAK,CAACC,OAAO;MAC9C;IACF;;IAEA;IACA,MAAM2B,oBAAmB,GAAI,MAAAA,CAAA,KAAY;MACvC,IAAI;QACFjC,OAAO,CAACC,GAAG,CAAC,WAAW;;QAEvB;QACAuB,cAAc,CAACc,UAAU,CAAC,mBAAmB;;QAE7C;QACA,MAAMC,YAAW,GAAI,CAAC,aAAa,EAAE,iBAAiB,EAAE,gBAAgB;QACxEA,YAAY,CAACC,OAAO,CAACC,GAAE,IAAK;UAC1B,IAAI;YACFjB,cAAc,CAACc,UAAU,CAACG,GAAG;UAC/B,EAAE,OAAOC,CAAC,EAAE;YACV1C,OAAO,CAAC2C,IAAI,CAAC,SAASF,GAAG,MAAM,EAAEC,CAAC;UACpC;QACF,CAAC;MAEH,EAAE,OAAOrC,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;MAClC;IACF;;IAEA;IACA,MAAM2B,uBAAsB,GAAI,MAAOP,QAAQ,IAAK;MAClD,IAAI;QACF,MAAMmB,MAAK,GAAI,MAAMjG,YAAY,CAACkG,OAAO,CACvC,4BAA4BpB,QAAQ,CAAC3B,MAAM,QAAQ,EACnD,MAAM,EACN;UACEgD,iBAAiB,EAAE,SAAS;UAC5BC,gBAAgB,EAAE,QAAQ;UAC1BhE,IAAI,EAAE,UAAU;UAChBiE,yBAAyB,EAAE,IAAI;UAC/BC,iBAAiB,EAAE,KAAK;UACxBC,kBAAkB,EAAE,KAAK;UACzBC,SAAS,EAAE,KAAK;UAChBC,WAAW,EAAE;QACf,CACF;;QAEA;QACA,IAAIR,MAAK,KAAM,SAAS,EAAE;UACxB5C,OAAO,CAACC,GAAG,CAAC,aAAa;UACzBhC,aAAa,CAACkB,KAAI,GAAIsC,QAAQ,CAAC3B,MAAK;UACpC,MAAMK,yBAAyB,CAACsB,QAAQ,CAAC3B,MAAM;UAC/CpD,SAAS,CAAC2E,IAAI,CAAC,YAAY;QAC7B;MAEF,EAAE,OAAOgC,MAAM,EAAE;QACf;QACA,IAAIA,MAAK,KAAM,QAAQ,EAAE;UACvBrD,OAAO,CAACC,GAAG,CAAC,YAAY;UACxB,MAAMC,oBAAoB,CAAC;QAC7B,OAAO;UACL;UACAF,OAAO,CAACC,GAAG,CAAC,eAAe;UAC3B,MAAMC,oBAAoB,CAAC;QAC7B;MACF;IACF;IAEA,MAAMoD,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC;MACA,MAAMlD,iBAAiB,CAAC;IAC1B;IAEA,MAAMe,sBAAqB,GAAKrB,MAAM,IAAK;MACzC;MACA,IAAIjB,iBAAiB,CAACM,KAAK,EAAE;QAC3BoE,aAAa,CAAC1E,iBAAiB,CAACM,KAAK;MACvC;MAEAa,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEH,MAAM;MAE/BjB,iBAAiB,CAACM,KAAI,GAAIqE,WAAW,CAAC,YAAY;QAChD,IAAI;UACF,MAAMrD,yBAAyB,CAACL,MAAM;;UAEtC;UACA,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAACoB,QAAQ,CAAChD,WAAW,CAACiB,KAAK,CAACf,MAAM,CAAC,EAAE;YAC9DmF,aAAa,CAAC1E,iBAAiB,CAACM,KAAK;YACrCN,iBAAiB,CAACM,KAAI,GAAI,IAAG;UAC/B;QAEF,EAAE,OAAOkB,KAAK,EAAE;UACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;QAClC;MACF,CAAC,EAAE,IAAI,GAAE;IACX;IAEA,MAAM6B,qBAAoB,GAAIA,CAAA,KAAM;MAClC,IAAIrD,iBAAiB,CAACM,KAAK,EAAE;QAC3BoE,aAAa,CAAC1E,iBAAiB,CAACM,KAAK;QACrCN,iBAAiB,CAACM,KAAI,GAAI,IAAG;MAC/B;IACF;;IAEA;IACA,MAAMsE,mBAAkB,GAAK9C,QAAQ,IAAK;MACxC,MAAMb,MAAK,GAAIa,QAAQ,CAAC+C,IAAI,EAAE5D,MAAK,IAAKa,QAAQ,CAACb,MAAK,IAAK,QAAQvB,IAAI,CAACsD,GAAG,CAAC,CAAC,EAAC;MAC9E5D,aAAa,CAACkB,KAAI,GAAIW,MAAK;;MAE3B;MACA,IAAI;QACF0B,cAAc,CAACmC,OAAO,CAAC,mBAAmB,EAAEjC,IAAI,CAACkC,SAAS,CAAC;UACzD9D,MAAM,EAAEA,MAAM;UACdgC,SAAS,EAAEvD,IAAI,CAACsD,GAAG,CAAC;QACtB,CAAC,CAAC;MACJ,EAAE,OAAOxB,KAAK,EAAE;QACdL,OAAO,CAAC2C,IAAI,CAAC,gBAAgB,EAAEtC,KAAK;MACtC;;MAEA;MACAnC,WAAW,CAACiB,KAAI,GAAI;QAClBhB,EAAE,EAAE2B,MAAM;QACVnC,IAAI,EAAE,WAAW;QACjBS,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;MACtB;;MAEA;MACAT,MAAM,CAACqE,OAAO,CAAC;QACbC,IAAI,EAAErE,KAAK,CAACqE,IAAI;QAChBzC,KAAK,EAAE;UAAE,GAAG5B,KAAK,CAAC4B,KAAK;UAAEG,MAAM,EAAEA;QAAO;MAC1C,CAAC;MAED9B,WAAW,CAACmB,KAAI,GAAI;MACpBzC,SAAS,CAAC4E,OAAO,CAAC,eAAe;;MAEjC;MACAH,sBAAsB,CAACrB,MAAM;IAC/B;IAEA,MAAM+D,iBAAgB,GAAKxD,KAAK,IAAK;MACnC3D,SAAS,CAAC2D,KAAK,CAAC,UAAS,GAAIA,KAAK,CAACC,OAAO;IAC5C;IAEA,MAAMwD,oBAAmB,GAAKzF,QAAQ,IAAK;MACzC2B,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE5B,QAAQ;IAC/B;IAEA,MAAM0F,wBAAuB,GAAK3F,MAAM,IAAK;MAC3C4B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE7B,MAAM;IAC/B;IAEA,MAAM4F,qBAAoB,GAAKN,IAAI,IAAK;MACtC1D,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEyD,IAAI;;MAEzB;MACA,IAAIxF,WAAW,CAACiB,KAAI,IAAKuE,IAAI,CAACrF,QAAQ,EAAE;QACtCH,WAAW,CAACiB,KAAK,CAACd,QAAO,GAAI4F,IAAI,CAACC,GAAG,CAACR,IAAI,CAACrF,QAAQ,EAAE,EAAE,GAAE;QACzDH,WAAW,CAACiB,KAAK,CAACX,SAAQ,GAAI,IAAID,IAAI,CAAC;MACzC;IACF;IAEA,MAAM4F,2BAA0B,GAAK/F,MAAM,IAAK;MAC9C,IAAIA,MAAK,KAAM,WAAW,EAAE;QAC1B;QACA,IAAIF,WAAW,CAACiB,KAAK,EAAE;UACrBjB,WAAW,CAACiB,KAAK,CAACf,MAAK,GAAI,WAAU;UACrCF,WAAW,CAACiB,KAAK,CAACd,QAAO,GAAI,GAAE;UAC/BH,WAAW,CAACiB,KAAK,CAACX,SAAQ,GAAI,IAAID,IAAI,CAAC;QACzC;QAEAP,WAAW,CAACmB,KAAI,GAAI;QACpBzC,SAAS,CAAC4E,OAAO,CAAC,eAAe;MACnC;IACF;;IAEA;IACA,MAAM8C,sBAAqB,GAAKC,YAAY,IAAK;MAC/C,IAAI;QACFrE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoE,YAAY;;QAE3C;QACA,IAAInG,WAAW,CAACiB,KAAK,EAAE;UACrBjB,WAAW,CAACiB,KAAK,CAACf,MAAK,GAAI,WAAU;UACrCF,WAAW,CAACiB,KAAK,CAACd,QAAO,GAAI,GAAE;UAC/BH,WAAW,CAACiB,KAAK,CAACX,SAAQ,GAAI,IAAID,IAAI,CAAC;QACzC;;QAEA;QACA7B,SAAS,CAAC;UACR4D,OAAO,EAAE,eAAe+D,YAAY,CAACC,OAAO,EAAEC,aAAY,IAAK,CAAC,qBAAqB;UACrFxF,IAAI,EAAE,SAAS;UACfyF,QAAQ,EAAE;QACZ,CAAC;;QAED;QACAC,UAAU,CAAC,MAAM;UACfzG,WAAW,CAACmB,KAAI,GAAI;UACpBzC,SAAS,CAAC2E,IAAI,CAAC,cAAc;QAC/B,CAAC,EAAE,IAAI;MAET,EAAE,OAAOhB,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,aAAa,EAAEA,KAAK;QAClC3D,SAAS,CAAC2D,KAAK,CAAC,qBAAqB;MACvC;IACF;IAEA,MAAMqE,wBAAuB,GAAKhB,IAAI,IAAK;MACzCjF,UAAU,CAACU,KAAI,GAAIuE,IAAG;MACtB1F,WAAW,CAACmB,KAAI,GAAI;MACpBzC,SAAS,CAAC4E,OAAO,CAAC,eAAe;IACnC;IAEA,MAAMqD,kBAAiB,GAAK7E,MAAM,IAAK;MACrCpD,SAAS,CAAC4E,OAAO,CAAC,QAAQ;IAC5B;IAEA,MAAMsD,uBAAsB,GAAK9E,MAAM,IAAK;MAC1CpD,SAAS,CAAC4E,OAAO,CAAC,UAAU;IAC9B;;IAEA;IACA,MAAMuD,UAAS,GAAIA,CAAA,KAAM;MACvB7G,WAAW,CAACmB,KAAI,GAAI;IACtB;IAEA,MAAM2F,cAAa,GAAIA,CAAA,KAAM;MAC3B,IAAIxF,SAAS,CAACH,KAAK,EAAE;QACnBnB,WAAW,CAACmB,KAAI,GAAI;MACtB,OAAO;QACLzC,SAAS,CAAC0E,OAAO,CAAC,UAAU;MAC9B;IACF;IAEA,MAAM2D,gBAAe,GAAIA,CAAA,KAAM;MAC7B,IAAIxF,UAAU,CAACJ,KAAK,EAAE;QACpBnB,WAAW,CAACmB,KAAI,GAAI;MACtB,OAAO;QACLzC,SAAS,CAAC0E,OAAO,CAAC,UAAU;MAC9B;IACF;IAEA,MAAM4D,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIxF,SAAS,CAACL,KAAK,EAAE;QACnBnB,WAAW,CAACmB,KAAI,GAAI;MACtB,OAAO;QACLzC,SAAS,CAAC0E,OAAO,CAAC,UAAU;MAC9B;IACF;IAEA,MAAM6D,aAAY,GAAIA,CAAA,KAAM;MAC1BtG,cAAc,CAACQ,KAAI,GAAI,IAAIZ,IAAI,CAAC,CAAC,CAAC2G,kBAAkB,CAAC;MACrDxI,SAAS,CAAC4E,OAAO,CAAC,SAAS;IAC7B;IAEA,MAAM6D,cAAa,GAAIA,CAAA,KAAM;MAC3BxI,YAAY,CAACyI,KAAK,CAAC,oBAAoB,EAAE,MAAM,EAAE;QAC/CtC,iBAAiB,EAAE;MACrB,CAAC;IACH;;IAEA;IACA,MAAMuC,gBAAe,GAAKC,SAAS,IAAK;MACtCtF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEqF,SAAS;;MAEhC;MACA,IAAIA,SAAQ,IAAKtH,WAAW,CAACmB,KAAK,EAAE;QAClCnB,WAAW,CAACmB,KAAI,GAAImG,SAAQ;QAC5B5I,SAAS,CAAC2E,IAAI,CAAC,UAAUiE,SAAQ,GAAI,CAAC,EAAE;MAC1C,OAAO;QACL5I,SAAS,CAAC0E,OAAO,CAAC,WAAW;MAC/B;IACF;IAEA,MAAMmE,iBAAgB,GAAKC,SAAS,IAAK;MACvCxF,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEuF,SAAS;MAE9B,QAAQA,SAAS;QACf,KAAK,QAAQ;UACXX,UAAU,CAAC;UACX;QACF,KAAK,WAAW;UACdC,cAAc,CAAC;UACf;QACF,KAAK,UAAU;UACbC,gBAAgB,CAAC;UACjB;QACF,KAAK,QAAQ;UACXC,YAAY,CAAC;UACb;QACF;UACEhF,OAAO,CAAC2C,IAAI,CAAC,UAAU,EAAE6C,SAAS;MACtC;IACF;IAEA,MAAMC,mBAAkB,GAAI,MAAAA,CAAA,KAAY;MACtC,IAAIxH,aAAa,CAACkB,KAAK,EAAE;QACvBzC,SAAS,CAAC2E,IAAI,CAAC,aAAa;QAC5B,MAAMlB,yBAAyB,CAAClC,aAAa,CAACkB,KAAK;MACrD,OAAO;QACLzC,SAAS,CAAC0E,OAAO,CAAC,SAAS;MAC7B;IACF;;IAEA;IACA,MAAMsE,iBAAgB,GAAKtH,MAAM,IAAK;MACpC,MAAMuH,SAAQ,GAAI;QAChB,SAAS,EAAE,MAAM;QACjB,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,SAAS;QACtB,QAAQ,EAAE,QAAQ;QAClB,QAAQ,EAAE;MACZ;MACA,OAAOA,SAAS,CAACvH,MAAM,KAAK,MAAK;IACnC;IAEA,MAAMwH,iBAAgB,GAAKxH,MAAM,IAAK;MACpC,MAAMuH,SAAQ,GAAI;QAChB,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,KAAK;QAClB,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE;MACZ;MACA,OAAOA,SAAS,CAACvH,MAAM,KAAK,IAAG;IACjC;IAEA,MAAMyH,iBAAgB,GAAKzH,MAAM,IAAK;MACpC,IAAIA,MAAK,KAAM,WAAW,EAAE,OAAO,SAAQ;MAC3C,IAAIA,MAAK,KAAM,QAAQ,EAAE,OAAO,WAAU;MAC1C,OAAO,IAAG;IACZ;IAEA,MAAM0H,UAAS,GAAKC,IAAI,IAAK;MAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,GAAE;MACpB,OAAO,IAAIxH,IAAI,CAACwH,IAAI,CAAC,CAACC,cAAc,CAAC;IACvC;IAEA,MAAMC,qBAAoB,GAAKC,IAAI,IAAK;MACtC,IAAI,CAACA,IAAG,IAAK,CAACA,IAAI,CAAC5H,SAAS,EAAE,OAAO,GAAE;MACvC,MAAM6H,KAAI,GAAI,IAAI5H,IAAI,CAAC2H,IAAI,CAAC5H,SAAS;MACrC,MAAM8H,GAAE,GAAIF,IAAI,CAAC1H,SAAQ,GAAI,IAAID,IAAI,CAAC2H,IAAI,CAAC1H,SAAS,IAAI,IAAID,IAAI,CAAC;MACjE,MAAMiG,QAAO,GAAIP,IAAI,CAACoC,KAAK,CAAC,CAACD,GAAE,GAAID,KAAK,IAAI,IAAI;MAEhD,IAAI3B,QAAO,GAAI,EAAE,EAAE,OAAO,GAAGA,QAAQ,GAAE;MACvC,IAAIA,QAAO,GAAI,IAAI,EAAE,OAAO,GAAGP,IAAI,CAACoC,KAAK,CAAC7B,QAAO,GAAI,EAAE,CAAC,IAAG;MAC3D,OAAO,GAAGP,IAAI,CAACoC,KAAK,CAAC7B,QAAO,GAAI,IAAI,CAAC,IAAG;IAC1C;;IAEA;IACAjI,KAAK,CAAC,MAAMwB,KAAK,CAAC4B,KAAK,CAACG,MAAM,EAAGwG,SAAS,IAAK;MAC7C,IAAIA,SAAQ,IAAKA,SAAQ,KAAMrI,aAAa,CAACkB,KAAK,EAAE;QAClDa,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEqG,SAAS;QAC1CrI,aAAa,CAACkB,KAAI,GAAImH,SAAQ;QAC9BnG,yBAAyB,CAACmG,SAAS;MACrC;IACF,CAAC;;IAED;IACAjK,SAAS,CAAC,YAAY;MACpB;MACAsC,cAAc,CAACQ,KAAI,GAAI,IAAIZ,IAAI,CAAC,CAAC,CAAC2G,kBAAkB,CAAC;MACrDxG,iBAAiB,CAACS,KAAI,GAAI;;MAE1B;MACA,MAAMM,mBAAmB,CAAC;IAC5B,CAAC;IAEDnD,WAAW,CAAC,MAAM;MAChB;MACA4F,qBAAqB,CAAC;IACxB,CAAC;IAED,OAAO;MACL;MACArF,MAAM;MACNC,WAAW;MACXC,YAAY;MACZC,QAAQ;MACRC,IAAI;MACJC,UAAU;MACVC,OAAO;MACPC,UAAU;MAEV;MACAY,WAAW;MACXC,aAAa;MACbC,WAAW;MACXO,UAAU;MACVC,iBAAiB;MACjBC,cAAc;MACdG,YAAY;MACZF,cAAc;MAEd;MACAK,UAAU;MACVC,WAAW;MACXE,cAAc;MACdC,SAAS;MACTC,SAAS;MACTC,UAAU;MACVC,SAAS;MAET;MACAC,mBAAmB;MACnBU,yBAAyB;MACzBc,mBAAmB;MACnBqC,eAAe;MACfnC,sBAAsB;MACtBe,qBAAqB;MACrBuB,mBAAmB;MACnBI,iBAAiB;MACjBC,oBAAoB;MACpBC,wBAAwB;MACxBC,qBAAqB;MACrBG,2BAA2B;MAC3BC,sBAAsB;MACtBM,wBAAwB;MACxBC,kBAAkB;MAClBC,uBAAuB;MACvBC,UAAU;MACVC,cAAc;MACdC,gBAAgB;MAChBC,YAAY;MACZC,aAAa;MACbE,cAAc;MACdE,gBAAgB;MAChBE,iBAAiB;MACjBE,mBAAmB;MAEnB;MACAC,iBAAiB;MACjBE,iBAAiB;MACjBC,iBAAiB;MACjBC,UAAU;MACVG;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}