{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"strategy-recommendation\"\n};\nconst _hoisted_2 = {\n  class: \"strategy-header\"\n};\nconst _hoisted_3 = {\n  class: \"strategy-content\"\n};\nconst _hoisted_4 = {\n  class: \"strategy-main\"\n};\nconst _hoisted_5 = {\n  class: \"strategy-icon\"\n};\nconst _hoisted_6 = {\n  class: \"strategy-info\"\n};\nconst _hoisted_7 = {\n  class: \"strategy-primary\"\n};\nconst _hoisted_8 = {\n  class: \"strategy-secondary\"\n};\nconst _hoisted_9 = {\n  key: 0,\n  class: \"strategy-actions\"\n};\nconst _hoisted_10 = {\n  class: \"strategy-details\"\n};\nconst _hoisted_11 = {\n  class: \"detail-section\"\n};\nconst _hoisted_12 = {\n  class: \"implementation-steps\"\n};\nconst _hoisted_13 = {\n  class: \"detail-section\"\n};\nconst _hoisted_14 = {\n  class: \"expected-results\"\n};\nconst _hoisted_15 = {\n  key: 0,\n  class: \"detail-section\"\n};\nconst _hoisted_16 = {\n  class: \"warnings\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_Warning = _resolveComponent(\"Warning\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_collapse_transition = _resolveComponent(\"el-collapse-transition\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[0] || (_cache[0] = _createElementVNode(\"h4\", null, \"交通管理建议\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: $setup.priorityTagType,\n    size: \"small\",\n    class: \"priority-tag\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.priorityText), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, _toDisplayString($props.strategy.icon), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString($props.strategy.primary), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_8, _toDisplayString($props.strategy.secondary), 1 /* TEXT */)])]), $props.showActions ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    size: \"small\",\n    onClick: $setup.handleApplyStrategy,\n    loading: $setup.applying\n  }, {\n    default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\" 应用建议 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"loading\"]), _createVNode(_component_el_button, {\n    size: \"small\",\n    onClick: $setup.handleViewDetails\n  }, {\n    default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\" 查看详情 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 详细信息展开区域 \"), _createVNode(_component_el_collapse_transition, null, {\n    default: _withCtx(() => [_withDirectives(_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_cache[3] || (_cache[3] = _createElementVNode(\"h5\", null, \"实施步骤:\", -1 /* HOISTED */)), _createElementVNode(\"ol\", _hoisted_12, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.implementationSteps, step => {\n      return _openBlock(), _createElementBlock(\"li\", {\n        key: step\n      }, _toDisplayString(step), 1 /* TEXT */);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_13, [_cache[4] || (_cache[4] = _createElementVNode(\"h5\", null, \"预期效果:\", -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_14, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.expectedResults, result => {\n      return _openBlock(), _createElementBlock(\"li\", {\n        key: result\n      }, _toDisplayString(result), 1 /* TEXT */);\n    }), 128 /* KEYED_FRAGMENT */))])]), $setup.warnings.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_cache[5] || (_cache[5] = _createElementVNode(\"h5\", null, \"注意事项:\", -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_16, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.warnings, warning => {\n      return _openBlock(), _createElementBlock(\"li\", {\n        key: warning,\n        class: \"warning-item\"\n      }, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Warning)]),\n        _: 1 /* STABLE */\n      }), _createTextVNode(\" \" + _toDisplayString(warning), 1 /* TEXT */)]);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)], 512 /* NEED_PATCH */), [[_vShow, $setup.showDetails]])]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_tag", "type", "$setup", "priorityTagType", "size", "default", "_withCtx", "_createTextVNode", "_toDisplayString", "priorityText", "_", "_hoisted_3", "_hoisted_4", "_hoisted_5", "$props", "strategy", "icon", "_hoisted_6", "_hoisted_7", "primary", "_hoisted_8", "secondary", "showActions", "_hoisted_9", "_component_el_button", "onClick", "handleApplyStrategy", "loading", "applying", "_cache", "handleViewDetails", "_createCommentVNode", "_component_el_collapse_transition", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_Fragment", "_renderList", "implementationSteps", "step", "_hoisted_13", "_hoisted_14", "expectedResults", "result", "warnings", "length", "_hoisted_15", "_hoisted_16", "warning", "_component_el_icon", "_component_Warning", "showDetails"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\traffic\\StrategyRecommendation.vue"], "sourcesContent": ["<template>\n  <div class=\"strategy-recommendation\">\n    <div class=\"strategy-header\">\n      <h4>交通管理建议</h4>\n      <el-tag \n        :type=\"priorityTagType\" \n        size=\"small\"\n        class=\"priority-tag\"\n      >\n        {{ priorityText }}\n      </el-tag>\n    </div>\n    \n    <div class=\"strategy-content\">\n      <div class=\"strategy-main\">\n        <div class=\"strategy-icon\">{{ strategy.icon }}</div>\n        <div class=\"strategy-info\">\n          <div class=\"strategy-primary\">{{ strategy.primary }}</div>\n          <div class=\"strategy-secondary\">{{ strategy.secondary }}</div>\n        </div>\n      </div>\n      \n      <div class=\"strategy-actions\" v-if=\"showActions\">\n        <el-button \n          type=\"primary\" \n          size=\"small\"\n          @click=\"handleApplyStrategy\"\n          :loading=\"applying\"\n        >\n          应用建议\n        </el-button>\n        <el-button \n          size=\"small\"\n          @click=\"handleViewDetails\"\n        >\n          查看详情\n        </el-button>\n      </div>\n    </div>\n    \n    <!-- 详细信息展开区域 -->\n    <el-collapse-transition>\n      <div v-show=\"showDetails\" class=\"strategy-details\">\n        <div class=\"detail-section\">\n          <h5>实施步骤:</h5>\n          <ol class=\"implementation-steps\">\n            <li v-for=\"step in implementationSteps\" :key=\"step\">{{ step }}</li>\n          </ol>\n        </div>\n        \n        <div class=\"detail-section\">\n          <h5>预期效果:</h5>\n          <ul class=\"expected-results\">\n            <li v-for=\"result in expectedResults\" :key=\"result\">{{ result }}</li>\n          </ul>\n        </div>\n        \n        <div class=\"detail-section\" v-if=\"warnings.length > 0\">\n          <h5>注意事项:</h5>\n          <ul class=\"warnings\">\n            <li v-for=\"warning in warnings\" :key=\"warning\" class=\"warning-item\">\n              <el-icon><Warning /></el-icon>\n              {{ warning }}\n            </li>\n          </ul>\n        </div>\n      </div>\n    </el-collapse-transition>\n  </div>\n</template>\n\n<script>\nimport { ref, computed } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport { Warning } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'StrategyRecommendation',\n  components: {\n    Warning\n  },\n  props: {\n    strategy: {\n      type: Object,\n      required: true,\n      default: () => ({\n        primary: '',\n        secondary: '',\n        icon: '📋',\n        priority: 'low'\n      })\n    },\n    showActions: {\n      type: Boolean,\n      default: true\n    },\n    grade: {\n      type: String,\n      default: 'A'\n    }\n  },\n  emits: ['apply-strategy', 'view-details'],\n  setup(props, { emit }) {\n    const applying = ref(false)\n    const showDetails = ref(false)\n    \n    const priorityTagType = computed(() => {\n      switch (props.strategy.priority) {\n        case 'critical': return 'danger'\n        case 'high': return 'warning'\n        case 'medium': return 'primary'\n        default: return 'success'\n      }\n    })\n    \n    const priorityText = computed(() => {\n      switch (props.strategy.priority) {\n        case 'critical': return '紧急'\n        case 'high': return '高优先级'\n        case 'medium': return '中优先级'\n        default: return '低优先级'\n      }\n    })\n    \n    const implementationSteps = computed(() => {\n      const stepMap = {\n        A: ['保持当前信号配时', '继续常规监控'],\n        B: ['维持现有交通管制', '准备应急预案'],\n        C: ['调整信号灯配时', '增加交通引导标识', '启动实时监控'],\n        D: ['延长主要方向绿灯时间', '部署交通协管员', '开启可变车道'],\n        E: ['启动应急交通管制', '派遣现场交警', '发布交通预警'],\n        F: ['实施全面交通管制', '启用替代路线', '协调应急部门']\n      }\n      return stepMap[props.grade] || stepMap.A\n    })\n    \n    const expectedResults = computed(() => {\n      const resultMap = {\n        A: ['维持良好通行状态', '预防拥堵发生'],\n        B: ['保持交通顺畅', '及时应对突发情况'],\n        C: ['缓解轻微拥堵', '提高通行效率'],\n        D: ['显著改善交通状况', '减少等待时间'],\n        E: ['快速疏散车流', '恢复正常通行'],\n        F: ['最大化道路通行能力', '避免交通瘫痪']\n      }\n      return resultMap[props.grade] || resultMap.A\n    })\n    \n    const warnings = computed(() => {\n      const warningMap = {\n        A: [],\n        B: ['注意监控车流变化'],\n        C: ['避免过度调整信号配时'],\n        D: ['确保其他方向车辆安全', '注意行人通行'],\n        E: ['协调各部门行动', '确保应急车辆通行'],\n        F: ['及时发布公告', '做好长期管制准备']\n      }\n      return warningMap[props.grade] || []\n    })\n    \n    const handleApplyStrategy = async () => {\n      applying.value = true\n      try {\n        // 模拟应用策略的过程\n        await new Promise(resolve => setTimeout(resolve, 1500))\n        ElMessage.success('策略应用成功')\n        emit('apply-strategy', props.strategy)\n      } catch (error) {\n        ElMessage.error('策略应用失败')\n      } finally {\n        applying.value = false\n      }\n    }\n    \n    const handleViewDetails = () => {\n      showDetails.value = !showDetails.value\n      emit('view-details', { strategy: props.strategy, showDetails: showDetails.value })\n    }\n    \n    return {\n      applying,\n      showDetails,\n      priorityTagType,\n      priorityText,\n      implementationSteps,\n      expectedResults,\n      warnings,\n      handleApplyStrategy,\n      handleViewDetails\n    }\n  }\n}\n</script>\n\n<style scoped>\n.strategy-recommendation {\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e8e8e8;\n  overflow: hidden;\n}\n\n.strategy-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.strategy-header h4 {\n  margin: 0;\n  font-size: 16px;\n  color: #333;\n}\n\n.priority-tag {\n  font-weight: 500;\n}\n\n.strategy-content {\n  padding: 20px;\n}\n\n.strategy-main {\n  display: flex;\n  align-items: flex-start;\n  gap: 16px;\n  margin-bottom: 16px;\n}\n\n.strategy-icon {\n  font-size: 32px;\n  line-height: 1;\n}\n\n.strategy-info {\n  flex: 1;\n}\n\n.strategy-primary {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.strategy-secondary {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n}\n\n.strategy-actions {\n  display: flex;\n  gap: 12px;\n  justify-content: flex-end;\n}\n\n.strategy-details {\n  padding: 20px;\n  background: #fafafa;\n  border-top: 1px solid #e8e8e8;\n}\n\n.detail-section {\n  margin-bottom: 20px;\n}\n\n.detail-section:last-child {\n  margin-bottom: 0;\n}\n\n.detail-section h5 {\n  margin: 0 0 12px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n}\n\n.implementation-steps,\n.expected-results,\n.warnings {\n  margin: 0;\n  padding-left: 20px;\n}\n\n.implementation-steps li,\n.expected-results li {\n  margin-bottom: 8px;\n  color: #666;\n  line-height: 1.5;\n}\n\n.warnings {\n  list-style: none;\n  padding-left: 0;\n}\n\n.warning-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n  color: #fa8c16;\n  font-size: 14px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .strategy-header {\n    flex-direction: column;\n    gap: 8px;\n    align-items: flex-start;\n  }\n  \n  .strategy-main {\n    flex-direction: column;\n    gap: 12px;\n  }\n  \n  .strategy-actions {\n    flex-direction: column;\n  }\n  \n  .strategy-actions .el-button {\n    width: 100%;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAiB;;EAWvBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAkB;;EACxBA,KAAK,EAAC;AAAoB;;EAlBzCC,GAAA;EAsBWD,KAAK,EAAC;;;EAoBeA,KAAK,EAAC;AAAkB;;EAC3CA,KAAK,EAAC;AAAgB;;EAErBA,KAAK,EAAC;AAAsB;;EAK7BA,KAAK,EAAC;AAAgB;;EAErBA,KAAK,EAAC;AAAkB;;EApDtCC,GAAA;EAyDaD,KAAK,EAAC;;;EAELA,KAAK,EAAC;AAAU;;;;;;;uBA1D5BE,mBAAA,CAmEM,OAnENC,UAmEM,GAlEJC,mBAAA,CASM,OATNC,UASM,G,0BARJD,mBAAA,CAAe,YAAX,QAAM,sBACVE,YAAA,CAMSC,iBAAA;IALNC,IAAI,EAAEC,MAAA,CAAAC,eAAe;IACtBC,IAAI,EAAC,OAAO;IACZX,KAAK,EAAC;;IAPdY,OAAA,EAAAC,QAAA,CASQ,MAAkB,CAT1BC,gBAAA,CAAAC,gBAAA,CASWN,MAAA,CAAAO,YAAY,iB;IATvBC,CAAA;iCAaIb,mBAAA,CAyBM,OAzBNc,UAyBM,GAxBJd,mBAAA,CAMM,OANNe,UAMM,GALJf,mBAAA,CAAoD,OAApDgB,UAAoD,EAAAL,gBAAA,CAAtBM,MAAA,CAAAC,QAAQ,CAACC,IAAI,kBAC3CnB,mBAAA,CAGM,OAHNoB,UAGM,GAFJpB,mBAAA,CAA0D,OAA1DqB,UAA0D,EAAAV,gBAAA,CAAzBM,MAAA,CAAAC,QAAQ,CAACI,OAAO,kBACjDtB,mBAAA,CAA8D,OAA9DuB,UAA8D,EAAAZ,gBAAA,CAA3BM,MAAA,CAAAC,QAAQ,CAACM,SAAS,iB,KAIrBP,MAAA,CAAAQ,WAAW,I,cAA/C3B,mBAAA,CAeM,OAfN4B,UAeM,GAdJxB,YAAA,CAOYyB,oBAAA;IANVvB,IAAI,EAAC,SAAS;IACdG,IAAI,EAAC,OAAO;IACXqB,OAAK,EAAEvB,MAAA,CAAAwB,mBAAmB;IAC1BC,OAAO,EAAEzB,MAAA,CAAA0B;;IA3BpBvB,OAAA,EAAAC,QAAA,CA4BS,MAEDuB,MAAA,QAAAA,MAAA,OA9BRtB,gBAAA,CA4BS,QAED,E;IA9BRG,CAAA;6CA+BQX,YAAA,CAKYyB,oBAAA;IAJVpB,IAAI,EAAC,OAAO;IACXqB,OAAK,EAAEvB,MAAA,CAAA4B;;IAjClBzB,OAAA,EAAAC,QAAA,CAkCS,MAEDuB,MAAA,QAAAA,MAAA,OApCRtB,gBAAA,CAkCS,QAED,E;IApCRG,CAAA;sCAAAqB,mBAAA,e,GAwCIA,mBAAA,cAAiB,EACjBhC,YAAA,CA0ByBiC,iCAAA;IAnE7B3B,OAAA,EAAAC,QAAA,CA0CM,MAwBM,C,gBAxBNT,mBAAA,CAwBM,OAxBNoC,WAwBM,GAvBJpC,mBAAA,CAKM,OALNqC,WAKM,G,0BAJJrC,mBAAA,CAAc,YAAV,OAAK,sBACTA,mBAAA,CAEK,MAFLsC,WAEK,I,kBADHxC,mBAAA,CAAmEyC,SAAA,QA9C/EC,WAAA,CA8C+BnC,MAAA,CAAAoC,mBAAmB,EAA3BC,IAAI;2BAAf5C,mBAAA,CAAmE;QAA1BD,GAAG,EAAE6C;MAAI,GAAA/B,gBAAA,CAAK+B,IAAI;wCAI/D1C,mBAAA,CAKM,OALN2C,WAKM,G,0BAJJ3C,mBAAA,CAAc,YAAV,OAAK,sBACTA,mBAAA,CAEK,MAFL4C,WAEK,I,kBADH9C,mBAAA,CAAqEyC,SAAA,QArDjFC,WAAA,CAqDiCnC,MAAA,CAAAwC,eAAe,EAAzBC,MAAM;2BAAjBhD,mBAAA,CAAqE;QAA9BD,GAAG,EAAEiD;MAAM,GAAAnC,gBAAA,CAAKmC,MAAM;wCAI/BzC,MAAA,CAAA0C,QAAQ,CAACC,MAAM,Q,cAAjDlD,mBAAA,CAQM,OARNmD,WAQM,G,0BAPJjD,mBAAA,CAAc,YAAV,OAAK,sBACTA,mBAAA,CAKK,MALLkD,WAKK,I,kBAJHpD,mBAAA,CAGKyC,SAAA,QA/DjBC,WAAA,CA4DkCnC,MAAA,CAAA0C,QAAQ,EAAnBI,OAAO;2BAAlBrD,mBAAA,CAGK;QAH4BD,GAAG,EAAEsD,OAAO;QAAEvD,KAAK,EAAC;UACnDM,YAAA,CAA8BkD,kBAAA;QA7D5C5C,OAAA,EAAAC,QAAA,CA6DuB,MAAW,CAAXP,YAAA,CAAWmD,kBAAA,E;QA7DlCxC,CAAA;UAAAH,gBAAA,CA6D4C,GAC9B,GAAAC,gBAAA,CAAGwC,OAAO,iB;0CA9DxBjB,mBAAA,e,mCA0CmB7B,MAAA,CAAAiD,WAAW,E;IA1C9BzC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}