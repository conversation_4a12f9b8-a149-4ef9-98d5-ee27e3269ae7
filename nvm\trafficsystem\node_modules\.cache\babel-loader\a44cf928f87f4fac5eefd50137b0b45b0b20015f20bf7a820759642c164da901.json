{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, onMounted, computed, watch, onUnmounted } from 'vue';\nimport { useRouter } from 'vue-router';\nexport default {\n  name: 'NavbarComponent',\n  setup() {\n    const router = useRouter();\n    const user = ref(null);\n    const activeDropdown = ref(null);\n\n    // 计算属性：检查用户是否为管理员\n    const isAdmin = computed(() => {\n      if (!user.value) return false;\n\n      // 兼容不同格式的管理员角色名称（大小写不敏感比较）\n      const role = user.value.role?.toLowerCase() || '';\n      return role === 'admin' || role === 'administrator';\n    });\n\n    // 切换下拉菜单显示状态\n    const toggleDropdown = dropdownId => {\n      console.log(`切换下拉菜单: ${dropdownId}, 当前状态: ${activeDropdown.value === dropdownId ? '开启' : '关闭'}`);\n\n      // 如果点击当前已打开的下拉菜单，则关闭它\n      if (activeDropdown.value === dropdownId) {\n        activeDropdown.value = null;\n        console.log(`关闭下拉菜单: ${dropdownId}`);\n      } else {\n        // 否则打开新的下拉菜单，并关闭其他已打开的菜单\n        activeDropdown.value = dropdownId;\n        console.log(`打开下拉菜单: ${dropdownId}`);\n      }\n    };\n\n    // 关闭所有下拉菜单\n    const closeDropdowns = () => {\n      if (activeDropdown.value) {\n        console.log(`关闭活动下拉菜单: ${activeDropdown.value}`);\n        activeDropdown.value = null;\n      }\n    };\n\n    // 处理页面点击事件，点击菜单外部时关闭下拉菜单\n    const handleOutsideClick = event => {\n      // 如果没有活动的下拉菜单，无需处理\n      if (!activeDropdown.value) return;\n\n      // 检查点击是否发生在下拉菜单或下拉菜单触发器上\n      const isClickOnDropdown = event.target.closest('.dropdown-menu');\n      const isClickOnDropdownToggle = event.target.closest('.dropdown-toggle');\n\n      // 如果点击不是发生在下拉菜单或其触发器上，则关闭所有下拉菜单\n      if (!isClickOnDropdown && !isClickOnDropdownToggle) {\n        closeDropdowns();\n      }\n    };\n    onMounted(() => {\n      // 读取用户信息\n      const userInfo = localStorage.getItem('user');\n      if (userInfo) {\n        user.value = JSON.parse(userInfo);\n      }\n\n      // 添加点击事件监听，用于处理点击菜单外部区域\n      document.addEventListener('click', handleOutsideClick);\n    });\n\n    // 组件卸载时清理事件\n    onUnmounted(() => {\n      document.removeEventListener('click', handleOutsideClick);\n    });\n\n    // 退出登录\n    const logout = () => {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n      user.value = null;\n      activeDropdown.value = null; // 关闭任何打开的下拉菜单\n      router.push('/login');\n    };\n    return {\n      user,\n      logout,\n      isAdmin,\n      activeDropdown,\n      toggleDropdown,\n      closeDropdowns\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "computed", "watch", "onUnmounted", "useRouter", "name", "setup", "router", "user", "activeDropdown", "isAdmin", "value", "role", "toLowerCase", "toggleDropdown", "dropdownId", "console", "log", "closeDropdowns", "handleOutsideClick", "event", "isClickOnDropdown", "target", "closest", "isClickOnDropdownToggle", "userInfo", "localStorage", "getItem", "JSON", "parse", "document", "addEventListener", "removeEventListener", "logout", "removeItem", "push"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\common\\Navbar.vue"], "sourcesContent": ["<template>\n  <nav class=\"navbar navbar-expand-lg\">\n    <div class=\"container\">\n      <router-link class=\"navbar-brand\" to=\"/\">交通分析系统</router-link>\n      <button class=\"navbar-toggler\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#navbarNav\" aria-controls=\"navbarNav\" aria-expanded=\"false\" aria-label=\"Toggle navigation\">\n        <span class=\"navbar-toggler-icon\"></span>\n      </button>\n      <div class=\"collapse navbar-collapse\" id=\"navbarNav\">\n        <ul class=\"navbar-nav me-auto mb-2 mb-lg-0\">\n          <li class=\"nav-item dropdown\">\n            <a class=\"nav-link dropdown-toggle\" href=\"#\" id=\"analysisDropdown\" role=\"button\" @click.prevent=\"toggleDropdown('analysis')\">\n              分析功能\n            </a>\n            <ul class=\"dropdown-menu\" :class=\"{ show: activeDropdown === 'analysis' }\">\n              <li>\n                <router-link class=\"dropdown-item\" to=\"/upload\" @click=\"closeDropdowns\">\n                  <i class=\"bi bi-image\"></i> 图像分析\n                </router-link>\n              </li>\n              <li>\n                <router-link class=\"dropdown-item\" to=\"/video-upload\" @click=\"closeDropdowns\">\n                  <i class=\"bi bi-camera-video\"></i> 视频分析\n                </router-link>\n              </li>\n\n              <li><hr class=\"dropdown-divider\"></li>\n              <li>\n                <router-link class=\"dropdown-item\" to=\"/four-way-console?mode=new\" @click=\"closeDropdowns\">\n                  <i class=\"bi bi-speedometer2\"></i> 四方向分析控制台\n                </router-link>\n              </li>\n            </ul>\n          </li>\n          <li class=\"nav-item dropdown\">\n            <a class=\"nav-link dropdown-toggle\" href=\"#\" id=\"historyDropdown\" role=\"button\" @click.prevent=\"toggleDropdown('history')\">\n              历史记录\n            </a>\n            <ul class=\"dropdown-menu\" :class=\"{ show: activeDropdown === 'history' }\">\n              <li>\n                <router-link class=\"dropdown-item\" to=\"/history\" @click=\"closeDropdowns\">\n                  <i class=\"bi bi-image\"></i> 图像分析历史\n                </router-link>\n              </li>\n              <li>\n                <router-link class=\"dropdown-item\" to=\"/video-history\" @click=\"closeDropdowns\">\n                  <i class=\"bi bi-camera-video\"></i> 视频分析历史\n                </router-link>\n              </li>\n              <li><hr class=\"dropdown-divider\"></li>\n              <li>\n                <router-link class=\"dropdown-item\" to=\"/four-way-history\" @click=\"closeDropdowns\">\n                  <i class=\"bi bi-intersection\"></i> 四方向分析历史\n                </router-link>\n              </li>\n            </ul>\n          </li>\n          <li class=\"nav-item\" v-if=\"user\">\n            <router-link class=\"nav-link\" :class=\"{ active: $route.path === '/user/profile' }\" to=\"/user/profile\" @click=\"closeDropdowns\">个人信息</router-link>\n          </li>\n          <li class=\"nav-item\">\n            <router-link class=\"nav-link\" :class=\"{ active: $route.path === '/settings' }\" to=\"/settings\" @click=\"closeDropdowns\">\n              <i class=\"bi bi-gear\"></i> 系统设置\n            </router-link>\n          </li>\n          <li class=\"nav-item\" v-if=\"isAdmin\">\n            <router-link class=\"nav-link\" :class=\"{ active: $route.path === '/admin/users' }\" to=\"/admin/users\" @click=\"closeDropdowns\">\n              <i class=\"bi bi-people\"></i> 用户管理\n              <span class=\"admin-badge\">管理员</span>\n            </router-link>\n          </li>\n        </ul>\n        <div class=\"d-flex align-items-center\">\n          <div class=\"d-flex\" v-if=\"user\">\n            <span class=\"navbar-text me-3\">\n              欢迎, {{ user.username }}\n            </span>\n            <button class=\"logout-btn\" type=\"button\" @click=\"logout\">退出</button>\n          </div>\n          <div v-else>\n            <router-link class=\"auth-btn login-btn\" to=\"/login\">登录</router-link>\n            <router-link class=\"auth-btn register-btn\" to=\"/register\">注册</router-link>\n          </div>\n        </div>\n      </div>\n    </div>\n  </nav>\n</template>\n\n<script>\nimport { ref, onMounted, computed, watch, onUnmounted } from 'vue';\nimport { useRouter } from 'vue-router';\n\nexport default {\n  name: 'NavbarComponent',\n  setup() {\n    const router = useRouter();\n    const user = ref(null);\n    const activeDropdown = ref(null);\n    \n    // 计算属性：检查用户是否为管理员\n    const isAdmin = computed(() => {\n      if (!user.value) return false;\n      \n      // 兼容不同格式的管理员角色名称（大小写不敏感比较）\n      const role = user.value.role?.toLowerCase() || '';\n      return role === 'admin' || role === 'administrator';\n    });\n    \n    // 切换下拉菜单显示状态\n    const toggleDropdown = (dropdownId) => {\n      console.log(`切换下拉菜单: ${dropdownId}, 当前状态: ${activeDropdown.value === dropdownId ? '开启' : '关闭'}`);\n      \n      // 如果点击当前已打开的下拉菜单，则关闭它\n      if (activeDropdown.value === dropdownId) {\n        activeDropdown.value = null;\n        console.log(`关闭下拉菜单: ${dropdownId}`);\n      } else {\n        // 否则打开新的下拉菜单，并关闭其他已打开的菜单\n        activeDropdown.value = dropdownId;\n        console.log(`打开下拉菜单: ${dropdownId}`);\n      }\n    };\n    \n    // 关闭所有下拉菜单\n    const closeDropdowns = () => {\n      if (activeDropdown.value) {\n        console.log(`关闭活动下拉菜单: ${activeDropdown.value}`);\n        activeDropdown.value = null;\n      }\n    };\n    \n    // 处理页面点击事件，点击菜单外部时关闭下拉菜单\n    const handleOutsideClick = (event) => {\n      // 如果没有活动的下拉菜单，无需处理\n      if (!activeDropdown.value) return;\n      \n      // 检查点击是否发生在下拉菜单或下拉菜单触发器上\n      const isClickOnDropdown = event.target.closest('.dropdown-menu');\n      const isClickOnDropdownToggle = event.target.closest('.dropdown-toggle');\n      \n      // 如果点击不是发生在下拉菜单或其触发器上，则关闭所有下拉菜单\n      if (!isClickOnDropdown && !isClickOnDropdownToggle) {\n        closeDropdowns();\n      }\n    };\n    \n    onMounted(() => {\n      // 读取用户信息\n      const userInfo = localStorage.getItem('user');\n      if (userInfo) {\n        user.value = JSON.parse(userInfo);\n      }\n      \n      // 添加点击事件监听，用于处理点击菜单外部区域\n      document.addEventListener('click', handleOutsideClick);\n    });\n    \n    // 组件卸载时清理事件\n    onUnmounted(() => {\n      document.removeEventListener('click', handleOutsideClick);\n    });\n    \n    // 退出登录\n    const logout = () => {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n      user.value = null;\n      activeDropdown.value = null; // 关闭任何打开的下拉菜单\n      router.push('/login');\n    };\n    \n    return {\n      user,\n      logout,\n      isAdmin,\n      activeDropdown,\n      toggleDropdown,\n      closeDropdowns\n    };\n  }\n};\n</script>\n\n<style scoped>\n.navbar {\n  background-color: #111827;\n  padding: 1rem 0;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.navbar-brand {\n  font-weight: 800;\n  font-size: 1.5rem;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6);\n  -webkit-background-clip: text;\n  background-clip: text;\n  color: transparent;\n  text-decoration: none;\n  padding: 0.5rem 0;\n  transition: none;\n}\n\n.navbar-brand:hover {\n  transform: none;\n  opacity: 1;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6);\n  -webkit-background-clip: text;\n  background-clip: text;\n  color: transparent;\n}\n\n.navbar-nav {\n  margin-left: 2rem;\n}\n\n.nav-item {\n  margin: 0 0.5rem;\n  position: relative;\n}\n\n.nav-link {\n  color: #d1d5db;\n  font-weight: 500;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  transition: none;\n  text-decoration: none;\n}\n\n.nav-link:hover,\n.nav-link.active,\n.nav-link:focus,\n.nav-link:active,\n.nav-link:visited {\n  color: #d1d5db !important;\n  background-color: transparent !important;\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.nav-link i {\n  margin-right: 0.5rem;\n  color: #3b82f6;\n}\n\n.dropdown-toggle {\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n}\n\n.dropdown-toggle::after {\n  margin-left: 0.5rem;\n  border-top: 0.3em solid #3b82f6;\n}\n\n.dropdown-menu {\n  display: none;\n  position: absolute;\n  background-color: #1a2032;\n  border: 1px solid rgba(255, 255, 255, 0.08);\n  border-radius: 0.75rem;\n  min-width: 10rem;\n  padding: 0.5rem 0;\n  margin-top: 0.5rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  z-index: 1000;\n  overflow: hidden;\n  animation: dropdown-appear 0.2s ease;\n}\n\n@keyframes dropdown-appear {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n.dropdown-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 0.75rem 1.25rem;\n  color: #d1d5db;\n  text-decoration: none;\n  transition: none;\n  height: 36px;\n  line-height: normal;\n}\n\n.dropdown-item:hover,\n.dropdown-item:focus,\n.dropdown-item:active,\n.dropdown-item:visited {\n  background-color: transparent !important;\n  color: #d1d5db !important;\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.dropdown-item i {\n  color: #3b82f6;\n  font-size: 1rem;\n}\n\n.navbar-text {\n  color: #d1d5db;\n  padding: 0.5rem 1rem;\n}\n\n.admin-badge {\n  display: inline-block;\n  background: linear-gradient(135deg, #f59e0b, #d97706);\n  color: #111827;\n  font-size: 0.7rem;\n  font-weight: 700;\n  padding: 0.2rem 0.5rem;\n  border-radius: 1rem;\n  margin-left: 0.5rem;\n  text-transform: uppercase;\n}\n\n.auth-btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.5rem 1.25rem;\n  border-radius: 0.75rem;\n  font-weight: 600;\n  transition: none;\n  text-decoration: none;\n  margin-left: 0.5rem;\n  font-size: 0.9rem;\n}\n\n.login-btn {\n  background-color: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  color: #d1d5db;\n}\n\n.login-btn:hover,\n.login-btn:focus,\n.login-btn:active,\n.login-btn:visited {\n  background-color: transparent !important;\n  color: #d1d5db !important;\n  transform: none !important;\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.register-btn {\n  background: linear-gradient(135deg, #3b82f6, #2563eb);\n  color: white;\n  border: none;\n}\n\n.register-btn:hover,\n.register-btn:focus,\n.register-btn:active,\n.register-btn:visited {\n  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;\n  color: white !important;\n  transform: none !important;\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.logout-btn {\n  background: rgba(255, 255, 255, 0.05);\n  color: #d1d5db;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 0.5rem 1.25rem;\n  border-radius: 0.75rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: none;\n}\n\n.logout-btn:hover,\n.logout-btn:focus,\n.logout-btn:active,\n.logout-btn:visited {\n  background: rgba(255, 255, 255, 0.05) !important;\n  color: #d1d5db !important;\n  border-color: rgba(255, 255, 255, 0.1) !important;\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.navbar-toggler {\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  background-color: rgba(255, 255, 255, 0.03);\n}\n\n.navbar-toggler-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\");\n}\n\n/* 全局防止导航栏元素点击后变色 */\n.navbar a,\n.navbar button,\n.navbar .nav-link,\n.navbar .dropdown-item,\n.navbar .auth-btn {\n  -webkit-tap-highlight-color: transparent !important;\n  -webkit-touch-callout: none !important;\n  -webkit-user-select: none !important;\n  -moz-user-select: none !important;\n  -ms-user-select: none !important;\n  user-select: none !important;\n}\n\n.navbar a:focus,\n.navbar button:focus,\n.navbar .nav-link:focus,\n.navbar .dropdown-item:focus,\n.navbar .auth-btn:focus {\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.navbar a:active,\n.navbar button:active,\n.navbar .nav-link:active,\n.navbar .dropdown-item:active,\n.navbar .auth-btn:active {\n  outline: none !important;\n  box-shadow: none !important;\n  transform: none !important;\n}\n\n@media (max-width: 992px) {\n  .navbar-nav {\n    margin-left: 0;\n    margin-top: 1rem;\n  }\n  \n  .nav-item {\n    margin: 0;\n  }\n  \n  .dropdown-menu {\n    position: static;\n    background-color: rgba(26, 32, 50, 0.5);\n    border: none;\n    box-shadow: none;\n    width: 100%;\n    margin-top: 0.25rem;\n    margin-bottom: 0.25rem;\n    padding-left: 1.5rem;\n  }\n  \n  .auth-btn {\n    margin: 0.5rem 0;\n    width: 100%;\n    justify-content: center;\n  }\n}\n</style> "], "mappings": ";AAyFA,SAASA,GAAG,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,WAAU,QAAS,KAAK;AAClE,SAASC,SAAQ,QAAS,YAAY;AAEtC,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIH,SAAS,CAAC,CAAC;IAC1B,MAAMI,IAAG,GAAIT,GAAG,CAAC,IAAI,CAAC;IACtB,MAAMU,cAAa,GAAIV,GAAG,CAAC,IAAI,CAAC;;IAEhC;IACA,MAAMW,OAAM,GAAIT,QAAQ,CAAC,MAAM;MAC7B,IAAI,CAACO,IAAI,CAACG,KAAK,EAAE,OAAO,KAAK;;MAE7B;MACA,MAAMC,IAAG,GAAIJ,IAAI,CAACG,KAAK,CAACC,IAAI,EAAEC,WAAW,CAAC,KAAK,EAAE;MACjD,OAAOD,IAAG,KAAM,OAAM,IAAKA,IAAG,KAAM,eAAe;IACrD,CAAC,CAAC;;IAEF;IACA,MAAME,cAAa,GAAKC,UAAU,IAAK;MACrCC,OAAO,CAACC,GAAG,CAAC,WAAWF,UAAU,WAAWN,cAAc,CAACE,KAAI,KAAMI,UAAS,GAAI,IAAG,GAAI,IAAI,EAAE,CAAC;;MAEhG;MACA,IAAIN,cAAc,CAACE,KAAI,KAAMI,UAAU,EAAE;QACvCN,cAAc,CAACE,KAAI,GAAI,IAAI;QAC3BK,OAAO,CAACC,GAAG,CAAC,WAAWF,UAAU,EAAE,CAAC;MACtC,OAAO;QACL;QACAN,cAAc,CAACE,KAAI,GAAII,UAAU;QACjCC,OAAO,CAACC,GAAG,CAAC,WAAWF,UAAU,EAAE,CAAC;MACtC;IACF,CAAC;;IAED;IACA,MAAMG,cAAa,GAAIA,CAAA,KAAM;MAC3B,IAAIT,cAAc,CAACE,KAAK,EAAE;QACxBK,OAAO,CAACC,GAAG,CAAC,aAAaR,cAAc,CAACE,KAAK,EAAE,CAAC;QAChDF,cAAc,CAACE,KAAI,GAAI,IAAI;MAC7B;IACF,CAAC;;IAED;IACA,MAAMQ,kBAAiB,GAAKC,KAAK,IAAK;MACpC;MACA,IAAI,CAACX,cAAc,CAACE,KAAK,EAAE;;MAE3B;MACA,MAAMU,iBAAgB,GAAID,KAAK,CAACE,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC;MAChE,MAAMC,uBAAsB,GAAIJ,KAAK,CAACE,MAAM,CAACC,OAAO,CAAC,kBAAkB,CAAC;;MAExE;MACA,IAAI,CAACF,iBAAgB,IAAK,CAACG,uBAAuB,EAAE;QAClDN,cAAc,CAAC,CAAC;MAClB;IACF,CAAC;IAEDlB,SAAS,CAAC,MAAM;MACd;MACA,MAAMyB,QAAO,GAAIC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC7C,IAAIF,QAAQ,EAAE;QACZjB,IAAI,CAACG,KAAI,GAAIiB,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;MACnC;;MAEA;MACAK,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEZ,kBAAkB,CAAC;IACxD,CAAC,CAAC;;IAEF;IACAhB,WAAW,CAAC,MAAM;MAChB2B,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEb,kBAAkB,CAAC;IAC3D,CAAC,CAAC;;IAEF;IACA,MAAMc,MAAK,GAAIA,CAAA,KAAM;MACnBP,YAAY,CAACQ,UAAU,CAAC,YAAY,CAAC;MACrCR,YAAY,CAACQ,UAAU,CAAC,MAAM,CAAC;MAC/B1B,IAAI,CAACG,KAAI,GAAI,IAAI;MACjBF,cAAc,CAACE,KAAI,GAAI,IAAI,EAAE;MAC7BJ,MAAM,CAAC4B,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,OAAO;MACL3B,IAAI;MACJyB,MAAM;MACNvB,OAAO;MACPD,cAAc;MACdK,cAAc;MACdI;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}