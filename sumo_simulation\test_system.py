#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统测试脚本
验证SUMO + TraCI动态红绿灯决策系统是否正常工作
"""

import os
import sys
import logging
import tempfile
from pathlib import Path

# 添加模块路径
sys.path.append(os.path.dirname(__file__))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        import traci
        print("✓ TraCI导入成功")
    except ImportError as e:
        print(f"✗ TraCI导入失败: {e}")
        return False
    
    try:
        from algorithms.fixed_timing import FixedTimingController
        print("✓ 固定配时算法导入成功")
    except ImportError as e:
        print(f"✗ 固定配时算法导入失败: {e}")
        return False
    
    try:
        from algorithms.adaptive_timing import AdaptiveTimingController
        print("✓ 自适应配时算法导入成功")
    except ImportError as e:
        print(f"✗ 自适应配时算法导入失败: {e}")
        return False
    
    try:
        from controllers.traffic_controller import TrafficController
        print("✓ 交通控制器导入成功")
    except ImportError as e:
        print(f"✗ 交通控制器导入失败: {e}")
        return False
    
    try:
        from analysis.performance_analyzer import PerformanceAnalyzer
        print("✓ 性能分析器导入成功")
    except ImportError as e:
        print(f"✗ 性能分析器导入失败: {e}")
        return False
    
    return True

def test_algorithm_controllers():
    """测试算法控制器"""
    print("\n测试算法控制器...")
    
    try:
        from algorithms.fixed_timing import FixedTimingController
        from algorithms.adaptive_timing import AdaptiveTimingController
        
        # 测试固定配时控制器
        fixed_controller = FixedTimingController()
        phase_info = fixed_controller.get_current_phase(0)
        assert 'phase_state' in phase_info
        print("✓ 固定配时控制器工作正常")
        
        # 测试自适应配时控制器
        adaptive_controller = AdaptiveTimingController()
        traffic_data = {'east': 5, 'south': 3, 'west': 2, 'north': 4}
        state = adaptive_controller.get_next_phase_state(0, traffic_data)
        assert isinstance(state, str)
        print("✓ 自适应配时控制器工作正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 算法控制器测试失败: {e}")
        return False

def test_config_files():
    """测试配置文件"""
    print("\n测试配置文件...")
    
    config_files = [
        'config/intersection.net.xml',
        'config/traffic_flows.rou.xml',
        'config/detectors.add.xml',
        'config/simulation.sumocfg'
    ]
    
    all_exist = True
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✓ {config_file} 存在")
        else:
            print(f"✗ {config_file} 不存在")
            all_exist = False
    
    return all_exist

def test_directory_structure():
    """测试目录结构"""
    print("\n测试目录结构...")
    
    required_dirs = [
        'config',
        'algorithms',
        'controllers',
        'analysis',
        'results'
    ]
    
    all_exist = True
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✓ {directory}/ 目录存在")
        else:
            print(f"✗ {directory}/ 目录不存在")
            all_exist = False
    
    return all_exist

def test_sumo_installation():
    """测试SUMO安装"""
    print("\n测试SUMO安装...")
    
    try:
        import subprocess
        
        # 测试sumo命令
        result = subprocess.run(['sumo', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ SUMO命令行工具可用")
            print(f"  版本信息: {result.stdout.strip().split()[0]}")
        else:
            print("✗ SUMO命令行工具不可用")
            return False
        
        # 测试TraCI连接（不启动实际仿真）
        import traci
        print("✓ TraCI模块可用")
        
        return True
        
    except subprocess.TimeoutExpired:
        print("✗ SUMO命令超时")
        return False
    except FileNotFoundError:
        print("✗ 找不到SUMO命令，请确保SUMO已正确安装并添加到PATH")
        return False
    except Exception as e:
        print(f"✗ SUMO测试失败: {e}")
        return False

def test_performance_analyzer():
    """测试性能分析器"""
    print("\n测试性能分析器...")
    
    try:
        from analysis.performance_analyzer import PerformanceAnalyzer
        
        # 创建测试数据
        test_data = [
            {
                'simulation_time': 10,
                'avg_waiting_time': 25.5,
                'avg_speed': 8.2,
                'throughput': 1200,
                'total_jam_length': 45.3
            },
            {
                'simulation_time': 20,
                'avg_waiting_time': 28.1,
                'avg_speed': 7.8,
                'throughput': 1150,
                'total_jam_length': 52.1
            }
        ]
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            import json
            json.dump(test_data, f)
            temp_file = f.name
        
        try:
            # 测试分析器
            analyzer = PerformanceAnalyzer()
            analyzer.load_performance_data('test_algorithm', temp_file)
            
            stats = analyzer.calculate_summary_statistics('test_algorithm')
            assert 'avg_waiting_time' in stats
            
            efficiency_score = analyzer.calculate_efficiency_score('test_algorithm')
            assert 0 <= efficiency_score <= 100
            
            print("✓ 性能分析器工作正常")
            return True
            
        finally:
            # 清理临时文件
            os.unlink(temp_file)
        
    except Exception as e:
        print(f"✗ 性能分析器测试失败: {e}")
        return False

def test_visualization():
    """测试可视化模块"""
    print("\n测试可视化模块...")
    
    try:
        from analysis.visualization import TrafficVisualization
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        
        viz = TrafficVisualization()
        
        # 测试简单的图表创建
        test_data = {
            'Algorithm A': [25.5, 28.1, 26.3],
            'Algorithm B': [18.2, 19.5, 17.8]
        }
        
        fig = viz.plot_performance_comparison(test_data, 'Test Metric')
        assert fig is not None
        
        print("✓ 可视化模块工作正常")
        return True
        
    except Exception as e:
        print(f"✗ 可视化模块测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("="*60)
    print("SUMO + TraCI 动态红绿灯决策系统测试")
    print("="*60)
    
    tests = [
        ("模块导入", test_imports),
        ("目录结构", test_directory_structure),
        ("配置文件", test_config_files),
        ("SUMO安装", test_sumo_installation),
        ("算法控制器", test_algorithm_controllers),
        ("性能分析器", test_performance_analyzer),
        ("可视化模块", test_visualization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}测试出现异常: {e}")
    
    print("\n" + "="*60)
    print(f"测试结果: {passed}/{total} 通过")
    print("="*60)
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        print("\n可以运行以下命令开始仿真:")
        print("  python main.py --algorithm fixed")
        print("  python main.py --algorithm adaptive")
        print("  python main.py --algorithm all")
        return True
    else:
        print("❌ 部分测试失败，请检查系统配置。")
        return False

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.WARNING)
    
    # 切换到脚本目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    success = run_all_tests()
    sys.exit(0 if success else 1)
