{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\n/**\n * 交通分析工具函数\n * 用于计算车流量、拥挤等级、趋势分析等\n */\n\n/**\n * 拥挤等级配置\n */\nexport const CONGESTION_GRADES = {\n  A: {\n    min: 0,\n    max: 3,\n    label: '优秀',\n    description: '畅通无阻',\n    color: '#52c41a',\n    bgColor: '#f6ffed'\n  },\n  B: {\n    min: 4,\n    max: 7,\n    label: '良好',\n    description: '车流正常',\n    color: '#1890ff',\n    bgColor: '#e6f7ff'\n  },\n  C: {\n    min: 8,\n    max: 12,\n    label: '一般',\n    description: '略有拥挤',\n    color: '#faad14',\n    bgColor: '#fffbe6'\n  },\n  D: {\n    min: 13,\n    max: 18,\n    label: '较差',\n    description: '明显拥挤',\n    color: '#fa8c16',\n    bgColor: '#fff2e8'\n  },\n  E: {\n    min: 19,\n    max: 25,\n    label: '很差',\n    description: '严重拥挤',\n    color: '#f5222d',\n    bgColor: '#fff1f0'\n  },\n  F: {\n    min: 26,\n    max: Infinity,\n    label: '极差',\n    description: '极度拥挤',\n    color: '#a8071a',\n    bgColor: '#ffebee'\n  }\n};\n\n/**\n * 计算拥挤等级\n * @param {number} vehicleCount 车辆数量\n * @returns {string} 拥挤等级 (A-F)\n */\nexport function calculateCongestionGrade(vehicleCount) {\n  for (const [grade, config] of Object.entries(CONGESTION_GRADES)) {\n    if (vehicleCount >= config.min && vehicleCount <= config.max) {\n      return grade;\n    }\n  }\n  return 'F'; // 默认返回最高拥挤等级\n}\n\n/**\n * 获取拥挤等级配置\n * @param {string} grade 拥挤等级\n * @returns {object} 等级配置信息\n */\nexport function getCongestionGradeConfig(grade) {\n  return CONGESTION_GRADES[grade] || CONGESTION_GRADES.F;\n}\n\n/**\n * 计算移动平均值\n * @param {Array} dataArray 数据数组\n * @param {number} windowSize 窗口大小\n * @returns {number} 移动平均值\n */\nexport function calculateMovingAverage(dataArray, windowSize) {\n  if (!dataArray || dataArray.length === 0) return 0;\n  const window = dataArray.slice(-windowSize);\n  const sum = window.reduce((acc, val) => acc + (val || 0), 0);\n  return Math.round(sum / window.length * 10) / 10; // 保留一位小数\n}\n\n/**\n * 计算多个窗口的移动平均值\n * @param {Array} dataArray 车辆数量数据数组\n * @returns {object} 包含不同窗口大小的移动平均值\n */\nexport function calculateMultipleMovingAverages(dataArray) {\n  return {\n    frame5: calculateMovingAverage(dataArray, 5),\n    frame10: calculateMovingAverage(dataArray, 10),\n    frame30: calculateMovingAverage(dataArray, 30)\n  };\n}\n\n/**\n * 分析车流趋势\n * @param {Array} recentData 最近的车辆数量数据\n * @param {number} windowSize 分析窗口大小，默认为5\n * @returns {object} 趋势分析结果\n */\nexport function analyzeTrend(recentData, windowSize = 5) {\n  if (!recentData || recentData.length < 2) {\n    return {\n      trend: 'stable',\n      strength: 0,\n      description: '数据不足'\n    };\n  }\n  const window = recentData.slice(-windowSize);\n  if (window.length < 2) {\n    return {\n      trend: 'stable',\n      strength: 0,\n      description: '数据不足'\n    };\n  }\n\n  // 计算趋势斜率\n  const n = window.length;\n  const sumX = n * (n - 1) / 2;\n  const sumY = window.reduce((sum, val) => sum + val, 0);\n  const sumXY = window.reduce((sum, val, index) => sum + val * index, 0);\n  const sumX2 = n * (n - 1) * (2 * n - 1) / 6;\n  const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);\n\n  // 判断趋势方向和强度\n  let trend, strength, description;\n  const absSlope = Math.abs(slope);\n  if (absSlope < 0.1) {\n    trend = 'stable';\n    strength = 0;\n    description = '车流稳定';\n  } else if (slope > 0) {\n    trend = 'rising';\n    strength = Math.min(absSlope * 10, 5); // 强度1-5\n    description = absSlope > 0.5 ? '车流快速增加' : '车流缓慢增加';\n  } else {\n    trend = 'falling';\n    strength = Math.min(absSlope * 10, 5);\n    description = absSlope > 0.5 ? '车流快速减少' : '车流缓慢减少';\n  }\n  return {\n    trend,\n    strength,\n    description\n  };\n}\n\n/**\n * 生成交通管理策略建议\n * @param {string} grade 拥挤等级\n * @param {object} trendInfo 趋势信息\n * @param {object} movingAverages 移动平均值\n * @returns {object} 策略建议\n */\nexport function generateTrafficStrategy(grade, trendInfo, movingAverages) {\n  const strategies = {\n    A: {\n      primary: '保持现状',\n      secondary: '正常通行，无需特殊措施',\n      icon: '✅',\n      priority: 'low'\n    },\n    B: {\n      primary: '正常管理',\n      secondary: '继续监控，准备应对可能的车流增加',\n      icon: '👍',\n      priority: 'low'\n    },\n    C: {\n      primary: '加强监控',\n      secondary: '建议优化信号灯配时，引导车流分散',\n      icon: '⚠️',\n      priority: 'medium'\n    },\n    D: {\n      primary: '主动干预',\n      secondary: '延长绿灯时间，考虑开放应急车道',\n      icon: '🚨',\n      priority: 'medium'\n    },\n    E: {\n      primary: '紧急措施',\n      secondary: '启动交通疏导，派遣交警现场指挥',\n      icon: '🚨',\n      priority: 'high'\n    },\n    F: {\n      primary: '全面管制',\n      secondary: '实施交通管制，寻找替代路线',\n      icon: '🔴',\n      priority: 'critical'\n    }\n  };\n  const baseStrategy = strategies[grade] || strategies.F;\n\n  // 根据趋势调整策略\n  let adjustedStrategy = {\n    ...baseStrategy\n  };\n  if (trendInfo.trend === 'rising' && trendInfo.strength > 2) {\n    adjustedStrategy.secondary += '，车流上升趋势明显，需提前准备';\n    adjustedStrategy.priority = adjustedStrategy.priority === 'low' ? 'medium' : 'high';\n  } else if (trendInfo.trend === 'falling' && trendInfo.strength > 2) {\n    adjustedStrategy.secondary += '，车流下降趋势明显，可适当放松管制';\n  }\n  return adjustedStrategy;\n}\n\n/**\n * 格式化车辆数量显示\n * @param {number} count 车辆数量\n * @returns {string} 格式化后的显示文本\n */\nexport function formatVehicleCount(count) {\n  if (count === 0) return '无车辆';\n  if (count === 1) return '1辆车';\n  return `${count}辆车`;\n}\n\n/**\n * 计算拥挤度百分比\n * @param {string} grade 拥挤等级\n * @param {number} vehicleCount 当前车辆数\n * @returns {number} 拥挤度百分比 (0-100)\n */\nexport function calculateCongestionPercentage(grade, vehicleCount) {\n  const config = getCongestionGradeConfig(grade);\n  const maxReasonable = 30; // 设定一个合理的最大值用于百分比计算\n\n  return Math.min(vehicleCount / maxReasonable * 100, 100);\n}", "map": {"version": 3, "names": ["CONGESTION_GRADES", "A", "min", "max", "label", "description", "color", "bgColor", "B", "C", "D", "E", "F", "Infinity", "calculateCongestionGrade", "vehicleCount", "grade", "config", "Object", "entries", "getCongestionGradeConfig", "calculateMovingAverage", "dataArray", "windowSize", "length", "window", "slice", "sum", "reduce", "acc", "val", "Math", "round", "calculateMultipleMovingAverages", "frame5", "frame10", "frame30", "analyzeTrend", "recentData", "trend", "strength", "n", "sumX", "sumY", "sumXY", "index", "sumX2", "slope", "absSlope", "abs", "generateTrafficStrategy", "trendInfo", "movingAverages", "strategies", "primary", "secondary", "icon", "priority", "baseStrategy", "adjustedStrategy", "formatVehicleCount", "count", "calculateCongestionPercentage", "maxReasonable"], "sources": ["D:/code/nvm/trafficsystem/src/utils/trafficAnalysisUtils.js"], "sourcesContent": ["/**\n * 交通分析工具函数\n * 用于计算车流量、拥挤等级、趋势分析等\n */\n\n/**\n * 拥挤等级配置\n */\nexport const CONGESTION_GRADES = {\n  A: { min: 0, max: 3, label: '优秀', description: '畅通无阻', color: '#52c41a', bgColor: '#f6ffed' },\n  B: { min: 4, max: 7, label: '良好', description: '车流正常', color: '#1890ff', bgColor: '#e6f7ff' },\n  C: { min: 8, max: 12, label: '一般', description: '略有拥挤', color: '#faad14', bgColor: '#fffbe6' },\n  D: { min: 13, max: 18, label: '较差', description: '明显拥挤', color: '#fa8c16', bgColor: '#fff2e8' },\n  E: { min: 19, max: 25, label: '很差', description: '严重拥挤', color: '#f5222d', bgColor: '#fff1f0' },\n  F: { min: 26, max: Infinity, label: '极差', description: '极度拥挤', color: '#a8071a', bgColor: '#ffebee' }\n}\n\n/**\n * 计算拥挤等级\n * @param {number} vehicleCount 车辆数量\n * @returns {string} 拥挤等级 (A-F)\n */\nexport function calculateCongestionGrade(vehicleCount) {\n  for (const [grade, config] of Object.entries(CONGESTION_GRADES)) {\n    if (vehicleCount >= config.min && vehicleCount <= config.max) {\n      return grade\n    }\n  }\n  return 'F' // 默认返回最高拥挤等级\n}\n\n/**\n * 获取拥挤等级配置\n * @param {string} grade 拥挤等级\n * @returns {object} 等级配置信息\n */\nexport function getCongestionGradeConfig(grade) {\n  return CONGESTION_GRADES[grade] || CONGESTION_GRADES.F\n}\n\n/**\n * 计算移动平均值\n * @param {Array} dataArray 数据数组\n * @param {number} windowSize 窗口大小\n * @returns {number} 移动平均值\n */\nexport function calculateMovingAverage(dataArray, windowSize) {\n  if (!dataArray || dataArray.length === 0) return 0\n  \n  const window = dataArray.slice(-windowSize)\n  const sum = window.reduce((acc, val) => acc + (val || 0), 0)\n  return Math.round((sum / window.length) * 10) / 10 // 保留一位小数\n}\n\n/**\n * 计算多个窗口的移动平均值\n * @param {Array} dataArray 车辆数量数据数组\n * @returns {object} 包含不同窗口大小的移动平均值\n */\nexport function calculateMultipleMovingAverages(dataArray) {\n  return {\n    frame5: calculateMovingAverage(dataArray, 5),\n    frame10: calculateMovingAverage(dataArray, 10),\n    frame30: calculateMovingAverage(dataArray, 30)\n  }\n}\n\n/**\n * 分析车流趋势\n * @param {Array} recentData 最近的车辆数量数据\n * @param {number} windowSize 分析窗口大小，默认为5\n * @returns {object} 趋势分析结果\n */\nexport function analyzeTrend(recentData, windowSize = 5) {\n  if (!recentData || recentData.length < 2) {\n    return { trend: 'stable', strength: 0, description: '数据不足' }\n  }\n\n  const window = recentData.slice(-windowSize)\n  if (window.length < 2) {\n    return { trend: 'stable', strength: 0, description: '数据不足' }\n  }\n\n  // 计算趋势斜率\n  const n = window.length\n  const sumX = (n * (n - 1)) / 2\n  const sumY = window.reduce((sum, val) => sum + val, 0)\n  const sumXY = window.reduce((sum, val, index) => sum + val * index, 0)\n  const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6\n\n  const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)\n  \n  // 判断趋势方向和强度\n  let trend, strength, description\n  const absSlope = Math.abs(slope)\n  \n  if (absSlope < 0.1) {\n    trend = 'stable'\n    strength = 0\n    description = '车流稳定'\n  } else if (slope > 0) {\n    trend = 'rising'\n    strength = Math.min(absSlope * 10, 5) // 强度1-5\n    description = absSlope > 0.5 ? '车流快速增加' : '车流缓慢增加'\n  } else {\n    trend = 'falling'\n    strength = Math.min(absSlope * 10, 5)\n    description = absSlope > 0.5 ? '车流快速减少' : '车流缓慢减少'\n  }\n\n  return { trend, strength, description }\n}\n\n/**\n * 生成交通管理策略建议\n * @param {string} grade 拥挤等级\n * @param {object} trendInfo 趋势信息\n * @param {object} movingAverages 移动平均值\n * @returns {object} 策略建议\n */\nexport function generateTrafficStrategy(grade, trendInfo, movingAverages) {\n  const strategies = {\n    A: {\n      primary: '保持现状',\n      secondary: '正常通行，无需特殊措施',\n      icon: '✅',\n      priority: 'low'\n    },\n    B: {\n      primary: '正常管理',\n      secondary: '继续监控，准备应对可能的车流增加',\n      icon: '👍',\n      priority: 'low'\n    },\n    C: {\n      primary: '加强监控',\n      secondary: '建议优化信号灯配时，引导车流分散',\n      icon: '⚠️',\n      priority: 'medium'\n    },\n    D: {\n      primary: '主动干预',\n      secondary: '延长绿灯时间，考虑开放应急车道',\n      icon: '🚨',\n      priority: 'medium'\n    },\n    E: {\n      primary: '紧急措施',\n      secondary: '启动交通疏导，派遣交警现场指挥',\n      icon: '🚨',\n      priority: 'high'\n    },\n    F: {\n      primary: '全面管制',\n      secondary: '实施交通管制，寻找替代路线',\n      icon: '🔴',\n      priority: 'critical'\n    }\n  }\n\n  const baseStrategy = strategies[grade] || strategies.F\n  \n  // 根据趋势调整策略\n  let adjustedStrategy = { ...baseStrategy }\n  \n  if (trendInfo.trend === 'rising' && trendInfo.strength > 2) {\n    adjustedStrategy.secondary += '，车流上升趋势明显，需提前准备'\n    adjustedStrategy.priority = adjustedStrategy.priority === 'low' ? 'medium' : 'high'\n  } else if (trendInfo.trend === 'falling' && trendInfo.strength > 2) {\n    adjustedStrategy.secondary += '，车流下降趋势明显，可适当放松管制'\n  }\n\n  return adjustedStrategy\n}\n\n/**\n * 格式化车辆数量显示\n * @param {number} count 车辆数量\n * @returns {string} 格式化后的显示文本\n */\nexport function formatVehicleCount(count) {\n  if (count === 0) return '无车辆'\n  if (count === 1) return '1辆车'\n  return `${count}辆车`\n}\n\n/**\n * 计算拥挤度百分比\n * @param {string} grade 拥挤等级\n * @param {number} vehicleCount 当前车辆数\n * @returns {number} 拥挤度百分比 (0-100)\n */\nexport function calculateCongestionPercentage(grade, vehicleCount) {\n  const config = getCongestionGradeConfig(grade)\n  const maxReasonable = 30 // 设定一个合理的最大值用于百分比计算\n  \n  return Math.min((vehicleCount / maxReasonable) * 100, 100)\n}\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,MAAMA,iBAAiB,GAAG;EAC/BC,CAAC,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,IAAI;IAAEC,WAAW,EAAE,MAAM;IAAEC,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAU,CAAC;EAC7FC,CAAC,EAAE;IAAEN,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,CAAC;IAAEC,KAAK,EAAE,IAAI;IAAEC,WAAW,EAAE,MAAM;IAAEC,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAU,CAAC;EAC7FE,CAAC,EAAE;IAAEP,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,WAAW,EAAE,MAAM;IAAEC,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAU,CAAC;EAC9FG,CAAC,EAAE;IAAER,GAAG,EAAE,EAAE;IAAEC,GAAG,EAAE,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,WAAW,EAAE,MAAM;IAAEC,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAU,CAAC;EAC/FI,CAAC,EAAE;IAAET,GAAG,EAAE,EAAE;IAAEC,GAAG,EAAE,EAAE;IAAEC,KAAK,EAAE,IAAI;IAAEC,WAAW,EAAE,MAAM;IAAEC,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAU,CAAC;EAC/FK,CAAC,EAAE;IAAEV,GAAG,EAAE,EAAE;IAAEC,GAAG,EAAEU,QAAQ;IAAET,KAAK,EAAE,IAAI;IAAEC,WAAW,EAAE,MAAM;IAAEC,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAU;AACtG,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,wBAAwBA,CAACC,YAAY,EAAE;EACrD,KAAK,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACnB,iBAAiB,CAAC,EAAE;IAC/D,IAAIe,YAAY,IAAIE,MAAM,CAACf,GAAG,IAAIa,YAAY,IAAIE,MAAM,CAACd,GAAG,EAAE;MAC5D,OAAOa,KAAK;IACd;EACF;EACA,OAAO,GAAG,EAAC;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,wBAAwBA,CAACJ,KAAK,EAAE;EAC9C,OAAOhB,iBAAiB,CAACgB,KAAK,CAAC,IAAIhB,iBAAiB,CAACY,CAAC;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,sBAAsBA,CAACC,SAAS,EAAEC,UAAU,EAAE;EAC5D,IAAI,CAACD,SAAS,IAAIA,SAAS,CAACE,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;EAElD,MAAMC,MAAM,GAAGH,SAAS,CAACI,KAAK,CAAC,CAACH,UAAU,CAAC;EAC3C,MAAMI,GAAG,GAAGF,MAAM,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5D,OAAOC,IAAI,CAACC,KAAK,CAAEL,GAAG,GAAGF,MAAM,CAACD,MAAM,GAAI,EAAE,CAAC,GAAG,EAAE,EAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,+BAA+BA,CAACX,SAAS,EAAE;EACzD,OAAO;IACLY,MAAM,EAAEb,sBAAsB,CAACC,SAAS,EAAE,CAAC,CAAC;IAC5Ca,OAAO,EAAEd,sBAAsB,CAACC,SAAS,EAAE,EAAE,CAAC;IAC9Cc,OAAO,EAAEf,sBAAsB,CAACC,SAAS,EAAE,EAAE;EAC/C,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASe,YAAYA,CAACC,UAAU,EAAEf,UAAU,GAAG,CAAC,EAAE;EACvD,IAAI,CAACe,UAAU,IAAIA,UAAU,CAACd,MAAM,GAAG,CAAC,EAAE;IACxC,OAAO;MAAEe,KAAK,EAAE,QAAQ;MAAEC,QAAQ,EAAE,CAAC;MAAEnC,WAAW,EAAE;IAAO,CAAC;EAC9D;EAEA,MAAMoB,MAAM,GAAGa,UAAU,CAACZ,KAAK,CAAC,CAACH,UAAU,CAAC;EAC5C,IAAIE,MAAM,CAACD,MAAM,GAAG,CAAC,EAAE;IACrB,OAAO;MAAEe,KAAK,EAAE,QAAQ;MAAEC,QAAQ,EAAE,CAAC;MAAEnC,WAAW,EAAE;IAAO,CAAC;EAC9D;;EAEA;EACA,MAAMoC,CAAC,GAAGhB,MAAM,CAACD,MAAM;EACvB,MAAMkB,IAAI,GAAID,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,GAAI,CAAC;EAC9B,MAAME,IAAI,GAAGlB,MAAM,CAACG,MAAM,CAAC,CAACD,GAAG,EAAEG,GAAG,KAAKH,GAAG,GAAGG,GAAG,EAAE,CAAC,CAAC;EACtD,MAAMc,KAAK,GAAGnB,MAAM,CAACG,MAAM,CAAC,CAACD,GAAG,EAAEG,GAAG,EAAEe,KAAK,KAAKlB,GAAG,GAAGG,GAAG,GAAGe,KAAK,EAAE,CAAC,CAAC;EACtE,MAAMC,KAAK,GAAIL,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAI,CAAC;EAE7C,MAAMM,KAAK,GAAG,CAACN,CAAC,GAAGG,KAAK,GAAGF,IAAI,GAAGC,IAAI,KAAKF,CAAC,GAAGK,KAAK,GAAGJ,IAAI,GAAGA,IAAI,CAAC;;EAEnE;EACA,IAAIH,KAAK,EAAEC,QAAQ,EAAEnC,WAAW;EAChC,MAAM2C,QAAQ,GAAGjB,IAAI,CAACkB,GAAG,CAACF,KAAK,CAAC;EAEhC,IAAIC,QAAQ,GAAG,GAAG,EAAE;IAClBT,KAAK,GAAG,QAAQ;IAChBC,QAAQ,GAAG,CAAC;IACZnC,WAAW,GAAG,MAAM;EACtB,CAAC,MAAM,IAAI0C,KAAK,GAAG,CAAC,EAAE;IACpBR,KAAK,GAAG,QAAQ;IAChBC,QAAQ,GAAGT,IAAI,CAAC7B,GAAG,CAAC8C,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC,EAAC;IACtC3C,WAAW,GAAG2C,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ;EACpD,CAAC,MAAM;IACLT,KAAK,GAAG,SAAS;IACjBC,QAAQ,GAAGT,IAAI,CAAC7B,GAAG,CAAC8C,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC3C,WAAW,GAAG2C,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ;EACpD;EAEA,OAAO;IAAET,KAAK;IAAEC,QAAQ;IAAEnC;EAAY,CAAC;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6C,uBAAuBA,CAAClC,KAAK,EAAEmC,SAAS,EAAEC,cAAc,EAAE;EACxE,MAAMC,UAAU,GAAG;IACjBpD,CAAC,EAAE;MACDqD,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,aAAa;MACxBC,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAE;IACZ,CAAC;IACDjD,CAAC,EAAE;MACD8C,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,kBAAkB;MAC7BC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACZ,CAAC;IACDhD,CAAC,EAAE;MACD6C,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,kBAAkB;MAC7BC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACZ,CAAC;IACD/C,CAAC,EAAE;MACD4C,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,iBAAiB;MAC5BC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACZ,CAAC;IACD9C,CAAC,EAAE;MACD2C,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,iBAAiB;MAC5BC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACZ,CAAC;IACD7C,CAAC,EAAE;MACD0C,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,eAAe;MAC1BC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACZ;EACF,CAAC;EAED,MAAMC,YAAY,GAAGL,UAAU,CAACrC,KAAK,CAAC,IAAIqC,UAAU,CAACzC,CAAC;;EAEtD;EACA,IAAI+C,gBAAgB,GAAG;IAAE,GAAGD;EAAa,CAAC;EAE1C,IAAIP,SAAS,CAACZ,KAAK,KAAK,QAAQ,IAAIY,SAAS,CAACX,QAAQ,GAAG,CAAC,EAAE;IAC1DmB,gBAAgB,CAACJ,SAAS,IAAI,iBAAiB;IAC/CI,gBAAgB,CAACF,QAAQ,GAAGE,gBAAgB,CAACF,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,MAAM;EACrF,CAAC,MAAM,IAAIN,SAAS,CAACZ,KAAK,KAAK,SAAS,IAAIY,SAAS,CAACX,QAAQ,GAAG,CAAC,EAAE;IAClEmB,gBAAgB,CAACJ,SAAS,IAAI,mBAAmB;EACnD;EAEA,OAAOI,gBAAgB;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACxC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;EAC7B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;EAC7B,OAAO,GAAGA,KAAK,IAAI;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,6BAA6BA,CAAC9C,KAAK,EAAED,YAAY,EAAE;EACjE,MAAME,MAAM,GAAGG,wBAAwB,CAACJ,KAAK,CAAC;EAC9C,MAAM+C,aAAa,GAAG,EAAE,EAAC;;EAEzB,OAAOhC,IAAI,CAAC7B,GAAG,CAAEa,YAAY,GAAGgD,aAAa,GAAI,GAAG,EAAE,GAAG,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}