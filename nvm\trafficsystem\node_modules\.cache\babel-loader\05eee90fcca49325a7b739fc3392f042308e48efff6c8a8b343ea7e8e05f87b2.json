{"ast": null, "code": "import { computed } from 'vue';\nimport { getCongestionGradeConfig, calculateCongestionPercentage } from '@/utils/trafficAnalysisUtils';\nexport default {\n  name: 'CongestionGradeIndicator',\n  props: {\n    grade: {\n      type: String,\n      required: true,\n      validator: value => ['A', 'B', 'C', 'D', 'E', 'F'].includes(value)\n    },\n    vehicleCount: {\n      type: Number,\n      required: true,\n      default: 0\n    }\n  },\n  setup(props) {\n    const gradeConfig = computed(() => getCongestionGradeConfig(props.grade));\n    const percentage = computed(() => calculateCongestionPercentage(props.grade, props.vehicleCount));\n    return {\n      gradeConfig,\n      percentage\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "getCongestionGradeConfig", "calculateCongestionPercentage", "name", "props", "grade", "type", "String", "required", "validator", "value", "includes", "vehicleCount", "Number", "default", "setup", "gradeConfig", "percentage"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\traffic\\CongestionGradeIndicator.vue"], "sourcesContent": ["<template>\n  <div class=\"congestion-grade-indicator\">\n    <div class=\"grade-display\">\n      <div \n        class=\"grade-badge\"\n        :style=\"{ \n          backgroundColor: gradeConfig.color,\n          boxShadow: `0 0 20px ${gradeConfig.color}40`\n        }\"\n      >\n        {{ grade }}\n      </div>\n      <div class=\"grade-info\">\n        <div class=\"grade-label\">{{ gradeConfig.label }}</div>\n        <div class=\"grade-description\">{{ gradeConfig.description }}</div>\n      </div>\n    </div>\n    \n    <div class=\"progress-container\">\n      <div class=\"progress-bar\">\n        <div \n          class=\"progress-fill\"\n          :style=\"{ \n            width: `${percentage}%`,\n            backgroundColor: gradeConfig.color\n          }\"\n        ></div>\n      </div>\n      <div class=\"progress-text\">拥挤度: {{ percentage.toFixed(1) }}%</div>\n    </div>\n    \n    <div class=\"vehicle-count\">\n      <span class=\"count-number\">{{ vehicleCount }}</span>\n      <span class=\"count-label\">辆车</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { getCongestionGradeConfig, calculateCongestionPercentage } from '@/utils/trafficAnalysisUtils'\n\nexport default {\n  name: 'CongestionGradeIndicator',\n  props: {\n    grade: {\n      type: String,\n      required: true,\n      validator: (value) => ['A', 'B', 'C', 'D', 'E', 'F'].includes(value)\n    },\n    vehicleCount: {\n      type: Number,\n      required: true,\n      default: 0\n    }\n  },\n  setup(props) {\n    const gradeConfig = computed(() => getCongestionGradeConfig(props.grade))\n    const percentage = computed(() => calculateCongestionPercentage(props.grade, props.vehicleCount))\n    \n    return {\n      gradeConfig,\n      percentage\n    }\n  }\n}\n</script>\n\n<style scoped>\n.congestion-grade-indicator {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e8e8e8;\n}\n\n.grade-display {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.grade-badge {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 24px;\n  font-weight: bold;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n  transition: all 0.3s ease;\n}\n\n.grade-info {\n  flex: 1;\n}\n\n.grade-label {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.grade-description {\n  font-size: 14px;\n  color: #666;\n}\n\n.progress-container {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.progress-bar {\n  height: 8px;\n  background: #f0f0f0;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n}\n\n.progress-fill {\n  height: 100%;\n  border-radius: 4px;\n  transition: width 0.5s ease, background-color 0.3s ease;\n  position: relative;\n}\n\n.progress-fill::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  animation: shimmer 2s infinite;\n}\n\n@keyframes shimmer {\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n}\n\n.progress-text {\n  font-size: 12px;\n  color: #666;\n  text-align: center;\n}\n\n.vehicle-count {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  gap: 4px;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n\n.count-number {\n  font-size: 28px;\n  font-weight: bold;\n  color: #333;\n}\n\n.count-label {\n  font-size: 14px;\n  color: #666;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .congestion-grade-indicator {\n    padding: 16px;\n  }\n  \n  .grade-badge {\n    width: 50px;\n    height: 50px;\n    font-size: 20px;\n  }\n  \n  .grade-label {\n    font-size: 16px;\n  }\n  \n  .count-number {\n    font-size: 24px;\n  }\n}\n</style>\n"], "mappings": "AAuCA,SAASA,QAAO,QAAS,KAAI;AAC7B,SAASC,wBAAwB,EAAEC,6BAA4B,QAAS,8BAA6B;AAErG,eAAe;EACbC,IAAI,EAAE,0BAA0B;EAChCC,KAAK,EAAE;IACLC,KAAK,EAAE;MACLC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAGC,KAAK,IAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACC,QAAQ,CAACD,KAAK;IACrE,CAAC;IACDE,YAAY,EAAE;MACZN,IAAI,EAAEO,MAAM;MACZL,QAAQ,EAAE,IAAI;MACdM,OAAO,EAAE;IACX;EACF,CAAC;EACDC,KAAKA,CAACX,KAAK,EAAE;IACX,MAAMY,WAAU,GAAIhB,QAAQ,CAAC,MAAMC,wBAAwB,CAACG,KAAK,CAACC,KAAK,CAAC;IACxE,MAAMY,UAAS,GAAIjB,QAAQ,CAAC,MAAME,6BAA6B,CAACE,KAAK,CAACC,KAAK,EAAED,KAAK,CAACQ,YAAY,CAAC;IAEhG,OAAO;MACLI,WAAW;MACXC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}