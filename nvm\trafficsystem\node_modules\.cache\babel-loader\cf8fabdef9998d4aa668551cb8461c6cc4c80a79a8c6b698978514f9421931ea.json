{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"traffic-analysis-test\"\n};\nconst _hoisted_2 = {\n  class: \"test-header\"\n};\nconst _hoisted_3 = {\n  class: \"test-controls\"\n};\nconst _hoisted_4 = {\n  class: \"test-content\"\n};\nconst _hoisted_5 = {\n  class: \"control-group\"\n};\nconst _hoisted_6 = {\n  class: \"control-group\"\n};\nconst _hoisted_7 = {\n  class: \"status-info\"\n};\nconst _hoisted_8 = {\n  class: \"panel-container\"\n};\nconst _hoisted_9 = {\n  class: \"log-header\"\n};\nconst _hoisted_10 = {\n  class: \"log-content\"\n};\nconst _hoisted_11 = {\n  class: \"log-time\"\n};\nconst _hoisted_12 = {\n  class: \"log-message\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_slider = _resolveComponent(\"el-slider\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_IntelligentTrafficPanel = _resolveComponent(\"IntelligentTrafficPanel\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[5] || (_cache[5] = _createElementVNode(\"h2\", null, \"智能交通状态面板测试\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n    onClick: $setup.startSimulation,\n    disabled: $setup.isSimulating\n  }, {\n    default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"开始模拟\")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n    onClick: $setup.stopSimulation,\n    disabled: !$setup.isSimulating\n  }, {\n    default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"停止模拟\")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n    onClick: $setup.resetData\n  }, {\n    default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"重置数据\")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 模拟控制面板 \"), _createVNode(_component_el_card, {\n    class: \"simulation-control\"\n  }, {\n    header: _withCtx(() => _cache[6] || (_cache[6] = [_createElementVNode(\"span\", null, \"模拟控制\", -1 /* HOISTED */)])),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"label\", null, \"当前车辆数量: \" + _toDisplayString($setup.currentVehicleCount), 1 /* TEXT */), _createVNode(_component_el_slider, {\n      modelValue: $setup.currentVehicleCount,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.currentVehicleCount = $event),\n      min: 0,\n      max: 50,\n      \"show-input\": \"\",\n      onChange: $setup.onVehicleCountChange\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]), _createElementVNode(\"div\", _hoisted_6, [_cache[7] || (_cache[7] = _createElementVNode(\"label\", null, \"模拟速度 (ms):\", -1 /* HOISTED */)), _createVNode(_component_el_input_number, {\n      modelValue: $setup.simulationSpeed,\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.simulationSpeed = $event),\n      min: 500,\n      max: 5000,\n      step: 500\n    }, null, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"p\", null, \"模拟状态: \" + _toDisplayString($setup.isSimulating ? '运行中' : '已停止'), 1 /* TEXT */), _createElementVNode(\"p\", null, \"数据点数量: \" + _toDisplayString($setup.dataPoints), 1 /* TEXT */)])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 智能交通状态面板 \"), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_IntelligentTrafficPanel, {\n    \"current-vehicle-count\": $setup.currentVehicleCount,\n    \"auto-update\": true,\n    onStrategyApplied: $setup.handleStrategyApplied,\n    onDataUpdated: $setup.handleDataUpdated,\n    ref: \"trafficPanel\"\n  }, null, 8 /* PROPS */, [\"current-vehicle-count\", \"onStrategyApplied\", \"onDataUpdated\"])]), _createCommentVNode(\" 事件日志 \"), _createVNode(_component_el_card, {\n    class: \"event-log\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", null, \"事件日志\", -1 /* HOISTED */)), _createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: $setup.clearLog\n    }, {\n      default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\"清空日志\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_10, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.eventLogs, (log, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: _normalizeClass([\"log-item\", log.type])\n      }, [_createElementVNode(\"span\", _hoisted_11, _toDisplayString(log.time), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_12, _toDisplayString(log.message), 1 /* TEXT */)], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))])]),\n    _: 1 /* STABLE */\n  })])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_button", "onClick", "$setup", "startSimulation", "disabled", "isSimulating", "default", "_withCtx", "_cache", "_createTextVNode", "_", "stopSimulation", "resetData", "_hoisted_4", "_createCommentVNode", "_component_el_card", "header", "_hoisted_5", "_toDisplayString", "currentVehicleCount", "_component_el_slider", "modelValue", "$event", "min", "max", "onChange", "onVehicleCountChange", "_hoisted_6", "_component_el_input_number", "simulationSpeed", "step", "_hoisted_7", "dataPoints", "_hoisted_8", "_component_IntelligentTrafficPanel", "onStrategyApplied", "handleStrategyApplied", "onDataUpdated", "handleDataUpdated", "ref", "_hoisted_9", "size", "clearLog", "_hoisted_10", "_Fragment", "_renderList", "eventLogs", "log", "index", "key", "_normalizeClass", "type", "_hoisted_11", "time", "_hoisted_12", "message"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\TrafficAnalysisTest.vue"], "sourcesContent": ["<template>\n  <div class=\"traffic-analysis-test\">\n    <div class=\"test-header\">\n      <h2>智能交通状态面板测试</h2>\n      <div class=\"test-controls\">\n        <el-button @click=\"startSimulation\" :disabled=\"isSimulating\">开始模拟</el-button>\n        <el-button @click=\"stopSimulation\" :disabled=\"!isSimulating\">停止模拟</el-button>\n        <el-button @click=\"resetData\">重置数据</el-button>\n      </div>\n    </div>\n\n    <div class=\"test-content\">\n      <!-- 模拟控制面板 -->\n      <el-card class=\"simulation-control\">\n        <template #header>\n          <span>模拟控制</span>\n        </template>\n        <div class=\"control-group\">\n          <label>当前车辆数量: {{ currentVehicleCount }}</label>\n          <el-slider \n            v-model=\"currentVehicleCount\" \n            :min=\"0\" \n            :max=\"50\" \n            show-input\n            @change=\"onVehicleCountChange\"\n          />\n        </div>\n        <div class=\"control-group\">\n          <label>模拟速度 (ms):</label>\n          <el-input-number \n            v-model=\"simulationSpeed\" \n            :min=\"500\" \n            :max=\"5000\" \n            :step=\"500\"\n          />\n        </div>\n        <div class=\"status-info\">\n          <p>模拟状态: {{ isSimulating ? '运行中' : '已停止' }}</p>\n          <p>数据点数量: {{ dataPoints }}</p>\n        </div>\n      </el-card>\n\n      <!-- 智能交通状态面板 -->\n      <div class=\"panel-container\">\n        <IntelligentTrafficPanel \n          :current-vehicle-count=\"currentVehicleCount\"\n          :auto-update=\"true\"\n          @strategy-applied=\"handleStrategyApplied\"\n          @data-updated=\"handleDataUpdated\"\n          ref=\"trafficPanel\"\n        />\n      </div>\n\n      <!-- 事件日志 -->\n      <el-card class=\"event-log\">\n        <template #header>\n          <div class=\"log-header\">\n            <span>事件日志</span>\n            <el-button size=\"small\" @click=\"clearLog\">清空日志</el-button>\n          </div>\n        </template>\n        <div class=\"log-content\">\n          <div \n            v-for=\"(log, index) in eventLogs\" \n            :key=\"index\"\n            class=\"log-item\"\n            :class=\"log.type\"\n          >\n            <span class=\"log-time\">{{ log.time }}</span>\n            <span class=\"log-message\">{{ log.message }}</span>\n          </div>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport IntelligentTrafficPanel from '@/components/traffic/IntelligentTrafficPanel.vue'\n\nexport default {\n  name: 'TrafficAnalysisTest',\n  components: {\n    IntelligentTrafficPanel\n  },\n  setup() {\n    const currentVehicleCount = ref(5)\n    const isSimulating = ref(false)\n    const simulationSpeed = ref(2000)\n    const dataPoints = ref(0)\n    const eventLogs = ref([])\n    const trafficPanel = ref(null)\n    \n    let simulationTimer = null\n    \n    // 添加日志\n    const addLog = (message, type = 'info') => {\n      const log = {\n        time: new Date().toLocaleTimeString(),\n        message,\n        type\n      }\n      eventLogs.value.unshift(log)\n      \n      // 限制日志数量\n      if (eventLogs.value.length > 50) {\n        eventLogs.value.pop()\n      }\n    }\n    \n    // 生成随机车辆数量\n    const generateRandomVehicleCount = () => {\n      // 模拟真实的交通流量变化\n      const patterns = [\n        // 低峰时段 (0-5辆)\n        () => Math.floor(Math.random() * 6),\n        // 正常时段 (3-12辆)\n        () => Math.floor(Math.random() * 10) + 3,\n        // 高峰时段 (8-25辆)\n        () => Math.floor(Math.random() * 18) + 8,\n        // 拥堵时段 (15-40辆)\n        () => Math.floor(Math.random() * 26) + 15\n      ]\n      \n      const pattern = patterns[Math.floor(Math.random() * patterns.length)]\n      return pattern()\n    }\n    \n    // 开始模拟\n    const startSimulation = () => {\n      if (isSimulating.value) return\n      \n      isSimulating.value = true\n      addLog('开始交通流量模拟', 'success')\n      \n      simulationTimer = setInterval(() => {\n        currentVehicleCount.value = generateRandomVehicleCount()\n        dataPoints.value++\n        addLog(`更新车辆数量: ${currentVehicleCount.value}`)\n      }, simulationSpeed.value)\n    }\n    \n    // 停止模拟\n    const stopSimulation = () => {\n      if (!isSimulating.value) return\n      \n      isSimulating.value = false\n      if (simulationTimer) {\n        clearInterval(simulationTimer)\n        simulationTimer = null\n      }\n      addLog('停止交通流量模拟', 'warning')\n    }\n    \n    // 重置数据\n    const resetData = () => {\n      stopSimulation()\n      currentVehicleCount.value = 5\n      dataPoints.value = 0\n      if (trafficPanel.value) {\n        trafficPanel.value.clearData()\n      }\n      addLog('重置所有数据', 'info')\n    }\n    \n    // 车辆数量变化处理\n    const onVehicleCountChange = (value) => {\n      addLog(`手动设置车辆数量: ${value}`)\n    }\n    \n    // 策略应用处理\n    const handleStrategyApplied = (strategyData) => {\n      addLog(`应用交通策略: ${strategyData.strategy.primary}`, 'success')\n      ElMessage.success(`策略已应用: ${strategyData.strategy.primary}`)\n    }\n    \n    // 数据更新处理\n    const handleDataUpdated = (trafficData) => {\n      addLog(`数据更新 - 等级: ${trafficData.grade}, 趋势: ${trafficData.trend.description}`)\n    }\n    \n    // 清空日志\n    const clearLog = () => {\n      eventLogs.value = []\n      addLog('日志已清空')\n    }\n    \n    // 组件挂载时初始化\n    onMounted(() => {\n      addLog('交通分析测试页面已加载', 'success')\n    })\n    \n    // 组件卸载时清理\n    onUnmounted(() => {\n      stopSimulation()\n    })\n    \n    return {\n      currentVehicleCount,\n      isSimulating,\n      simulationSpeed,\n      dataPoints,\n      eventLogs,\n      trafficPanel,\n      startSimulation,\n      stopSimulation,\n      resetData,\n      onVehicleCountChange,\n      handleStrategyApplied,\n      handleDataUpdated,\n      clearLog\n    }\n  }\n}\n</script>\n\n<style scoped>\n.traffic-analysis-test {\n  padding: 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n.test-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.test-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.test-controls {\n  display: flex;\n  gap: 12px;\n}\n\n.test-content {\n  display: grid;\n  grid-template-columns: 300px 1fr;\n  grid-template-rows: auto 1fr;\n  gap: 20px;\n  height: calc(100vh - 200px);\n}\n\n.simulation-control {\n  grid-row: 1 / 3;\n}\n\n.panel-container {\n  grid-column: 2;\n  grid-row: 1;\n}\n\n.event-log {\n  grid-column: 2;\n  grid-row: 2;\n  max-height: 400px;\n}\n\n.control-group {\n  margin-bottom: 20px;\n}\n\n.control-group label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #333;\n}\n\n.status-info {\n  padding: 16px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  margin-top: 20px;\n}\n\n.status-info p {\n  margin: 4px 0;\n  color: #666;\n}\n\n.log-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.log-content {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.log-item {\n  display: flex;\n  gap: 12px;\n  padding: 8px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.log-item:last-child {\n  border-bottom: none;\n}\n\n.log-time {\n  font-size: 12px;\n  color: #999;\n  min-width: 80px;\n}\n\n.log-message {\n  flex: 1;\n  font-size: 14px;\n}\n\n.log-item.success .log-message {\n  color: #52c41a;\n}\n\n.log-item.warning .log-message {\n  color: #fa8c16;\n}\n\n.log-item.error .log-message {\n  color: #f5222d;\n}\n\n.log-item.info .log-message {\n  color: #333;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .test-content {\n    grid-template-columns: 1fr;\n    grid-template-rows: auto auto auto;\n  }\n  \n  .simulation-control {\n    grid-row: 1;\n    grid-column: 1;\n  }\n  \n  .panel-container {\n    grid-row: 2;\n    grid-column: 1;\n  }\n  \n  .event-log {\n    grid-row: 3;\n    grid-column: 1;\n  }\n}\n\n@media (max-width: 768px) {\n  .traffic-analysis-test {\n    padding: 16px;\n  }\n  \n  .test-header {\n    flex-direction: column;\n    gap: 16px;\n    align-items: flex-start;\n  }\n  \n  .test-controls {\n    width: 100%;\n    justify-content: flex-start;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAe;;EAOvBA,KAAK,EAAC;AAAc;;EAMhBA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;EASrBA,KAAK,EAAC;AAAa;;EAOrBA,KAAK,EAAC;AAAiB;;EAanBA,KAAK,EAAC;AAAY;;EAKpBA,KAAK,EAAC;AAAa;;EAOdA,KAAK,EAAC;AAAU;;EAChBA,KAAK,EAAC;AAAa;;;;;;;uBApEnCC,mBAAA,CAyEM,OAzENC,UAyEM,GAxEJC,mBAAA,CAOM,OAPNC,UAOM,G,0BANJD,mBAAA,CAAmB,YAAf,YAAU,sBACdA,mBAAA,CAIM,OAJNE,UAIM,GAHJC,YAAA,CAA6EC,oBAAA;IAAjEC,OAAK,EAAEC,MAAA,CAAAC,eAAe;IAAGC,QAAQ,EAAEF,MAAA,CAAAG;;IALvDC,OAAA,EAAAC,QAAA,CAKqE,MAAIC,MAAA,QAAAA,MAAA,OALzEC,gBAAA,CAKqE,MAAI,E;IALzEC,CAAA;8CAMQX,YAAA,CAA6EC,oBAAA;IAAjEC,OAAK,EAAEC,MAAA,CAAAS,cAAc;IAAGP,QAAQ,GAAGF,MAAA,CAAAG;;IANvDC,OAAA,EAAAC,QAAA,CAMqE,MAAIC,MAAA,QAAAA,MAAA,OANzEC,gBAAA,CAMqE,MAAI,E;IANzEC,CAAA;8CAOQX,YAAA,CAA8CC,oBAAA;IAAlCC,OAAK,EAAEC,MAAA,CAAAU;EAAS;IAPpCN,OAAA,EAAAC,QAAA,CAOsC,MAAIC,MAAA,QAAAA,MAAA,OAP1CC,gBAAA,CAOsC,MAAI,E;IAP1CC,CAAA;sCAWId,mBAAA,CA8DM,OA9DNiB,UA8DM,GA7DJC,mBAAA,YAAe,EACff,YAAA,CA2BUgB,kBAAA;IA3BDtB,KAAK,EAAC;EAAoB;IACtBuB,MAAM,EAAAT,QAAA,CACf,MAAiBC,MAAA,QAAAA,MAAA,OAAjBZ,mBAAA,CAAiB,cAAX,MAAI,oB;IAfpBU,OAAA,EAAAC,QAAA,CAiBQ,MASM,CATNX,mBAAA,CASM,OATNqB,UASM,GARJrB,mBAAA,CAAgD,eAAzC,UAAQ,GAAAsB,gBAAA,CAAGhB,MAAA,CAAAiB,mBAAmB,kBACrCpB,YAAA,CAMEqB,oBAAA;MAzBZC,UAAA,EAoBqBnB,MAAA,CAAAiB,mBAAmB;MApBxC,uBAAAX,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAoBqBpB,MAAA,CAAAiB,mBAAmB,GAAAG,MAAA;MAC3BC,GAAG,EAAE,CAAC;MACNC,GAAG,EAAE,EAAE;MACR,YAAU,EAAV,EAAU;MACTC,QAAM,EAAEvB,MAAA,CAAAwB;2DAGb9B,mBAAA,CAQM,OARN+B,UAQM,G,0BAPJ/B,mBAAA,CAAyB,eAAlB,YAAU,sBACjBG,YAAA,CAKE6B,0BAAA;MAlCZP,UAAA,EA8BqBnB,MAAA,CAAA2B,eAAe;MA9BpC,uBAAArB,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA8BqBpB,MAAA,CAAA2B,eAAe,GAAAP,MAAA;MACvBC,GAAG,EAAE,GAAG;MACRC,GAAG,EAAE,IAAI;MACTM,IAAI,EAAE;+CAGXlC,mBAAA,CAGM,OAHNmC,UAGM,GAFJnC,mBAAA,CAA+C,WAA5C,QAAM,GAAAsB,gBAAA,CAAGhB,MAAA,CAAAG,YAAY,kCACxBT,mBAAA,CAA8B,WAA3B,SAAO,GAAAsB,gBAAA,CAAGhB,MAAA,CAAA8B,UAAU,iB;IAtCjCtB,CAAA;MA0CMI,mBAAA,cAAiB,EACjBlB,mBAAA,CAQM,OARNqC,UAQM,GAPJlC,YAAA,CAMEmC,kCAAA;IALC,uBAAqB,EAAEhC,MAAA,CAAAiB,mBAAmB;IAC1C,aAAW,EAAE,IAAI;IACjBgB,iBAAgB,EAAEjC,MAAA,CAAAkC,qBAAqB;IACvCC,aAAY,EAAEnC,MAAA,CAAAoC,iBAAiB;IAChCC,GAAG,EAAC;8FAIRzB,mBAAA,UAAa,EACbf,YAAA,CAkBUgB,kBAAA;IAlBDtB,KAAK,EAAC;EAAW;IACbuB,MAAM,EAAAT,QAAA,CACf,MAGM,CAHNX,mBAAA,CAGM,OAHN4C,UAGM,G,0BAFJ5C,mBAAA,CAAiB,cAAX,MAAI,sBACVG,YAAA,CAA0DC,oBAAA;MAA/CyC,IAAI,EAAC,OAAO;MAAExC,OAAK,EAAEC,MAAA,CAAAwC;;MA1D5CpC,OAAA,EAAAC,QAAA,CA0DsD,MAAIC,MAAA,QAAAA,MAAA,OA1D1DC,gBAAA,CA0DsD,MAAI,E;MA1D1DC,CAAA;;IAAAJ,OAAA,EAAAC,QAAA,CA6DQ,MAUM,CAVNX,mBAAA,CAUM,OAVN+C,WAUM,I,kBATJjD,mBAAA,CAQMkD,SAAA,QAtEhBC,WAAA,CA+DmC3C,MAAA,CAAA4C,SAAS,EA/D5C,CA+DoBC,GAAG,EAAEC,KAAK;2BADpBtD,mBAAA,CAQM;QANHuD,GAAG,EAAED,KAAK;QACXvD,KAAK,EAjEjByD,eAAA,EAiEkB,UAAU,EACRH,GAAG,CAACI,IAAI;UAEhBvD,mBAAA,CAA4C,QAA5CwD,WAA4C,EAAAlC,gBAAA,CAAlB6B,GAAG,CAACM,IAAI,kBAClCzD,mBAAA,CAAkD,QAAlD0D,WAAkD,EAAApC,gBAAA,CAArB6B,GAAG,CAACQ,OAAO,iB;;IArEpD7C,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}