#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交通控制器
使用TraCI接口控制SUMO仿真中的信号灯
"""

import os
import sys
import time
import logging
from typing import Dict, Any, Optional, List
import traci
import traci.constants as tc

# 添加算法模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from algorithms.fixed_timing import FixedTimingController
from algorithms.adaptive_timing import AdaptiveTimingController

class TrafficController:
    """交通信号灯控制器"""
    
    def __init__(self, algorithm_type: str = 'fixed', config: Dict[str, Any] = None):
        """
        初始化交通控制器
        
        Args:
            algorithm_type: 算法类型 ('fixed', 'adaptive', 'intelligent')
            config: 配置参数
        """
        self.algorithm_type = algorithm_type
        self.config = config or {}
        
        # 初始化算法控制器
        self.controller = self._create_algorithm_controller()
        
        # TraCI连接状态
        self.connected = False
        
        # 信号灯ID
        self.traffic_light_id = 'center'
        
        # 检测器ID列表
        self.detectors = {
            'east_in': ['det_east_in_0', 'det_east_in_1'],
            'south_in': ['det_south_in_0', 'det_south_in_1'],
            'west_in': ['det_west_in_0', 'det_west_in_1'],
            'north_in': ['det_north_in_0', 'det_north_in_1']
        }
        
        # 排队检测器ID列表
        self.queue_detectors = {
            'east': ['queue_east_in_0', 'queue_east_in_1'],
            'south': ['queue_south_in_0', 'queue_south_in_1'],
            'west': ['queue_west_in_0', 'queue_west_in_1'],
            'north': ['queue_north_in_0', 'queue_north_in_1']
        }
        
        # 性能数据收集
        self.performance_data = []
        self.last_collection_time = 0
        self.collection_interval = 10  # 每10秒收集一次数据
        
        # 日志记录
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"交通控制器初始化完成，算法类型: {algorithm_type}")
    
    def _create_algorithm_controller(self):
        """创建算法控制器"""
        if self.algorithm_type == 'fixed':
            return FixedTimingController(self.config)
        elif self.algorithm_type == 'adaptive':
            return AdaptiveTimingController(self.config)
        else:
            raise ValueError(f"不支持的算法类型: {self.algorithm_type}")
    
    def connect_to_sumo(self, sumo_cmd: List[str], port: int = 8813):
        """
        连接到SUMO仿真
        
        Args:
            sumo_cmd: SUMO启动命令
            port: TraCI端口
        """
        try:
            # 启动SUMO
            traci.start(sumo_cmd, port=port)
            self.connected = True
            self.logger.info(f"成功连接到SUMO，端口: {port}")
            
            # 验证信号灯是否存在
            if self.traffic_light_id not in traci.trafficlight.getIDList():
                raise Exception(f"信号灯 {self.traffic_light_id} 不存在")
            
            self.logger.info(f"找到信号灯: {self.traffic_light_id}")
            
        except Exception as e:
            self.logger.error(f"连接SUMO失败: {str(e)}")
            raise
    
    def disconnect_from_sumo(self):
        """断开与SUMO的连接"""
        if self.connected:
            traci.close()
            self.connected = False
            self.logger.info("已断开与SUMO的连接")
    
    def collect_traffic_data(self) -> Dict[str, Any]:
        """
        收集交通数据
        
        Returns:
            包含各方向车辆数量和排队信息的字典
        """
        traffic_data = {}
        
        try:
            # 收集各方向的车辆数量
            for direction, detector_ids in self.detectors.items():
                total_vehicles = 0
                for detector_id in detector_ids:
                    if detector_id in traci.inductionloop.getIDList():
                        # 获取最近一个时间步的车辆数量
                        vehicles = traci.inductionloop.getLastStepVehicleNumber(detector_id)
                        total_vehicles += vehicles
                
                # 去掉方向后缀 '_in'
                clean_direction = direction.replace('_in', '')
                traffic_data[clean_direction] = total_vehicles
            
            # 收集排队长度信息
            queue_data = {}
            for direction, detector_ids in self.queue_detectors.items():
                total_queue_length = 0
                for detector_id in detector_ids:
                    if detector_id in traci.lanearea.getIDList():
                        # 获取排队长度（占用的车辆数）
                        queue_length = traci.lanearea.getLastStepVehicleNumber(detector_id)
                        total_queue_length += queue_length
                
                queue_data[direction] = total_queue_length
            
            traffic_data['queue_lengths'] = queue_data
            
            # 添加仿真时间
            traffic_data['simulation_time'] = traci.simulation.getTime()
            
        except Exception as e:
            self.logger.error(f"收集交通数据时出错: {str(e)}")
            # 返回默认数据
            traffic_data = {
                'east': 0, 'south': 0, 'west': 0, 'north': 0,
                'queue_lengths': {'east': 0, 'south': 0, 'west': 0, 'north': 0},
                'simulation_time': traci.simulation.getTime() if self.connected else 0
            }
        
        return traffic_data
    
    def collect_performance_data(self) -> Dict[str, Any]:
        """
        收集性能数据
        
        Returns:
            性能数据字典
        """
        try:
            # 获取基础仿真数据
            sim_time = traci.simulation.getTime()
            vehicle_count = traci.vehicle.getIDCount()
            
            # 计算平均等待时间
            total_waiting_time = 0
            vehicle_ids = traci.vehicle.getIDList()
            
            if vehicle_ids:
                for veh_id in vehicle_ids:
                    waiting_time = traci.vehicle.getWaitingTime(veh_id)
                    total_waiting_time += waiting_time
                
                avg_waiting_time = total_waiting_time / len(vehicle_ids)
            else:
                avg_waiting_time = 0
            
            # 获取交通数据
            traffic_data = self.collect_traffic_data()
            
            # 计算总排队长度
            total_queue_length = sum(traffic_data['queue_lengths'].values())
            
            # 计算通行效率（简化版）
            throughput = vehicle_count / max(sim_time / 3600, 0.001)  # 车辆/小时
            
            performance_data = {
                'simulation_time': sim_time,
                'vehicle_count': vehicle_count,
                'avg_waiting_time': avg_waiting_time,
                'total_queue_length': total_queue_length,
                'throughput': throughput,
                'algorithm_type': self.algorithm_type,
                'traffic_data': traffic_data
            }
            
            # 添加算法特定的性能指标
            algorithm_metrics = self.controller.get_performance_metrics(traffic_data)
            performance_data.update(algorithm_metrics)
            
            return performance_data
            
        except Exception as e:
            self.logger.error(f"收集性能数据时出错: {str(e)}")
            return {
                'simulation_time': 0,
                'vehicle_count': 0,
                'avg_waiting_time': 0,
                'total_queue_length': 0,
                'throughput': 0,
                'algorithm_type': self.algorithm_type,
                'error': str(e)
            }
    
    def update_traffic_light(self, simulation_time: float):
        """
        更新信号灯状态
        
        Args:
            simulation_time: 当前仿真时间
        """
        try:
            # 收集交通数据
            traffic_data = self.collect_traffic_data()
            
            # 获取新的信号灯状态
            new_state = self.controller.get_next_phase_state(simulation_time, traffic_data)
            
            # 设置信号灯状态
            traci.trafficlight.setRedYellowGreenState(self.traffic_light_id, new_state)
            
            # 记录相位信息
            if hasattr(self.controller, 'get_current_phase_info'):
                phase_info = self.controller.get_current_phase_info(simulation_time)
                self.logger.debug(f"当前相位: {phase_info.get('phase_description', 'Unknown')}")
            
        except Exception as e:
            self.logger.error(f"更新信号灯时出错: {str(e)}")
    
    def run_simulation(self, duration: int = 3600) -> List[Dict[str, Any]]:
        """
        运行仿真
        
        Args:
            duration: 仿真持续时间（秒）
            
        Returns:
            性能数据列表
        """
        if not self.connected:
            raise Exception("未连接到SUMO")
        
        self.logger.info(f"开始运行仿真，持续时间: {duration}秒")
        
        # 重置控制器
        self.controller.reset()
        
        # 清空性能数据
        self.performance_data = []
        self.last_collection_time = 0
        
        step = 0
        start_time = time.time()
        
        try:
            while traci.simulation.getMinExpectedNumber() > 0 and step < duration:
                # 执行仿真步骤
                traci.simulationStep()
                
                # 获取当前仿真时间
                sim_time = traci.simulation.getTime()
                
                # 更新信号灯
                self.update_traffic_light(sim_time)
                
                # 定期收集性能数据
                if sim_time - self.last_collection_time >= self.collection_interval:
                    performance_data = self.collect_performance_data()
                    self.performance_data.append(performance_data)
                    self.last_collection_time = sim_time
                
                step += 1
                
                # 每100步输出一次进度
                if step % 100 == 0:
                    progress = (step / duration) * 100
                    self.logger.info(f"仿真进度: {progress:.1f}% (步骤 {step}/{duration})")
        
        except Exception as e:
            self.logger.error(f"仿真运行时出错: {str(e)}")
            raise
        
        finally:
            real_time = time.time() - start_time
            self.logger.info(f"仿真完成，实际用时: {real_time:.2f}秒")
        
        return self.performance_data
    
    def get_algorithm_info(self) -> Dict[str, Any]:
        """获取算法信息"""
        return self.controller.get_algorithm_info()
    
    def save_performance_data(self, filename: str):
        """
        保存性能数据到文件
        
        Args:
            filename: 文件名
        """
        import json
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.performance_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"性能数据已保存到: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存性能数据时出错: {str(e)}")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建控制器
    controller = TrafficController('fixed')
    
    print("算法信息:")
    print(controller.get_algorithm_info())
