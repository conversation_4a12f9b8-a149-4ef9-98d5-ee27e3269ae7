{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';\nimport { useRouter, useRoute } from 'vue-router';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Grid, Upload, VideoCamera, DataAnalysis, Document, Plus, MoreFilled, Refresh, InfoFilled, Loading } from '@element-plus/icons-vue';\n\n// 导入组件\nimport FourWayVideoUpload from '@/components/analysis/FourWayVideoUpload.vue';\nimport FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue';\nimport TrafficAnalysisDashboard from '@/components/analysis/TrafficAnalysisDashboard.vue';\nimport IntelligentTrafficReport from '@/components/analysis/IntelligentTrafficReport.vue';\nimport StepNavigator from '@/components/analysis/StepNavigator.vue';\n\n// 导入API\nimport { getFourWayTaskStatus, getFourWayAnalysisResult, generateFourWayTrafficReport } from '@/api/video';\nexport default {\n  name: 'FourWayAnalysisConsole',\n  components: {\n    Grid,\n    Upload,\n    VideoCamera,\n    DataAnalysis,\n    Document,\n    Plus,\n    MoreFilled,\n    Refresh,\n    InfoFilled,\n    Loading,\n    FourWayVideoUpload,\n    FourWayRealtimeViewer,\n    TrafficAnalysisDashboard,\n    IntelligentTrafficReport,\n    StepNavigator\n  },\n  setup() {\n    const router = useRouter();\n    const route = useRoute();\n\n    // 响应式数据\n    const currentStep = ref(0);\n    const currentTaskId = ref('');\n    const currentTask = ref({\n      id: '',\n      name: '四方向交通分析任务',\n      status: 'waiting',\n      progress: 0,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    const reportData = ref(null);\n    const activeConnections = ref(0);\n    const lastUpdateTime = ref('');\n    const isInitializing = ref(true);\n    const taskStatusPolling = ref(null);\n\n    // 系统状态\n    const systemStatus = reactive({\n      type: 'success',\n      text: '正常运行'\n    });\n\n    // 计算属性\n    const totalTasks = computed(() => 1);\n    const activeTasks = computed(() => currentStep.value > 0 && currentStep.value < 4 ? 1 : 0);\n    const completedTasks = computed(() => currentStep.value === 4 ? 1 : 0);\n    const canUpload = computed(() => true);\n    const canDetect = computed(() => currentStep.value >= 1);\n    const canAnalyze = computed(() => currentStep.value >= 2);\n    const canExport = computed(() => currentStep.value >= 3);\n\n    // 方法\n\n    // 初始化和状态检测方法\n    const initializeFromRoute = async () => {\n      try {\n        isInitializing.value = true;\n\n        // 检查URL参数中的mode参数\n        const sessionMode = route.query.mode;\n        const urlTaskId = route.query.taskId || route.params.taskId;\n        console.log('页面初始化 - 模式:', sessionMode, '任务ID:', urlTaskId);\n\n        // 根据模式参数决定初始化策略\n        if (sessionMode === 'new') {\n          console.log('检测到新会话模式，清理之前的状态');\n          await initializeNewSession();\n        } else if (urlTaskId) {\n          console.log('从URL参数检测到任务ID:', urlTaskId);\n          currentTaskId.value = urlTaskId;\n          // 获取任务状态并设置对应步骤\n          await fetchTaskStatusAndSetStep(urlTaskId);\n        } else {\n          // 智能检测会话模式\n          await detectSessionMode();\n        }\n      } catch (error) {\n        console.error('初始化失败:', error);\n        ElMessage.error('初始化页面状态失败: ' + error.message);\n      } finally {\n        isInitializing.value = false;\n      }\n    };\n    const fetchTaskStatusAndSetStep = async taskId => {\n      try {\n        const token = localStorage.getItem('auth_token');\n        if (!token) {\n          throw new Error('未找到认证令牌');\n        }\n        const response = await fetch(`/api/video-analysis/four-way/${taskId}/status`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`获取任务状态失败: ${response.status}`);\n        }\n        const taskData = await response.json();\n        console.log('获取到任务状态:', taskData);\n\n        // 更新当前任务信息\n        currentTask.value = {\n          id: taskData.taskId || taskId,\n          name: taskData.name || '四方向交通分析任务',\n          status: taskData.status || 'waiting',\n          progress: taskData.progress || 0,\n          createdAt: taskData.createdAt ? new Date(taskData.createdAt) : new Date(),\n          updatedAt: taskData.updatedAt ? new Date(taskData.updatedAt) : new Date()\n        };\n\n        // 根据任务状态设置对应步骤\n        setStepByTaskStatus(taskData.status, taskData.progress);\n\n        // 如果任务正在处理中，开始轮询状态\n        if (['queued', 'processing'].includes(taskData.status)) {\n          startTaskStatusPolling(taskId);\n        }\n      } catch (error) {\n        console.error('获取任务状态失败:', error);\n        ElMessage.warning('无法获取任务状态，将从上传步骤开始');\n        currentStep.value = 0;\n      }\n    };\n    const setStepByTaskStatus = (status, progress = 0) => {\n      console.log('根据任务状态设置步骤:', {\n        status,\n        progress\n      });\n      switch (status) {\n        case 'queued':\n        case 'uploading':\n          currentStep.value = 0; // 上传步骤\n          ElMessage.info('任务正在排队中，请等待处理');\n          break;\n        case 'processing':\n          if (progress < 50) {\n            currentStep.value = 1; // 实时检测步骤\n            ElMessage.info('任务正在进行实时检测');\n          } else if (progress < 90) {\n            currentStep.value = 1; // 仍在检测阶段\n            ElMessage.info('实时检测进行中');\n          } else {\n            currentStep.value = 2; // 智能分析步骤\n            ElMessage.info('正在进行智能分析');\n          }\n          break;\n        case 'completed':\n          currentStep.value = 2; // 跳转到智能分析步骤\n          ElMessage.success('任务已完成，可以查看分析结果');\n          break;\n        case 'failed':\n          currentStep.value = 0; // 回到上传步骤\n          ElMessage.error('任务处理失败，请重新上传');\n          break;\n        default:\n          currentStep.value = 0; // 默认从上传开始\n          break;\n      }\n    };\n\n    // 智能会话检测机制\n    const detectSessionMode = async () => {\n      try {\n        // 检查会话存储中是否有活跃任务\n        const sessionData = sessionStorage.getItem('fourWayActiveTask');\n        if (sessionData) {\n          const taskInfo = JSON.parse(sessionData);\n          const sessionAge = Date.now() - taskInfo.timestamp;\n          const maxSessionAge = 24 * 60 * 60 * 1000; // 24小时\n\n          console.log('检测到会话数据:', taskInfo, '会话年龄:', sessionAge / 1000 / 60, '分钟');\n\n          // 如果会话数据过期，清理并开始新会话\n          if (sessionAge > maxSessionAge) {\n            console.log('会话数据已过期，开始新会话');\n            await initializeNewSession();\n            return;\n          }\n\n          // 显示用户选择界面：继续任务 vs 开始新任务\n          await showSessionChoiceDialog(taskInfo);\n        } else {\n          // 没有活跃任务，开始新会话\n          console.log('没有检测到活跃任务，开始新会话');\n          await initializeNewSession();\n        }\n      } catch (error) {\n        console.error('会话检测失败:', error);\n        await initializeNewSession();\n      }\n    };\n\n    // 初始化新会话\n    const initializeNewSession = async () => {\n      try {\n        console.log('初始化新会话');\n\n        // 清理之前的会话数据\n        await clearPreviousSession();\n\n        // 重置页面状态\n        currentStep.value = 0;\n        currentTaskId.value = null;\n        currentTask.value = {};\n        reportData.value = null;\n\n        // 停止任何正在进行的轮询\n        stopTaskStatusPolling();\n\n        // 清理URL参数中的taskId\n        if (route.query.taskId) {\n          router.replace({\n            path: route.path,\n            query: {\n              ...route.query,\n              taskId: undefined\n            }\n          });\n        }\n        ElMessage.success('已开始新的分析会话');\n      } catch (error) {\n        console.error('初始化新会话失败:', error);\n        ElMessage.error('初始化新会话失败: ' + error.message);\n      }\n    };\n\n    // 清理之前的会话\n    const clearPreviousSession = async () => {\n      try {\n        console.log('清理之前的会话数据');\n\n        // 清理会话存储\n        sessionStorage.removeItem('fourWayActiveTask');\n\n        // 清理其他相关的存储数据\n        const keysToRemove = ['uploadState', 'fourWayProgress', 'fourWayResults'];\n        keysToRemove.forEach(key => {\n          try {\n            sessionStorage.removeItem(key);\n          } catch (e) {\n            console.warn(`清理存储键 ${key} 失败:`, e);\n          }\n        });\n      } catch (error) {\n        console.error('清理会话数据失败:', error);\n      }\n    };\n\n    // 显示会话选择对话框\n    const showSessionChoiceDialog = async taskInfo => {\n      try {\n        const result = await ElMessageBox.confirm(`检测到您有一个未完成的四方向分析任务（任务ID: ${taskInfo.taskId}）。您希望：`, '会话选择', {\n          confirmButtonText: '继续之前的任务',\n          cancelButtonText: '开始新的分析',\n          type: 'question',\n          distinguishCancelAndClose: true,\n          closeOnClickModal: false,\n          closeOnPressEscape: false,\n          showClose: false,\n          customClass: 'session-choice-dialog'\n        });\n\n        // 用户选择继续之前的任务\n        if (result === 'confirm') {\n          console.log('用户选择继续之前的任务');\n          currentTaskId.value = taskInfo.taskId;\n          await fetchTaskStatusAndSetStep(taskInfo.taskId);\n          ElMessage.info('已恢复之前的分析任务');\n        }\n      } catch (action) {\n        // 用户选择开始新分析或关闭对话框\n        if (action === 'cancel') {\n          console.log('用户选择开始新的分析');\n          await initializeNewSession();\n        } else {\n          // 用户关闭对话框，默认开始新会话\n          console.log('用户关闭对话框，开始新会话');\n          await initializeNewSession();\n        }\n      }\n    };\n    const checkActiveTask = async () => {\n      // 这个方法现在被 detectSessionMode 替代，保留用于向后兼容\n      await detectSessionMode();\n    };\n    const startTaskStatusPolling = taskId => {\n      // 清除现有的轮询\n      if (taskStatusPolling.value) {\n        clearInterval(taskStatusPolling.value);\n      }\n      console.log('开始轮询任务状态:', taskId);\n      taskStatusPolling.value = setInterval(async () => {\n        try {\n          await fetchTaskStatusAndSetStep(taskId);\n\n          // 如果任务完成或失败，停止轮询\n          if (['completed', 'failed'].includes(currentTask.value.status)) {\n            clearInterval(taskStatusPolling.value);\n            taskStatusPolling.value = null;\n          }\n        } catch (error) {\n          console.error('轮询任务状态失败:', error);\n        }\n      }, 3000); // 每3秒轮询一次\n    };\n    const stopTaskStatusPolling = () => {\n      if (taskStatusPolling.value) {\n        clearInterval(taskStatusPolling.value);\n        taskStatusPolling.value = null;\n      }\n    };\n\n    // 增强的会话存储管理\n    const saveTaskToSession = (taskId, additionalData = {}) => {\n      try {\n        const sessionData = {\n          taskId: taskId,\n          timestamp: Date.now(),\n          sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n          mode: 'active',\n          ...additionalData\n        };\n        sessionStorage.setItem('fourWayActiveTask', JSON.stringify(sessionData));\n        console.log('任务已保存到会话存储:', sessionData);\n      } catch (error) {\n        console.warn('保存任务信息到会话存储失败:', error);\n      }\n    };\n    const getTaskFromSession = () => {\n      try {\n        const sessionData = sessionStorage.getItem('fourWayActiveTask');\n        if (sessionData) {\n          const taskInfo = JSON.parse(sessionData);\n\n          // 检查会话有效性\n          const sessionAge = Date.now() - taskInfo.timestamp;\n          const maxSessionAge = 24 * 60 * 60 * 1000; // 24小时\n\n          if (sessionAge <= maxSessionAge) {\n            return taskInfo;\n          } else {\n            console.log('会话数据已过期，清理存储');\n            sessionStorage.removeItem('fourWayActiveTask');\n          }\n        }\n      } catch (error) {\n        console.warn('从会话存储获取任务信息失败:', error);\n      }\n      return null;\n    };\n\n    // 事件处理\n    const handleUploadSuccess = response => {\n      const taskId = response.data?.taskId || response.taskId || `task_${Date.now()}`;\n      currentTaskId.value = taskId;\n\n      // 使用增强的会话存储管理\n      saveTaskToSession(taskId, {\n        uploadTime: new Date().toISOString(),\n        status: 'processing'\n      });\n\n      // 更新当前任务信息\n      currentTask.value = {\n        id: taskId,\n        name: '四方向交通分析任务',\n        status: 'processing',\n        progress: 10,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n\n      // 更新URL参数但不导航\n      router.replace({\n        path: route.path,\n        query: {\n          ...route.query,\n          taskId: taskId\n        }\n      });\n      currentStep.value = 1;\n      ElMessage.success('视频上传成功，开始实时检测');\n\n      // 开始轮询任务状态\n      startTaskStatusPolling(taskId);\n    };\n    const handleUploadError = error => {\n      ElMessage.error('视频上传失败: ' + error.message);\n    };\n    const handleUploadProgress = progress => {\n      console.log('上传进度:', progress);\n    };\n    const handleUploadStatusChange = status => {\n      console.log('上传状态变化:', status);\n    };\n    const handleDetectionUpdate = data => {\n      console.log('检测更新:', data);\n\n      // 更新任务进度\n      if (currentTask.value && data.progress) {\n        currentTask.value.progress = Math.min(data.progress, 90); // 检测阶段最多90%\n        currentTask.value.updatedAt = new Date();\n      }\n    };\n    const handleDetectionStatusChange = status => {\n      if (status === 'completed') {\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed';\n          currentTask.value.progress = 100;\n          currentTask.value.updatedAt = new Date();\n        }\n        currentStep.value = 2;\n        ElMessage.success('实时检测完成，开始智能分析');\n      }\n    };\n\n    // 处理四方向分析完成事件\n    const handleAnalysisComplete = async completeData => {\n      try {\n        console.log('🎉 收到四方向分析完成事件:', completeData);\n\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed';\n          currentTask.value.progress = 100;\n          currentTask.value.updatedAt = new Date();\n        }\n\n        // 显示完成提示\n        ElMessage({\n          message: `四方向分析完成！检测到 ${completeData.summary?.totalVehicles || 0} 辆车辆，正在生成智能报告...`,\n          type: 'success',\n          duration: 4000\n        });\n\n        // 自动生成智能报告数据\n        try {\n          await generateReportFromAnalysisData(completeData);\n\n          // 延迟跳转到智能分析模块\n          setTimeout(() => {\n            currentStep.value = 2;\n            ElMessage.info('已自动跳转到智能分析模块');\n          }, 1500);\n        } catch (error) {\n          console.error('自动生成报告失败:', error);\n          // 即使报告生成失败，也跳转到智能分析模块\n          setTimeout(() => {\n            currentStep.value = 2;\n            ElMessage.warning('已跳转到智能分析模块，请手动生成报告');\n          }, 2000);\n        }\n      } catch (error) {\n        console.error('处理分析完成事件失败:', error);\n        ElMessage.error('处理完成事件失败，请手动跳转到智能分析');\n      }\n    };\n    const handleAnalysisDataUpdate = data => {\n      reportData.value = data;\n      currentStep.value = 3;\n      ElMessage.success('智能分析完成，可以生成报告');\n    };\n\n    // 从分析数据生成报告数据\n    const generateReportFromAnalysisData = async completeData => {\n      try {\n        if (!currentTaskId.value) {\n          throw new Error('缺少任务ID');\n        }\n        console.log('正在从分析数据生成报告...', completeData);\n\n        // 首先尝试从API获取完整的分析结果\n        let apiAnalysisData = null;\n        try {\n          const response = await getFourWayAnalysisResult(currentTaskId.value);\n          apiAnalysisData = response.data;\n          console.log('从API获取到分析数据:', apiAnalysisData);\n        } catch (apiError) {\n          console.warn('从API获取分析数据失败，使用WebSocket数据:', apiError);\n        }\n\n        // 合并WebSocket数据和API数据\n        const mergedData = {\n          ...completeData,\n          ...(apiAnalysisData || {})\n        };\n\n        // 计算智能分析指标\n        const totalVehicles = mergedData.summary?.totalVehicles || 0;\n        const processingDuration = mergedData.summary?.processingDuration || 0;\n        const directions = mergedData.directions || {};\n\n        // 计算流量平衡度\n        const directionCounts = Object.values(directions).map(d => d.vehicleCount || 0);\n        const maxCount = Math.max(...directionCounts, 1);\n        const minCount = Math.min(...directionCounts, 0);\n        const flowBalance = maxCount > 0 ? Math.round((1 - (maxCount - minCount) / maxCount) * 100) : 75;\n\n        // 计算拥堵等级\n        const avgVehicles = totalVehicles / 4;\n        let congestionLevel = '畅通';\n        if (avgVehicles > 30) congestionLevel = '重度拥堵';else if (avgVehicles > 20) congestionLevel = '中度拥堵';else if (avgVehicles > 10) congestionLevel = '轻度拥堵';\n\n        // 构建报告数据结构\n        const reportData = {\n          taskId: currentTaskId.value,\n          generatedAt: new Date(),\n          analysisType: '四方向智能分析',\n          summary: {\n            totalVehicles: totalVehicles,\n            vehicleIncrease: 0,\n            // 可以后续计算\n            processingDuration: processingDuration,\n            efficiency: Math.round(totalVehicles / Math.max(1, processingDuration / 60) * 100) / 100,\n            peakDirection: mergedData.summary?.peakDirection || '未知',\n            peakPercentage: maxCount > 0 ? Math.round(maxCount / totalVehicles * 100) : 0,\n            congestionLevel: congestionLevel,\n            congestionTrend: '稳定'\n          },\n          directions: directions,\n          intelligentAnalysis: {\n            flowBalance: flowBalance,\n            peakHours: '08:00-09:00, 17:00-18:00',\n            flowTrend: totalVehicles > 50 ? '增长趋势' : '稳定',\n            congestionPrediction: congestionLevel === '畅通' ? '低风险' : '中等风险',\n            signalOptimization: mergedData.trafficAnalysis?.signalOptimization || {\n              recommendedCycle: 120,\n              greenTimeAllocation: {\n                east: 30,\n                south: 25,\n                west: 35,\n                north: 30\n              },\n              expectedImprovement: '通行效率提升15%'\n            }\n          },\n          recommendations: (mergedData.trafficAnalysis?.recommendations || ['建议在高峰时段优化信号配时', '考虑增加车流量较大方向的绿灯时间', '建议定期监控交通流量变化']).map((rec, index) => {\n            if (typeof rec === 'string') {\n              return {\n                title: `优化建议 ${index + 1}`,\n                description: rec,\n                type: 'signal',\n                priority: index === 0 ? 'high' : index === 1 ? 'medium' : 'low',\n                expectedImprovement: '预计提升通行效率10-15%'\n              };\n            }\n            return rec;\n          }),\n          technicalMetrics: {\n            accuracy: 95.5,\n            processingSpeed: 25.0,\n            stability: 98.2,\n            dataIntegrity: 99.1,\n            responseTime: 150,\n            memoryUsage: 65.3,\n            cpuUsage: 45.8\n          }\n        };\n\n        // 设置报告数据\n        handleAnalysisDataUpdate(reportData);\n        console.log('报告数据生成成功:', reportData);\n        return reportData;\n      } catch (error) {\n        console.error('生成报告数据失败:', error);\n        throw error;\n      }\n    };\n    const handleExportReport = taskId => {\n      ElMessage.success('报告导出成功');\n    };\n    const handleRefreshReportData = taskId => {\n      ElMessage.success('报告数据刷新成功');\n    };\n\n    // 快速操作\n    const goToUpload = () => {\n      currentStep.value = 0;\n    };\n    const startDetection = () => {\n      if (canDetect.value) {\n        currentStep.value = 1;\n      } else {\n        ElMessage.warning('请先上传视频文件');\n      }\n    };\n    const generateAnalysis = () => {\n      if (canAnalyze.value) {\n        currentStep.value = 2;\n      } else {\n        ElMessage.warning('请先完成视频检测');\n      }\n    };\n    const exportReport = () => {\n      if (canExport.value) {\n        currentStep.value = 3;\n      } else {\n        ElMessage.warning('请先完成智能分析');\n      }\n    };\n    const refreshSystem = () => {\n      lastUpdateTime.value = new Date().toLocaleTimeString();\n      ElMessage.success('系统状态已刷新');\n    };\n    const showSystemInfo = () => {\n      ElMessageBox.alert('四方向智能交通分析系统 v1.0.0', '系统信息', {\n        confirmButtonText: '确定'\n      });\n    };\n\n    // StepNavigator事件处理\n    const handleStepChange = stepIndex => {\n      console.log('步骤切换请求:', stepIndex);\n\n      // 检查是否可以切换到目标步骤\n      if (stepIndex <= currentStep.value) {\n        currentStep.value = stepIndex;\n        ElMessage.info(`已切换到步骤 ${stepIndex + 1}`);\n      } else {\n        ElMessage.warning('请先完成前面的步骤');\n      }\n    };\n    const handleQuickAction = actionKey => {\n      console.log('快速操作:', actionKey);\n      switch (actionKey) {\n        case 'upload':\n          goToUpload();\n          break;\n        case 'detection':\n          startDetection();\n          break;\n        case 'analysis':\n          generateAnalysis();\n          break;\n        case 'report':\n          exportReport();\n          break;\n        default:\n          console.warn('未知的快速操作:', actionKey);\n      }\n    };\n\n    // 页面刷新优化\n    const handlePageRefresh = async () => {\n      try {\n        console.log('处理页面刷新事件');\n\n        // 检查是否是强制刷新（Ctrl+F5 或 Cmd+Shift+R）\n        const isHardRefresh = performance.navigation?.type === 1;\n        if (isHardRefresh) {\n          console.log('检测到强制刷新，开始新会话');\n          await initializeNewSession();\n          return;\n        }\n\n        // 普通刷新，检查会话状态\n        const sessionTask = getTaskFromSession();\n        if (sessionTask) {\n          console.log('页面刷新 - 恢复会话:', sessionTask);\n          currentTaskId.value = sessionTask.taskId;\n          await fetchTaskStatusAndSetStep(sessionTask.taskId);\n        } else {\n          console.log('页面刷新 - 没有有效会话，开始新会话');\n          await initializeNewSession();\n        }\n      } catch (error) {\n        console.error('处理页面刷新失败:', error);\n        await initializeNewSession();\n      }\n    };\n    const handleRefreshStatus = async () => {\n      if (currentTaskId.value) {\n        ElMessage.info('正在刷新任务状态...');\n        await fetchTaskStatusAndSetStep(currentTaskId.value);\n      } else {\n        ElMessage.warning('没有活跃的任务');\n      }\n    };\n\n    // 页面状态一致性检查\n    const checkPageStateConsistency = () => {\n      try {\n        const urlTaskId = route.query.taskId;\n        const sessionTask = getTaskFromSession();\n        const currentTaskIdValue = currentTaskId.value;\n        console.log('页面状态一致性检查:', {\n          urlTaskId,\n          sessionTaskId: sessionTask?.taskId,\n          currentTaskIdValue\n        });\n\n        // 如果URL和会话存储不一致，以URL为准\n        if (urlTaskId && sessionTask && urlTaskId !== sessionTask.taskId) {\n          console.log('检测到状态不一致，以URL参数为准');\n          saveTaskToSession(urlTaskId);\n          currentTaskId.value = urlTaskId;\n        }\n      } catch (error) {\n        console.error('页面状态一致性检查失败:', error);\n      }\n    };\n\n    // 任务状态辅助方法\n    const getTaskStatusType = status => {\n      const statusMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'failed': 'danger',\n        'paused': 'info'\n      };\n      return statusMap[status] || 'info';\n    };\n    const getTaskStatusText = status => {\n      const statusMap = {\n        'waiting': '等待中',\n        'processing': '处理中',\n        'completed': '已完成',\n        'failed': '失败',\n        'paused': '已暂停'\n      };\n      return statusMap[status] || '未知';\n    };\n    const getProgressStatus = status => {\n      if (status === 'completed') return 'success';\n      if (status === 'failed') return 'exception';\n      return null;\n    };\n    const formatTime = time => {\n      if (!time) return '-';\n      return new Date(time).toLocaleString();\n    };\n    const getProcessingDuration = task => {\n      if (!task || !task.createdAt) return '-';\n      const start = new Date(task.createdAt);\n      const end = task.updatedAt ? new Date(task.updatedAt) : new Date();\n      const duration = Math.floor((end - start) / 1000);\n      if (duration < 60) return `${duration}秒`;\n      if (duration < 3600) return `${Math.floor(duration / 60)}分钟`;\n      return `${Math.floor(duration / 3600)}小时`;\n    };\n\n    // 监听路由变化 - 增强版\n    watch(() => route.query, (newQuery, oldQuery) => {\n      const newTaskId = newQuery.taskId;\n      const newMode = newQuery.mode;\n      const oldTaskId = oldQuery?.taskId;\n      const oldMode = oldQuery?.mode;\n      console.log('路由参数变化:', {\n        newTaskId,\n        newMode,\n        oldTaskId,\n        oldMode\n      });\n\n      // 检测模式变化\n      if (newMode !== oldMode) {\n        if (newMode === 'new') {\n          console.log('检测到新会话模式参数');\n          initializeNewSession();\n          return;\n        }\n      }\n\n      // 检测任务ID变化\n      if (newTaskId && newTaskId !== currentTaskId.value) {\n        console.log('检测到URL中的taskId变化:', newTaskId);\n        currentTaskId.value = newTaskId;\n        fetchTaskStatusAndSetStep(newTaskId);\n      }\n    }, {\n      deep: true\n    });\n\n    // 状态隔离机制\n    const createSessionNamespace = taskId => {\n      return `fourWay_${taskId}_${Date.now()}`;\n    };\n    const getNamespacedStorageKey = (key, taskId = null) => {\n      const baseKey = taskId ? `${key}_${taskId}` : key;\n      return `fourWayConsole_${baseKey}`;\n    };\n    const setNamespacedStorage = (key, value, taskId = null) => {\n      try {\n        const namespacedKey = getNamespacedStorageKey(key, taskId);\n        sessionStorage.setItem(namespacedKey, JSON.stringify(value));\n      } catch (error) {\n        console.warn('设置命名空间存储失败:', error);\n      }\n    };\n    const getNamespacedStorage = (key, taskId = null) => {\n      try {\n        const namespacedKey = getNamespacedStorageKey(key, taskId);\n        const value = sessionStorage.getItem(namespacedKey);\n        return value ? JSON.parse(value) : null;\n      } catch (error) {\n        console.warn('获取命名空间存储失败:', error);\n        return null;\n      }\n    };\n    const clearNamespacedStorage = (taskId = null) => {\n      try {\n        const prefix = taskId ? `fourWayConsole_${taskId}` : 'fourWayConsole_';\n        const keysToRemove = [];\n        for (let i = 0; i < sessionStorage.length; i++) {\n          const key = sessionStorage.key(i);\n          if (key && key.startsWith(prefix)) {\n            keysToRemove.push(key);\n          }\n        }\n        keysToRemove.forEach(key => sessionStorage.removeItem(key));\n        console.log('已清理命名空间存储:', keysToRemove.length, '个项目');\n      } catch (error) {\n        console.warn('清理命名空间存储失败:', error);\n      }\n    };\n\n    // 生命周期\n    onMounted(async () => {\n      // 初始化系统状态\n      lastUpdateTime.value = new Date().toLocaleTimeString();\n      activeConnections.value = 1;\n\n      // 页面状态一致性检查\n      checkPageStateConsistency();\n\n      // 初始化页面状态\n      await initializeFromRoute();\n    });\n    onUnmounted(() => {\n      // 清理轮询\n      stopTaskStatusPolling();\n    });\n    return {\n      // 图标组件\n      Upload,\n      VideoCamera,\n      DataAnalysis,\n      Document,\n      Plus,\n      MoreFilled,\n      Refresh,\n      InfoFilled,\n      // 响应式数据\n      currentStep,\n      currentTaskId,\n      currentTask,\n      reportData,\n      activeConnections,\n      lastUpdateTime,\n      systemStatus,\n      isInitializing,\n      // 计算属性\n      totalTasks,\n      activeTasks,\n      completedTasks,\n      canUpload,\n      canDetect,\n      canAnalyze,\n      canExport,\n      // 用户反馈和提示\n      showUserFeedback: (message, type = 'info', duration = 3000) => {\n        ElMessage({\n          message,\n          type,\n          duration,\n          showClose: true\n        });\n      },\n      showConfirmDialog: async (message, title = '确认操作') => {\n        try {\n          await ElMessageBox.confirm(message, title, {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          });\n          return true;\n        } catch {\n          return false;\n        }\n      },\n      // 方法\n      initializeFromRoute,\n      fetchTaskStatusAndSetStep,\n      setStepByTaskStatus,\n      checkActiveTask,\n      detectSessionMode,\n      initializeNewSession,\n      clearPreviousSession,\n      showSessionChoiceDialog,\n      saveTaskToSession,\n      getTaskFromSession,\n      handlePageRefresh,\n      checkPageStateConsistency,\n      createSessionNamespace,\n      getNamespacedStorageKey,\n      setNamespacedStorage,\n      getNamespacedStorage,\n      clearNamespacedStorage,\n      startTaskStatusPolling,\n      stopTaskStatusPolling,\n      handleUploadSuccess,\n      handleUploadError,\n      handleUploadProgress,\n      handleUploadStatusChange,\n      handleDetectionUpdate,\n      handleDetectionStatusChange,\n      handleAnalysisComplete,\n      handleAnalysisDataUpdate,\n      handleExportReport,\n      handleRefreshReportData,\n      goToUpload,\n      startDetection,\n      generateAnalysis,\n      exportReport,\n      refreshSystem,\n      showSystemInfo,\n      handleStepChange,\n      handleQuickAction,\n      handleRefreshStatus,\n      // 任务状态辅助方法\n      getTaskStatusType,\n      getTaskStatusText,\n      getProgressStatus,\n      formatTime,\n      getProcessingDuration\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "onUnmounted", "watch", "useRouter", "useRoute", "ElMessage", "ElMessageBox", "Grid", "Upload", "VideoCamera", "DataAnalysis", "Document", "Plus", "MoreFilled", "Refresh", "InfoFilled", "Loading", "FourWayVideoUpload", "FourWayRealtimeViewer", "TrafficAnalysisDashboard", "IntelligentTrafficReport", "StepNavigator", "getFourWayTaskStatus", "getFourWayAnalysisResult", "generateFourWayTrafficReport", "name", "components", "setup", "router", "route", "currentStep", "currentTaskId", "currentTask", "id", "status", "progress", "createdAt", "Date", "updatedAt", "reportData", "activeConnections", "lastUpdateTime", "isInitializing", "taskStatusPolling", "systemStatus", "type", "text", "totalTasks", "activeTasks", "value", "completedTasks", "canUpload", "canDetect", "canAnalyze", "canExport", "initializeFromRoute", "sessionMode", "query", "mode", "urlTaskId", "taskId", "params", "console", "log", "initializeNewSession", "fetchTaskStatusAndSetStep", "detectSessionMode", "error", "message", "token", "localStorage", "getItem", "Error", "response", "fetch", "headers", "ok", "taskData", "json", "setStepByTaskStatus", "includes", "startTaskStatusPolling", "warning", "info", "success", "sessionData", "sessionStorage", "taskInfo", "JSON", "parse", "sessionAge", "now", "timestamp", "maxSessionAge", "showSessionChoiceDialog", "clearPreviousSession", "stopTaskStatusPolling", "replace", "path", "undefined", "removeItem", "keysToRemove", "for<PERSON>ach", "key", "e", "warn", "result", "confirm", "confirmButtonText", "cancelButtonText", "distinguishCancelAndClose", "closeOnClickModal", "closeOnPressEscape", "showClose", "customClass", "action", "checkActiveTask", "clearInterval", "setInterval", "saveTaskToSession", "additionalData", "sessionId", "Math", "random", "toString", "substr", "setItem", "stringify", "getTaskFromSession", "handleUploadSuccess", "data", "uploadTime", "toISOString", "handleUploadError", "handleUploadProgress", "handleUploadStatusChange", "handleDetectionUpdate", "min", "handleDetectionStatusChange", "handleAnalysisComplete", "completeData", "summary", "totalVehicles", "duration", "generateReportFromAnalysisData", "setTimeout", "handleAnalysisDataUpdate", "apiAnalysisData", "apiError", "mergedData", "processingDuration", "directions", "directionCounts", "Object", "values", "map", "d", "vehicleCount", "maxCount", "max", "minCount", "flowBalance", "round", "avgVehicles", "congestionLevel", "generatedAt", "analysisType", "vehicleIncrease", "efficiency", "peakDirection", "peakPercentage", "congestionTrend", "intelligentAnalysis", "peakHours", "flowTrend", "congestionPrediction", "signalOptimization", "trafficAnalysis", "recommendedCycle", "greenTimeAllocation", "east", "south", "west", "north", "expectedImprovement", "recommendations", "rec", "index", "title", "description", "priority", "technicalMetrics", "accuracy", "processingSpeed", "stability", "dataIntegrity", "responseTime", "memoryUsage", "cpuUsage", "handleExportReport", "handleRefreshReportData", "goToUpload", "startDetection", "generateAnalysis", "exportReport", "refreshSystem", "toLocaleTimeString", "showSystemInfo", "alert", "handleStepChange", "stepIndex", "handleQuickAction", "action<PERSON>ey", "handlePageRefresh", "isHardRefresh", "performance", "navigation", "sessionTask", "handleRefreshStatus", "checkPageStateConsistency", "currentTaskIdValue", "sessionTaskId", "getTaskStatusType", "statusMap", "getTaskStatusText", "getProgressStatus", "formatTime", "time", "toLocaleString", "getProcessingDuration", "task", "start", "end", "floor", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "newTaskId", "newMode", "oldTaskId", "oldMode", "deep", "createSessionNamespace", "getNamespacedStorageKey", "baseKey", "setNamespacedStorage", "namespacedKey", "getNamespacedStorage", "clearNamespacedStorage", "prefix", "i", "length", "startsWith", "push", "showUserFeedback", "showConfirmDialog"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\FourWayAnalysisConsole.vue"], "sourcesContent": ["<template>\n  <div class=\"four-way-analysis-console\">\n    <!-- 页面头部 -->\n    <div class=\"console-header\">\n      <div class=\"header-content\">\n        <h1 class=\"console-title\">\n          <el-icon><Grid /></el-icon>\n          四方向智能交通分析控制台\n        </h1>\n        <p class=\"console-description\">\n          集成视频上传、实时检测、智能分析和报告生成的一站式交通分析平台\n        </p>\n      </div>\n      \n      <div class=\"header-stats\">\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ totalTasks }}</div>\n          <div class=\"stat-label\">总任务数</div>\n        </div>\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ activeTasks }}</div>\n          <div class=\"stat-label\">活跃任务</div>\n        </div>\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ completedTasks }}</div>\n          <div class=\"stat-label\">已完成</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 工作流程导航 -->\n    <div class=\"workflow-navigation\">\n      <el-steps :active=\"currentStep\" align-center>\n        <el-step \n          title=\"视频上传\" \n          description=\"上传四方向视频文件\"\n          icon=\"Upload\"\n        />\n        <el-step \n          title=\"实时检测\" \n          description=\"AI模型实时分析\"\n          icon=\"VideoCamera\"\n        />\n        <el-step \n          title=\"智能分析\" \n          description=\"生成分析结果\"\n          icon=\"DataAnalysis\"\n        />\n        <el-step \n          title=\"报告生成\" \n          description=\"导出分析报告\"\n          icon=\"Document\"\n        />\n      </el-steps>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"console-content\">\n      <!-- 左侧面板 -->\n      <div class=\"left-panel\">\n        <!-- 步骤导航器 -->\n        <StepNavigator\n          :current-step=\"currentStep\"\n          :task-status=\"currentTask.status\"\n          :task-progress=\"currentTask.progress\"\n          :loading=\"isInitializing\"\n          :allow-navigation=\"true\"\n          @step-change=\"handleStepChange\"\n          @action=\"handleQuickAction\"\n          @refresh=\"handleRefreshStatus\"\n        />\n      </div>\n\n      <!-- 右侧主内容 -->\n      <div class=\"main-content\">\n        <!-- 当前任务信息 -->\n        <div v-if=\"currentTask\" class=\"current-task-info\">\n          <el-card>\n            <template #header>\n              <div class=\"task-header\">\n                <div class=\"task-title-section\">\n                  <h3>{{ currentTask.name }}</h3>\n                  <el-tag :type=\"getTaskStatusType(currentTask.status)\">\n                    {{ getTaskStatusText(currentTask.status) }}\n                  </el-tag>\n                </div>\n                <div class=\"task-progress-section\">\n                  <el-progress \n                    :percentage=\"currentTask.progress\" \n                    :status=\"getProgressStatus(currentTask.status)\"\n                    :stroke-width=\"8\"\n                  />\n                  <span class=\"progress-text\">{{ currentTask.progress }}%</span>\n                </div>\n              </div>\n            </template>\n            \n            <div class=\"task-details\">\n              <el-row :gutter=\"20\">\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">任务ID:</span>\n                    <span class=\"detail-value\">{{ currentTask.id }}</span>\n                  </div>\n                </el-col>\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">创建时间:</span>\n                    <span class=\"detail-value\">{{ formatTime(currentTask.createdAt) }}</span>\n                  </div>\n                </el-col>\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">处理时长:</span>\n                    <span class=\"detail-value\">{{ getProcessingDuration(currentTask) }}</span>\n                  </div>\n                </el-col>\n              </el-row>\n            </div>\n          </el-card>\n        </div>\n\n        <!-- 动态内容区域 -->\n        <div class=\"dynamic-content\">\n          <!-- 初始化加载状态 -->\n          <div v-if=\"isInitializing\" class=\"loading-container\">\n            <el-skeleton :rows=\"8\" animated>\n              <template #template>\n                <div class=\"loading-content\">\n                  <el-skeleton-item variant=\"h1\" style=\"width: 40%; margin-bottom: 20px;\" />\n                  <el-skeleton-item variant=\"text\" style=\"width: 80%; margin-bottom: 10px;\" />\n                  <el-skeleton-item variant=\"text\" style=\"width: 60%; margin-bottom: 20px;\" />\n                  <el-skeleton-item variant=\"rect\" style=\"width: 100%; height: 200px;\" />\n                </div>\n              </template>\n            </el-skeleton>\n            <div class=\"loading-text\">\n              <el-icon class=\"loading-icon\"><Loading /></el-icon>\n              正在初始化页面状态...\n            </div>\n          </div>\n\n          <!-- 步骤1: 视频上传 -->\n          <div v-else-if=\"currentStep === 0\" class=\"step-content\">\n            <FourWayVideoUpload\n              @upload-success=\"handleUploadSuccess\"\n              @upload-error=\"handleUploadError\"\n              @upload-progress=\"handleUploadProgress\"\n              @status-change=\"handleUploadStatusChange\"\n            />\n          </div>\n\n          <!-- 步骤2: 实时检测 -->\n          <div v-if=\"currentStep === 1\" class=\"step-content\">\n            <FourWayRealtimeViewer\n              v-if=\"currentTaskId\"\n              :task-id=\"currentTaskId\"\n              :auto-start=\"true\"\n              @detection-update=\"handleDetectionUpdate\"\n              @status-change=\"handleDetectionStatusChange\"\n              @analysis-complete=\"handleAnalysisComplete\"\n            />\n            <el-empty v-else description=\"请先上传视频文件\">\n              <el-button type=\"primary\" @click=\"currentStep = 0\">\n                返回上传\n              </el-button>\n            </el-empty>\n          </div>\n\n          <!-- 步骤3: 智能分析 -->\n          <div v-if=\"currentStep === 2\" class=\"step-content\">\n            <TrafficAnalysisDashboard\n              v-if=\"currentTaskId\"\n              :task-id=\"currentTaskId\"\n              @data-updated=\"handleAnalysisDataUpdate\"\n            />\n            <el-empty v-else description=\"请先完成视频检测\">\n              <el-button type=\"primary\" @click=\"currentStep = 1\">\n                返回检测\n              </el-button>\n            </el-empty>\n          </div>\n\n          <!-- 步骤4: 报告生成 -->\n          <div v-if=\"currentStep === 3\" class=\"step-content\">\n            <IntelligentTrafficReport\n              v-if=\"currentTaskId && reportData\"\n              :task-id=\"currentTaskId\"\n              :report-data=\"reportData\"\n              @export-report=\"handleExportReport\"\n              @refresh-data=\"handleRefreshReportData\"\n            />\n            <el-empty v-else description=\"请先完成智能分析\">\n              <el-button type=\"primary\" @click=\"currentStep = 2\">\n                返回分析\n              </el-button>\n            </el-empty>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 底部状态栏 -->\n    <div class=\"console-footer\">\n      <div class=\"footer-info\">\n        <span>系统状态: </span>\n        <el-tag :type=\"systemStatus.type\" size=\"small\">{{ systemStatus.text }}</el-tag>\n        <span class=\"separator\">|</span>\n        <span>活跃连接: {{ activeConnections }}</span>\n        <span class=\"separator\">|</span>\n        <span>最后更新: {{ lastUpdateTime }}</span>\n      </div>\n      \n      <div class=\"footer-actions\">\n        <el-button size=\"small\" @click=\"refreshSystem\">\n          <el-icon><Refresh /></el-icon>\n          刷新\n        </el-button>\n        <el-button size=\"small\" @click=\"showSystemInfo\">\n          <el-icon><InfoFilled /></el-icon>\n          系统信息\n        </el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'\nimport { useRouter, useRoute } from 'vue-router'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport {\n  Grid, Upload, VideoCamera, DataAnalysis, Document, Plus,\n  MoreFilled, Refresh, InfoFilled, Loading\n} from '@element-plus/icons-vue'\n\n// 导入组件\nimport FourWayVideoUpload from '@/components/analysis/FourWayVideoUpload.vue'\nimport FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue'\nimport TrafficAnalysisDashboard from '@/components/analysis/TrafficAnalysisDashboard.vue'\nimport IntelligentTrafficReport from '@/components/analysis/IntelligentTrafficReport.vue'\nimport StepNavigator from '@/components/analysis/StepNavigator.vue'\n\n// 导入API\nimport {\n  getFourWayTaskStatus,\n  getFourWayAnalysisResult,\n  generateFourWayTrafficReport\n} from '@/api/video'\n\nexport default {\n  name: 'FourWayAnalysisConsole',\n  components: {\n    Grid, Upload, VideoCamera, DataAnalysis, Document, Plus,\n    MoreFilled, Refresh, InfoFilled, Loading,\n    FourWayVideoUpload,\n    FourWayRealtimeViewer,\n    TrafficAnalysisDashboard,\n    IntelligentTrafficReport,\n    StepNavigator\n  },\n  setup() {\n    const router = useRouter()\n    const route = useRoute()\n    \n    // 响应式数据\n    const currentStep = ref(0)\n    const currentTaskId = ref('')\n    const currentTask = ref({\n      id: '',\n      name: '四方向交通分析任务',\n      status: 'waiting',\n      progress: 0,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    })\n    const reportData = ref(null)\n    const activeConnections = ref(0)\n    const lastUpdateTime = ref('')\n    const isInitializing = ref(true)\n    const taskStatusPolling = ref(null)\n\n    // 系统状态\n    const systemStatus = reactive({\n      type: 'success',\n      text: '正常运行'\n    })\n\n    // 计算属性\n    const totalTasks = computed(() => 1)\n    const activeTasks = computed(() => currentStep.value > 0 && currentStep.value < 4 ? 1 : 0)\n    const completedTasks = computed(() => currentStep.value === 4 ? 1 : 0)\n\n    const canUpload = computed(() => true)\n    const canDetect = computed(() => currentStep.value >= 1)\n    const canAnalyze = computed(() => currentStep.value >= 2)\n    const canExport = computed(() => currentStep.value >= 3)\n    \n    // 方法\n\n    // 初始化和状态检测方法\n    const initializeFromRoute = async () => {\n      try {\n        isInitializing.value = true\n\n        // 检查URL参数中的mode参数\n        const sessionMode = route.query.mode\n        const urlTaskId = route.query.taskId || route.params.taskId\n\n        console.log('页面初始化 - 模式:', sessionMode, '任务ID:', urlTaskId)\n\n        // 根据模式参数决定初始化策略\n        if (sessionMode === 'new') {\n          console.log('检测到新会话模式，清理之前的状态')\n          await initializeNewSession()\n        } else if (urlTaskId) {\n          console.log('从URL参数检测到任务ID:', urlTaskId)\n          currentTaskId.value = urlTaskId\n          // 获取任务状态并设置对应步骤\n          await fetchTaskStatusAndSetStep(urlTaskId)\n        } else {\n          // 智能检测会话模式\n          await detectSessionMode()\n        }\n\n      } catch (error) {\n        console.error('初始化失败:', error)\n        ElMessage.error('初始化页面状态失败: ' + error.message)\n      } finally {\n        isInitializing.value = false\n      }\n    }\n\n    const fetchTaskStatusAndSetStep = async (taskId) => {\n      try {\n        const token = localStorage.getItem('auth_token')\n        if (!token) {\n          throw new Error('未找到认证令牌')\n        }\n\n        const response = await fetch(`/api/video-analysis/four-way/${taskId}/status`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`获取任务状态失败: ${response.status}`)\n        }\n\n        const taskData = await response.json()\n        console.log('获取到任务状态:', taskData)\n\n        // 更新当前任务信息\n        currentTask.value = {\n          id: taskData.taskId || taskId,\n          name: taskData.name || '四方向交通分析任务',\n          status: taskData.status || 'waiting',\n          progress: taskData.progress || 0,\n          createdAt: taskData.createdAt ? new Date(taskData.createdAt) : new Date(),\n          updatedAt: taskData.updatedAt ? new Date(taskData.updatedAt) : new Date()\n        }\n\n        // 根据任务状态设置对应步骤\n        setStepByTaskStatus(taskData.status, taskData.progress)\n\n        // 如果任务正在处理中，开始轮询状态\n        if (['queued', 'processing'].includes(taskData.status)) {\n          startTaskStatusPolling(taskId)\n        }\n\n      } catch (error) {\n        console.error('获取任务状态失败:', error)\n        ElMessage.warning('无法获取任务状态，将从上传步骤开始')\n        currentStep.value = 0\n      }\n    }\n\n    const setStepByTaskStatus = (status, progress = 0) => {\n      console.log('根据任务状态设置步骤:', { status, progress })\n\n      switch (status) {\n        case 'queued':\n        case 'uploading':\n          currentStep.value = 0 // 上传步骤\n          ElMessage.info('任务正在排队中，请等待处理')\n          break\n\n        case 'processing':\n          if (progress < 50) {\n            currentStep.value = 1 // 实时检测步骤\n            ElMessage.info('任务正在进行实时检测')\n          } else if (progress < 90) {\n            currentStep.value = 1 // 仍在检测阶段\n            ElMessage.info('实时检测进行中')\n          } else {\n            currentStep.value = 2 // 智能分析步骤\n            ElMessage.info('正在进行智能分析')\n          }\n          break\n\n        case 'completed':\n          currentStep.value = 2 // 跳转到智能分析步骤\n          ElMessage.success('任务已完成，可以查看分析结果')\n          break\n\n        case 'failed':\n          currentStep.value = 0 // 回到上传步骤\n          ElMessage.error('任务处理失败，请重新上传')\n          break\n\n        default:\n          currentStep.value = 0 // 默认从上传开始\n          break\n      }\n    }\n\n    // 智能会话检测机制\n    const detectSessionMode = async () => {\n      try {\n        // 检查会话存储中是否有活跃任务\n        const sessionData = sessionStorage.getItem('fourWayActiveTask')\n        if (sessionData) {\n          const taskInfo = JSON.parse(sessionData)\n          const sessionAge = Date.now() - taskInfo.timestamp\n          const maxSessionAge = 24 * 60 * 60 * 1000 // 24小时\n\n          console.log('检测到会话数据:', taskInfo, '会话年龄:', sessionAge / 1000 / 60, '分钟')\n\n          // 如果会话数据过期，清理并开始新会话\n          if (sessionAge > maxSessionAge) {\n            console.log('会话数据已过期，开始新会话')\n            await initializeNewSession()\n            return\n          }\n\n          // 显示用户选择界面：继续任务 vs 开始新任务\n          await showSessionChoiceDialog(taskInfo)\n        } else {\n          // 没有活跃任务，开始新会话\n          console.log('没有检测到活跃任务，开始新会话')\n          await initializeNewSession()\n        }\n\n      } catch (error) {\n        console.error('会话检测失败:', error)\n        await initializeNewSession()\n      }\n    }\n\n    // 初始化新会话\n    const initializeNewSession = async () => {\n      try {\n        console.log('初始化新会话')\n\n        // 清理之前的会话数据\n        await clearPreviousSession()\n\n        // 重置页面状态\n        currentStep.value = 0\n        currentTaskId.value = null\n        currentTask.value = {}\n        reportData.value = null\n\n        // 停止任何正在进行的轮询\n        stopTaskStatusPolling()\n\n        // 清理URL参数中的taskId\n        if (route.query.taskId) {\n          router.replace({\n            path: route.path,\n            query: { ...route.query, taskId: undefined }\n          })\n        }\n\n        ElMessage.success('已开始新的分析会话')\n\n      } catch (error) {\n        console.error('初始化新会话失败:', error)\n        ElMessage.error('初始化新会话失败: ' + error.message)\n      }\n    }\n\n    // 清理之前的会话\n    const clearPreviousSession = async () => {\n      try {\n        console.log('清理之前的会话数据')\n\n        // 清理会话存储\n        sessionStorage.removeItem('fourWayActiveTask')\n\n        // 清理其他相关的存储数据\n        const keysToRemove = ['uploadState', 'fourWayProgress', 'fourWayResults']\n        keysToRemove.forEach(key => {\n          try {\n            sessionStorage.removeItem(key)\n          } catch (e) {\n            console.warn(`清理存储键 ${key} 失败:`, e)\n          }\n        })\n\n      } catch (error) {\n        console.error('清理会话数据失败:', error)\n      }\n    }\n\n    // 显示会话选择对话框\n    const showSessionChoiceDialog = async (taskInfo) => {\n      try {\n        const result = await ElMessageBox.confirm(\n          `检测到您有一个未完成的四方向分析任务（任务ID: ${taskInfo.taskId}）。您希望：`,\n          '会话选择',\n          {\n            confirmButtonText: '继续之前的任务',\n            cancelButtonText: '开始新的分析',\n            type: 'question',\n            distinguishCancelAndClose: true,\n            closeOnClickModal: false,\n            closeOnPressEscape: false,\n            showClose: false,\n            customClass: 'session-choice-dialog'\n          }\n        )\n\n        // 用户选择继续之前的任务\n        if (result === 'confirm') {\n          console.log('用户选择继续之前的任务')\n          currentTaskId.value = taskInfo.taskId\n          await fetchTaskStatusAndSetStep(taskInfo.taskId)\n          ElMessage.info('已恢复之前的分析任务')\n        }\n\n      } catch (action) {\n        // 用户选择开始新分析或关闭对话框\n        if (action === 'cancel') {\n          console.log('用户选择开始新的分析')\n          await initializeNewSession()\n        } else {\n          // 用户关闭对话框，默认开始新会话\n          console.log('用户关闭对话框，开始新会话')\n          await initializeNewSession()\n        }\n      }\n    }\n\n    const checkActiveTask = async () => {\n      // 这个方法现在被 detectSessionMode 替代，保留用于向后兼容\n      await detectSessionMode()\n    }\n\n    const startTaskStatusPolling = (taskId) => {\n      // 清除现有的轮询\n      if (taskStatusPolling.value) {\n        clearInterval(taskStatusPolling.value)\n      }\n\n      console.log('开始轮询任务状态:', taskId)\n\n      taskStatusPolling.value = setInterval(async () => {\n        try {\n          await fetchTaskStatusAndSetStep(taskId)\n\n          // 如果任务完成或失败，停止轮询\n          if (['completed', 'failed'].includes(currentTask.value.status)) {\n            clearInterval(taskStatusPolling.value)\n            taskStatusPolling.value = null\n          }\n\n        } catch (error) {\n          console.error('轮询任务状态失败:', error)\n        }\n      }, 3000) // 每3秒轮询一次\n    }\n\n    const stopTaskStatusPolling = () => {\n      if (taskStatusPolling.value) {\n        clearInterval(taskStatusPolling.value)\n        taskStatusPolling.value = null\n      }\n    }\n\n    // 增强的会话存储管理\n    const saveTaskToSession = (taskId, additionalData = {}) => {\n      try {\n        const sessionData = {\n          taskId: taskId,\n          timestamp: Date.now(),\n          sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n          mode: 'active',\n          ...additionalData\n        }\n\n        sessionStorage.setItem('fourWayActiveTask', JSON.stringify(sessionData))\n        console.log('任务已保存到会话存储:', sessionData)\n\n      } catch (error) {\n        console.warn('保存任务信息到会话存储失败:', error)\n      }\n    }\n\n    const getTaskFromSession = () => {\n      try {\n        const sessionData = sessionStorage.getItem('fourWayActiveTask')\n        if (sessionData) {\n          const taskInfo = JSON.parse(sessionData)\n\n          // 检查会话有效性\n          const sessionAge = Date.now() - taskInfo.timestamp\n          const maxSessionAge = 24 * 60 * 60 * 1000 // 24小时\n\n          if (sessionAge <= maxSessionAge) {\n            return taskInfo\n          } else {\n            console.log('会话数据已过期，清理存储')\n            sessionStorage.removeItem('fourWayActiveTask')\n          }\n        }\n      } catch (error) {\n        console.warn('从会话存储获取任务信息失败:', error)\n      }\n      return null\n    }\n\n    // 事件处理\n    const handleUploadSuccess = (response) => {\n      const taskId = response.data?.taskId || response.taskId || `task_${Date.now()}`\n      currentTaskId.value = taskId\n\n      // 使用增强的会话存储管理\n      saveTaskToSession(taskId, {\n        uploadTime: new Date().toISOString(),\n        status: 'processing'\n      })\n\n      // 更新当前任务信息\n      currentTask.value = {\n        id: taskId,\n        name: '四方向交通分析任务',\n        status: 'processing',\n        progress: 10,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      }\n\n      // 更新URL参数但不导航\n      router.replace({\n        path: route.path,\n        query: { ...route.query, taskId: taskId }\n      })\n\n      currentStep.value = 1\n      ElMessage.success('视频上传成功，开始实时检测')\n\n      // 开始轮询任务状态\n      startTaskStatusPolling(taskId)\n    }\n\n    const handleUploadError = (error) => {\n      ElMessage.error('视频上传失败: ' + error.message)\n    }\n\n    const handleUploadProgress = (progress) => {\n      console.log('上传进度:', progress)\n    }\n\n    const handleUploadStatusChange = (status) => {\n      console.log('上传状态变化:', status)\n    }\n\n    const handleDetectionUpdate = (data) => {\n      console.log('检测更新:', data)\n\n      // 更新任务进度\n      if (currentTask.value && data.progress) {\n        currentTask.value.progress = Math.min(data.progress, 90) // 检测阶段最多90%\n        currentTask.value.updatedAt = new Date()\n      }\n    }\n\n    const handleDetectionStatusChange = (status) => {\n      if (status === 'completed') {\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed'\n          currentTask.value.progress = 100\n          currentTask.value.updatedAt = new Date()\n        }\n\n        currentStep.value = 2\n        ElMessage.success('实时检测完成，开始智能分析')\n      }\n    }\n\n    // 处理四方向分析完成事件\n    const handleAnalysisComplete = async (completeData) => {\n      try {\n        console.log('🎉 收到四方向分析完成事件:', completeData)\n\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed'\n          currentTask.value.progress = 100\n          currentTask.value.updatedAt = new Date()\n        }\n\n        // 显示完成提示\n        ElMessage({\n          message: `四方向分析完成！检测到 ${completeData.summary?.totalVehicles || 0} 辆车辆，正在生成智能报告...`,\n          type: 'success',\n          duration: 4000\n        })\n\n        // 自动生成智能报告数据\n        try {\n          await generateReportFromAnalysisData(completeData)\n\n          // 延迟跳转到智能分析模块\n          setTimeout(() => {\n            currentStep.value = 2\n            ElMessage.info('已自动跳转到智能分析模块')\n          }, 1500)\n        } catch (error) {\n          console.error('自动生成报告失败:', error)\n          // 即使报告生成失败，也跳转到智能分析模块\n          setTimeout(() => {\n            currentStep.value = 2\n            ElMessage.warning('已跳转到智能分析模块，请手动生成报告')\n          }, 2000)\n        }\n\n      } catch (error) {\n        console.error('处理分析完成事件失败:', error)\n        ElMessage.error('处理完成事件失败，请手动跳转到智能分析')\n      }\n    }\n\n    const handleAnalysisDataUpdate = (data) => {\n      reportData.value = data\n      currentStep.value = 3\n      ElMessage.success('智能分析完成，可以生成报告')\n    }\n\n    // 从分析数据生成报告数据\n    const generateReportFromAnalysisData = async (completeData) => {\n      try {\n        if (!currentTaskId.value) {\n          throw new Error('缺少任务ID')\n        }\n\n        console.log('正在从分析数据生成报告...', completeData)\n\n        // 首先尝试从API获取完整的分析结果\n        let apiAnalysisData = null\n        try {\n          const response = await getFourWayAnalysisResult(currentTaskId.value)\n          apiAnalysisData = response.data\n          console.log('从API获取到分析数据:', apiAnalysisData)\n        } catch (apiError) {\n          console.warn('从API获取分析数据失败，使用WebSocket数据:', apiError)\n        }\n\n        // 合并WebSocket数据和API数据\n        const mergedData = {\n          ...completeData,\n          ...(apiAnalysisData || {})\n        }\n\n        // 计算智能分析指标\n        const totalVehicles = mergedData.summary?.totalVehicles || 0\n        const processingDuration = mergedData.summary?.processingDuration || 0\n        const directions = mergedData.directions || {}\n\n        // 计算流量平衡度\n        const directionCounts = Object.values(directions).map(d => d.vehicleCount || 0)\n        const maxCount = Math.max(...directionCounts, 1)\n        const minCount = Math.min(...directionCounts, 0)\n        const flowBalance = maxCount > 0 ? Math.round((1 - (maxCount - minCount) / maxCount) * 100) : 75\n\n        // 计算拥堵等级\n        const avgVehicles = totalVehicles / 4\n        let congestionLevel = '畅通'\n        if (avgVehicles > 30) congestionLevel = '重度拥堵'\n        else if (avgVehicles > 20) congestionLevel = '中度拥堵'\n        else if (avgVehicles > 10) congestionLevel = '轻度拥堵'\n\n        // 构建报告数据结构\n        const reportData = {\n          taskId: currentTaskId.value,\n          generatedAt: new Date(),\n          analysisType: '四方向智能分析',\n          summary: {\n            totalVehicles: totalVehicles,\n            vehicleIncrease: 0, // 可以后续计算\n            processingDuration: processingDuration,\n            efficiency: Math.round((totalVehicles / Math.max(1, processingDuration / 60)) * 100) / 100,\n            peakDirection: mergedData.summary?.peakDirection || '未知',\n            peakPercentage: maxCount > 0 ? Math.round((maxCount / totalVehicles) * 100) : 0,\n            congestionLevel: congestionLevel,\n            congestionTrend: '稳定'\n          },\n          directions: directions,\n          intelligentAnalysis: {\n            flowBalance: flowBalance,\n            peakHours: '08:00-09:00, 17:00-18:00',\n            flowTrend: totalVehicles > 50 ? '增长趋势' : '稳定',\n            congestionPrediction: congestionLevel === '畅通' ? '低风险' : '中等风险',\n            signalOptimization: mergedData.trafficAnalysis?.signalOptimization || {\n              recommendedCycle: 120,\n              greenTimeAllocation: {\n                east: 30,\n                south: 25,\n                west: 35,\n                north: 30\n              },\n              expectedImprovement: '通行效率提升15%'\n            }\n          },\n          recommendations: (mergedData.trafficAnalysis?.recommendations || [\n            '建议在高峰时段优化信号配时',\n            '考虑增加车流量较大方向的绿灯时间',\n            '建议定期监控交通流量变化'\n          ]).map((rec, index) => {\n            if (typeof rec === 'string') {\n              return {\n                title: `优化建议 ${index + 1}`,\n                description: rec,\n                type: 'signal',\n                priority: index === 0 ? 'high' : index === 1 ? 'medium' : 'low',\n                expectedImprovement: '预计提升通行效率10-15%'\n              }\n            }\n            return rec\n          }),\n          technicalMetrics: {\n            accuracy: 95.5,\n            processingSpeed: 25.0,\n            stability: 98.2,\n            dataIntegrity: 99.1,\n            responseTime: 150,\n            memoryUsage: 65.3,\n            cpuUsage: 45.8\n          }\n        }\n\n        // 设置报告数据\n        handleAnalysisDataUpdate(reportData)\n\n        console.log('报告数据生成成功:', reportData)\n        return reportData\n\n      } catch (error) {\n        console.error('生成报告数据失败:', error)\n        throw error\n      }\n    }\n\n    const handleExportReport = (taskId) => {\n      ElMessage.success('报告导出成功')\n    }\n\n    const handleRefreshReportData = (taskId) => {\n      ElMessage.success('报告数据刷新成功')\n    }\n    \n    // 快速操作\n    const goToUpload = () => {\n      currentStep.value = 0\n    }\n    \n    const startDetection = () => {\n      if (canDetect.value) {\n        currentStep.value = 1\n      } else {\n        ElMessage.warning('请先上传视频文件')\n      }\n    }\n    \n    const generateAnalysis = () => {\n      if (canAnalyze.value) {\n        currentStep.value = 2\n      } else {\n        ElMessage.warning('请先完成视频检测')\n      }\n    }\n    \n    const exportReport = () => {\n      if (canExport.value) {\n        currentStep.value = 3\n      } else {\n        ElMessage.warning('请先完成智能分析')\n      }\n    }\n\n    const refreshSystem = () => {\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n      ElMessage.success('系统状态已刷新')\n    }\n\n    const showSystemInfo = () => {\n      ElMessageBox.alert('四方向智能交通分析系统 v1.0.0', '系统信息', {\n        confirmButtonText: '确定'\n      })\n    }\n\n    // StepNavigator事件处理\n    const handleStepChange = (stepIndex) => {\n      console.log('步骤切换请求:', stepIndex)\n\n      // 检查是否可以切换到目标步骤\n      if (stepIndex <= currentStep.value) {\n        currentStep.value = stepIndex\n        ElMessage.info(`已切换到步骤 ${stepIndex + 1}`)\n      } else {\n        ElMessage.warning('请先完成前面的步骤')\n      }\n    }\n\n    const handleQuickAction = (actionKey) => {\n      console.log('快速操作:', actionKey)\n\n      switch (actionKey) {\n        case 'upload':\n          goToUpload()\n          break\n        case 'detection':\n          startDetection()\n          break\n        case 'analysis':\n          generateAnalysis()\n          break\n        case 'report':\n          exportReport()\n          break\n        default:\n          console.warn('未知的快速操作:', actionKey)\n      }\n    }\n\n    // 页面刷新优化\n    const handlePageRefresh = async () => {\n      try {\n        console.log('处理页面刷新事件')\n\n        // 检查是否是强制刷新（Ctrl+F5 或 Cmd+Shift+R）\n        const isHardRefresh = performance.navigation?.type === 1\n\n        if (isHardRefresh) {\n          console.log('检测到强制刷新，开始新会话')\n          await initializeNewSession()\n          return\n        }\n\n        // 普通刷新，检查会话状态\n        const sessionTask = getTaskFromSession()\n        if (sessionTask) {\n          console.log('页面刷新 - 恢复会话:', sessionTask)\n          currentTaskId.value = sessionTask.taskId\n          await fetchTaskStatusAndSetStep(sessionTask.taskId)\n        } else {\n          console.log('页面刷新 - 没有有效会话，开始新会话')\n          await initializeNewSession()\n        }\n\n      } catch (error) {\n        console.error('处理页面刷新失败:', error)\n        await initializeNewSession()\n      }\n    }\n\n    const handleRefreshStatus = async () => {\n      if (currentTaskId.value) {\n        ElMessage.info('正在刷新任务状态...')\n        await fetchTaskStatusAndSetStep(currentTaskId.value)\n      } else {\n        ElMessage.warning('没有活跃的任务')\n      }\n    }\n\n    // 页面状态一致性检查\n    const checkPageStateConsistency = () => {\n      try {\n        const urlTaskId = route.query.taskId\n        const sessionTask = getTaskFromSession()\n        const currentTaskIdValue = currentTaskId.value\n\n        console.log('页面状态一致性检查:', {\n          urlTaskId,\n          sessionTaskId: sessionTask?.taskId,\n          currentTaskIdValue\n        })\n\n        // 如果URL和会话存储不一致，以URL为准\n        if (urlTaskId && sessionTask && urlTaskId !== sessionTask.taskId) {\n          console.log('检测到状态不一致，以URL参数为准')\n          saveTaskToSession(urlTaskId)\n          currentTaskId.value = urlTaskId\n        }\n\n      } catch (error) {\n        console.error('页面状态一致性检查失败:', error)\n      }\n    }\n\n    // 任务状态辅助方法\n    const getTaskStatusType = (status) => {\n      const statusMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'failed': 'danger',\n        'paused': 'info'\n      }\n      return statusMap[status] || 'info'\n    }\n\n    const getTaskStatusText = (status) => {\n      const statusMap = {\n        'waiting': '等待中',\n        'processing': '处理中',\n        'completed': '已完成',\n        'failed': '失败',\n        'paused': '已暂停'\n      }\n      return statusMap[status] || '未知'\n    }\n\n    const getProgressStatus = (status) => {\n      if (status === 'completed') return 'success'\n      if (status === 'failed') return 'exception'\n      return null\n    }\n\n    const formatTime = (time) => {\n      if (!time) return '-'\n      return new Date(time).toLocaleString()\n    }\n\n    const getProcessingDuration = (task) => {\n      if (!task || !task.createdAt) return '-'\n      const start = new Date(task.createdAt)\n      const end = task.updatedAt ? new Date(task.updatedAt) : new Date()\n      const duration = Math.floor((end - start) / 1000)\n\n      if (duration < 60) return `${duration}秒`\n      if (duration < 3600) return `${Math.floor(duration / 60)}分钟`\n      return `${Math.floor(duration / 3600)}小时`\n    }\n\n    // 监听路由变化 - 增强版\n    watch(() => route.query, (newQuery, oldQuery) => {\n      const newTaskId = newQuery.taskId\n      const newMode = newQuery.mode\n      const oldTaskId = oldQuery?.taskId\n      const oldMode = oldQuery?.mode\n\n      console.log('路由参数变化:', { newTaskId, newMode, oldTaskId, oldMode })\n\n      // 检测模式变化\n      if (newMode !== oldMode) {\n        if (newMode === 'new') {\n          console.log('检测到新会话模式参数')\n          initializeNewSession()\n          return\n        }\n      }\n\n      // 检测任务ID变化\n      if (newTaskId && newTaskId !== currentTaskId.value) {\n        console.log('检测到URL中的taskId变化:', newTaskId)\n        currentTaskId.value = newTaskId\n        fetchTaskStatusAndSetStep(newTaskId)\n      }\n    }, { deep: true })\n\n    // 状态隔离机制\n    const createSessionNamespace = (taskId) => {\n      return `fourWay_${taskId}_${Date.now()}`\n    }\n\n    const getNamespacedStorageKey = (key, taskId = null) => {\n      const baseKey = taskId ? `${key}_${taskId}` : key\n      return `fourWayConsole_${baseKey}`\n    }\n\n    const setNamespacedStorage = (key, value, taskId = null) => {\n      try {\n        const namespacedKey = getNamespacedStorageKey(key, taskId)\n        sessionStorage.setItem(namespacedKey, JSON.stringify(value))\n      } catch (error) {\n        console.warn('设置命名空间存储失败:', error)\n      }\n    }\n\n    const getNamespacedStorage = (key, taskId = null) => {\n      try {\n        const namespacedKey = getNamespacedStorageKey(key, taskId)\n        const value = sessionStorage.getItem(namespacedKey)\n        return value ? JSON.parse(value) : null\n      } catch (error) {\n        console.warn('获取命名空间存储失败:', error)\n        return null\n      }\n    }\n\n    const clearNamespacedStorage = (taskId = null) => {\n      try {\n        const prefix = taskId ? `fourWayConsole_${taskId}` : 'fourWayConsole_'\n        const keysToRemove = []\n\n        for (let i = 0; i < sessionStorage.length; i++) {\n          const key = sessionStorage.key(i)\n          if (key && key.startsWith(prefix)) {\n            keysToRemove.push(key)\n          }\n        }\n\n        keysToRemove.forEach(key => sessionStorage.removeItem(key))\n        console.log('已清理命名空间存储:', keysToRemove.length, '个项目')\n\n      } catch (error) {\n        console.warn('清理命名空间存储失败:', error)\n      }\n    }\n\n    // 生命周期\n    onMounted(async () => {\n      // 初始化系统状态\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n      activeConnections.value = 1\n\n      // 页面状态一致性检查\n      checkPageStateConsistency()\n\n      // 初始化页面状态\n      await initializeFromRoute()\n    })\n\n    onUnmounted(() => {\n      // 清理轮询\n      stopTaskStatusPolling()\n    })\n    \n    return {\n      // 图标组件\n      Upload,\n      VideoCamera,\n      DataAnalysis,\n      Document,\n      Plus,\n      MoreFilled,\n      Refresh,\n      InfoFilled,\n\n      // 响应式数据\n      currentStep,\n      currentTaskId,\n      currentTask,\n      reportData,\n      activeConnections,\n      lastUpdateTime,\n      systemStatus,\n      isInitializing,\n\n      // 计算属性\n      totalTasks,\n      activeTasks,\n      completedTasks,\n      canUpload,\n      canDetect,\n      canAnalyze,\n      canExport,\n\n      // 用户反馈和提示\n      showUserFeedback: (message, type = 'info', duration = 3000) => {\n        ElMessage({\n          message,\n          type,\n          duration,\n          showClose: true\n        })\n      },\n\n      showConfirmDialog: async (message, title = '确认操作') => {\n        try {\n          await ElMessageBox.confirm(message, title, {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          })\n          return true\n        } catch {\n          return false\n        }\n      },\n\n      // 方法\n      initializeFromRoute,\n      fetchTaskStatusAndSetStep,\n      setStepByTaskStatus,\n      checkActiveTask,\n      detectSessionMode,\n      initializeNewSession,\n      clearPreviousSession,\n      showSessionChoiceDialog,\n      saveTaskToSession,\n      getTaskFromSession,\n      handlePageRefresh,\n      checkPageStateConsistency,\n      createSessionNamespace,\n      getNamespacedStorageKey,\n      setNamespacedStorage,\n      getNamespacedStorage,\n      clearNamespacedStorage,\n      startTaskStatusPolling,\n      stopTaskStatusPolling,\n      handleUploadSuccess,\n      handleUploadError,\n      handleUploadProgress,\n      handleUploadStatusChange,\n      handleDetectionUpdate,\n      handleDetectionStatusChange,\n      handleAnalysisComplete,\n      handleAnalysisDataUpdate,\n      handleExportReport,\n      handleRefreshReportData,\n      goToUpload,\n      startDetection,\n      generateAnalysis,\n      exportReport,\n      refreshSystem,\n      showSystemInfo,\n      handleStepChange,\n      handleQuickAction,\n      handleRefreshStatus,\n\n      // 任务状态辅助方法\n      getTaskStatusType,\n      getTaskStatusText,\n      getProgressStatus,\n      formatTime,\n      getProcessingDuration\n    }\n  }\n}\n</script>\n\n<style scoped>\n.four-way-analysis-console {\n  min-height: 100vh;\n  background: #f5f7fa;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 控制台头部 */\n.console-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 24px 32px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.console-title {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 28px;\n  font-weight: 700;\n  margin: 0 0 8px 0;\n}\n\n.console-description {\n  font-size: 16px;\n  opacity: 0.9;\n  margin: 0;\n  line-height: 1.5;\n}\n\n.header-stats {\n  display: flex;\n  gap: 32px;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: 700;\n  line-height: 1.2;\n}\n\n.stat-label {\n  font-size: 14px;\n  opacity: 0.8;\n  margin-top: 4px;\n}\n\n/* 工作流程导航 */\n.workflow-navigation {\n  background: white;\n  padding: 24px 32px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n/* 主要内容区域 */\n.console-content {\n  flex: 1;\n  display: grid;\n  grid-template-columns: 280px 1fr;\n  gap: 24px;\n  padding: 24px 32px;\n  min-height: 0;\n}\n\n/* 左侧面板 */\n.left-panel {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.quick-actions-card {\n  height: fit-content;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.quick-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.quick-actions .el-button {\n  justify-content: flex-start;\n}\n\n/* 主内容区域 */\n.main-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  min-height: 0;\n}\n\n.current-task-info {\n  flex-shrink: 0;\n}\n\n.task-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.task-title-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.task-title-section h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.task-progress-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  min-width: 200px;\n}\n\n.progress-text {\n  font-size: 14px;\n  font-weight: 500;\n  color: #6b7280;\n  min-width: 40px;\n}\n\n.task-details {\n  margin-top: 16px;\n}\n\n.detail-item {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.detail-label {\n  font-size: 12px;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.detail-value {\n  font-size: 14px;\n  color: #1f2937;\n  font-weight: 500;\n}\n\n.dynamic-content {\n  flex: 1;\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  overflow: auto;\n}\n\n.step-content {\n  height: 100%;\n}\n\n/* 加载状态样式 */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 400px;\n  gap: 20px;\n}\n\n.loading-content {\n  width: 100%;\n  max-width: 600px;\n}\n\n.loading-text {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  color: #6b7280;\n  margin-top: 20px;\n}\n\n.loading-icon {\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n/* 底部状态栏 */\n.console-footer {\n  background: white;\n  border-top: 1px solid #e5e7eb;\n  padding: 12px 32px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 14px;\n}\n\n.footer-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #6b7280;\n}\n\n.separator {\n  color: #d1d5db;\n}\n\n.footer-actions {\n  display: flex;\n  gap: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .console-content {\n    grid-template-columns: 300px 1fr;\n  }\n\n  .header-stats {\n    gap: 16px;\n  }\n}\n\n@media (max-width: 768px) {\n  .console-header {\n    flex-direction: column;\n    gap: 16px;\n    text-align: center;\n  }\n\n  .header-stats {\n    justify-content: center;\n  }\n\n  .console-content {\n    grid-template-columns: 1fr;\n    padding: 16px;\n  }\n\n  .workflow-navigation {\n    padding: 16px;\n  }\n\n  .console-footer {\n    flex-direction: column;\n    gap: 12px;\n    padding: 16px;\n  }\n\n  .task-header {\n    flex-direction: column;\n    gap: 12px;\n    align-items: flex-start;\n  }\n\n  .task-progress-section {\n    width: 100%;\n    min-width: auto;\n  }\n}\n\n/* 滚动条样式 */\n.task-list::-webkit-scrollbar {\n  width: 6px;\n}\n\n.task-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.task-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.task-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 动画效果 */\n.task-item {\n  animation: slideIn 0.3s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.step-content {\n  animation: fadeIn 0.5s ease-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n</style>\n"], "mappings": ";;;AAoOA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAI,QAAS,KAAI;AAC3E,SAASC,SAAS,EAAEC,QAAO,QAAS,YAAW;AAC/C,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SACEC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,IAAI,EACvDC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAM,QAClC,yBAAwB;;AAE/B;AACA,OAAOC,kBAAiB,MAAO,8CAA6C;AAC5E,OAAOC,qBAAoB,MAAO,iDAAgD;AAClF,OAAOC,wBAAuB,MAAO,oDAAmD;AACxF,OAAOC,wBAAuB,MAAO,oDAAmD;AACxF,OAAOC,aAAY,MAAO,yCAAwC;;AAElE;AACA,SACEC,oBAAoB,EACpBC,wBAAwB,EACxBC,4BAA2B,QACtB,aAAY;AAEnB,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,UAAU,EAAE;IACVnB,IAAI;IAAEC,MAAM;IAAEC,WAAW;IAAEC,YAAY;IAAEC,QAAQ;IAAEC,IAAI;IACvDC,UAAU;IAAEC,OAAO;IAAEC,UAAU;IAAEC,OAAO;IACxCC,kBAAkB;IAClBC,qBAAqB;IACrBC,wBAAwB;IACxBC,wBAAwB;IACxBC;EACF,CAAC;EACDM,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIzB,SAAS,CAAC;IACzB,MAAM0B,KAAI,GAAIzB,QAAQ,CAAC;;IAEvB;IACA,MAAM0B,WAAU,GAAIjC,GAAG,CAAC,CAAC;IACzB,MAAMkC,aAAY,GAAIlC,GAAG,CAAC,EAAE;IAC5B,MAAMmC,WAAU,GAAInC,GAAG,CAAC;MACtBoC,EAAE,EAAE,EAAE;MACNR,IAAI,EAAE,WAAW;MACjBS,MAAM,EAAE,SAAS;MACjBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;IACtB,CAAC;IACD,MAAME,UAAS,GAAI1C,GAAG,CAAC,IAAI;IAC3B,MAAM2C,iBAAgB,GAAI3C,GAAG,CAAC,CAAC;IAC/B,MAAM4C,cAAa,GAAI5C,GAAG,CAAC,EAAE;IAC7B,MAAM6C,cAAa,GAAI7C,GAAG,CAAC,IAAI;IAC/B,MAAM8C,iBAAgB,GAAI9C,GAAG,CAAC,IAAI;;IAElC;IACA,MAAM+C,YAAW,GAAI9C,QAAQ,CAAC;MAC5B+C,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE;IACR,CAAC;;IAED;IACA,MAAMC,UAAS,GAAIhD,QAAQ,CAAC,MAAM,CAAC;IACnC,MAAMiD,WAAU,GAAIjD,QAAQ,CAAC,MAAM+B,WAAW,CAACmB,KAAI,GAAI,KAAKnB,WAAW,CAACmB,KAAI,GAAI,IAAI,IAAI,CAAC;IACzF,MAAMC,cAAa,GAAInD,QAAQ,CAAC,MAAM+B,WAAW,CAACmB,KAAI,KAAM,IAAI,IAAI,CAAC;IAErE,MAAME,SAAQ,GAAIpD,QAAQ,CAAC,MAAM,IAAI;IACrC,MAAMqD,SAAQ,GAAIrD,QAAQ,CAAC,MAAM+B,WAAW,CAACmB,KAAI,IAAK,CAAC;IACvD,MAAMI,UAAS,GAAItD,QAAQ,CAAC,MAAM+B,WAAW,CAACmB,KAAI,IAAK,CAAC;IACxD,MAAMK,SAAQ,GAAIvD,QAAQ,CAAC,MAAM+B,WAAW,CAACmB,KAAI,IAAK,CAAC;;IAEvD;;IAEA;IACA,MAAMM,mBAAkB,GAAI,MAAAA,CAAA,KAAY;MACtC,IAAI;QACFb,cAAc,CAACO,KAAI,GAAI,IAAG;;QAE1B;QACA,MAAMO,WAAU,GAAI3B,KAAK,CAAC4B,KAAK,CAACC,IAAG;QACnC,MAAMC,SAAQ,GAAI9B,KAAK,CAAC4B,KAAK,CAACG,MAAK,IAAK/B,KAAK,CAACgC,MAAM,CAACD,MAAK;QAE1DE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEP,WAAW,EAAE,OAAO,EAAEG,SAAS;;QAE1D;QACA,IAAIH,WAAU,KAAM,KAAK,EAAE;UACzBM,OAAO,CAACC,GAAG,CAAC,kBAAkB;UAC9B,MAAMC,oBAAoB,CAAC;QAC7B,OAAO,IAAIL,SAAS,EAAE;UACpBG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEJ,SAAS;UACvC5B,aAAa,CAACkB,KAAI,GAAIU,SAAQ;UAC9B;UACA,MAAMM,yBAAyB,CAACN,SAAS;QAC3C,OAAO;UACL;UACA,MAAMO,iBAAiB,CAAC;QAC1B;MAEF,EAAE,OAAOC,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7B9D,SAAS,CAAC8D,KAAK,CAAC,aAAY,GAAIA,KAAK,CAACC,OAAO;MAC/C,UAAU;QACR1B,cAAc,CAACO,KAAI,GAAI,KAAI;MAC7B;IACF;IAEA,MAAMgB,yBAAwB,GAAI,MAAOL,MAAM,IAAK;MAClD,IAAI;QACF,MAAMS,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,YAAY;QAC/C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIG,KAAK,CAAC,SAAS;QAC3B;QAEA,MAAMC,QAAO,GAAI,MAAMC,KAAK,CAAC,gCAAgCd,MAAM,SAAS,EAAE;UAC5Ee,OAAO,EAAE;YACP,eAAe,EAAE,UAAUN,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC;QAED,IAAI,CAACI,QAAQ,CAACG,EAAE,EAAE;UAChB,MAAM,IAAIJ,KAAK,CAAC,aAAaC,QAAQ,CAACvC,MAAM,EAAE;QAChD;QAEA,MAAM2C,QAAO,GAAI,MAAMJ,QAAQ,CAACK,IAAI,CAAC;QACrChB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEc,QAAQ;;QAEhC;QACA7C,WAAW,CAACiB,KAAI,GAAI;UAClBhB,EAAE,EAAE4C,QAAQ,CAACjB,MAAK,IAAKA,MAAM;UAC7BnC,IAAI,EAAEoD,QAAQ,CAACpD,IAAG,IAAK,WAAW;UAClCS,MAAM,EAAE2C,QAAQ,CAAC3C,MAAK,IAAK,SAAS;UACpCC,QAAQ,EAAE0C,QAAQ,CAAC1C,QAAO,IAAK,CAAC;UAChCC,SAAS,EAAEyC,QAAQ,CAACzC,SAAQ,GAAI,IAAIC,IAAI,CAACwC,QAAQ,CAACzC,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC;UACzEC,SAAS,EAAEuC,QAAQ,CAACvC,SAAQ,GAAI,IAAID,IAAI,CAACwC,QAAQ,CAACvC,SAAS,IAAI,IAAID,IAAI,CAAC;QAC1E;;QAEA;QACA0C,mBAAmB,CAACF,QAAQ,CAAC3C,MAAM,EAAE2C,QAAQ,CAAC1C,QAAQ;;QAEtD;QACA,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC6C,QAAQ,CAACH,QAAQ,CAAC3C,MAAM,CAAC,EAAE;UACtD+C,sBAAsB,CAACrB,MAAM;QAC/B;MAEF,EAAE,OAAOO,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC9D,SAAS,CAAC6E,OAAO,CAAC,mBAAmB;QACrCpD,WAAW,CAACmB,KAAI,GAAI;MACtB;IACF;IAEA,MAAM8B,mBAAkB,GAAIA,CAAC7C,MAAM,EAAEC,QAAO,GAAI,CAAC,KAAK;MACpD2B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QAAE7B,MAAM;QAAEC;MAAS,CAAC;MAE/C,QAAQD,MAAM;QACZ,KAAK,QAAQ;QACb,KAAK,WAAW;UACdJ,WAAW,CAACmB,KAAI,GAAI,GAAE;UACtB5C,SAAS,CAAC8E,IAAI,CAAC,eAAe;UAC9B;QAEF,KAAK,YAAY;UACf,IAAIhD,QAAO,GAAI,EAAE,EAAE;YACjBL,WAAW,CAACmB,KAAI,GAAI,GAAE;YACtB5C,SAAS,CAAC8E,IAAI,CAAC,YAAY;UAC7B,OAAO,IAAIhD,QAAO,GAAI,EAAE,EAAE;YACxBL,WAAW,CAACmB,KAAI,GAAI,GAAE;YACtB5C,SAAS,CAAC8E,IAAI,CAAC,SAAS;UAC1B,OAAO;YACLrD,WAAW,CAACmB,KAAI,GAAI,GAAE;YACtB5C,SAAS,CAAC8E,IAAI,CAAC,UAAU;UAC3B;UACA;QAEF,KAAK,WAAW;UACdrD,WAAW,CAACmB,KAAI,GAAI,GAAE;UACtB5C,SAAS,CAAC+E,OAAO,CAAC,gBAAgB;UAClC;QAEF,KAAK,QAAQ;UACXtD,WAAW,CAACmB,KAAI,GAAI,GAAE;UACtB5C,SAAS,CAAC8D,KAAK,CAAC,cAAc;UAC9B;QAEF;UACErC,WAAW,CAACmB,KAAI,GAAI,GAAE;UACtB;MACJ;IACF;;IAEA;IACA,MAAMiB,iBAAgB,GAAI,MAAAA,CAAA,KAAY;MACpC,IAAI;QACF;QACA,MAAMmB,WAAU,GAAIC,cAAc,CAACf,OAAO,CAAC,mBAAmB;QAC9D,IAAIc,WAAW,EAAE;UACf,MAAME,QAAO,GAAIC,IAAI,CAACC,KAAK,CAACJ,WAAW;UACvC,MAAMK,UAAS,GAAIrD,IAAI,CAACsD,GAAG,CAAC,IAAIJ,QAAQ,CAACK,SAAQ;UACjD,MAAMC,aAAY,GAAI,EAAC,GAAI,EAAC,GAAI,EAAC,GAAI,IAAG,EAAE;;UAE1C/B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEwB,QAAQ,EAAE,OAAO,EAAEG,UAAS,GAAI,IAAG,GAAI,EAAE,EAAE,IAAI;;UAEvE;UACA,IAAIA,UAAS,GAAIG,aAAa,EAAE;YAC9B/B,OAAO,CAACC,GAAG,CAAC,eAAe;YAC3B,MAAMC,oBAAoB,CAAC;YAC3B;UACF;;UAEA;UACA,MAAM8B,uBAAuB,CAACP,QAAQ;QACxC,OAAO;UACL;UACAzB,OAAO,CAACC,GAAG,CAAC,iBAAiB;UAC7B,MAAMC,oBAAoB,CAAC;QAC7B;MAEF,EAAE,OAAOG,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B,MAAMH,oBAAoB,CAAC;MAC7B;IACF;;IAEA;IACA,MAAMA,oBAAmB,GAAI,MAAAA,CAAA,KAAY;MACvC,IAAI;QACFF,OAAO,CAACC,GAAG,CAAC,QAAQ;;QAEpB;QACA,MAAMgC,oBAAoB,CAAC;;QAE3B;QACAjE,WAAW,CAACmB,KAAI,GAAI;QACpBlB,aAAa,CAACkB,KAAI,GAAI,IAAG;QACzBjB,WAAW,CAACiB,KAAI,GAAI,CAAC;QACrBV,UAAU,CAACU,KAAI,GAAI,IAAG;;QAEtB;QACA+C,qBAAqB,CAAC;;QAEtB;QACA,IAAInE,KAAK,CAAC4B,KAAK,CAACG,MAAM,EAAE;UACtBhC,MAAM,CAACqE,OAAO,CAAC;YACbC,IAAI,EAAErE,KAAK,CAACqE,IAAI;YAChBzC,KAAK,EAAE;cAAE,GAAG5B,KAAK,CAAC4B,KAAK;cAAEG,MAAM,EAAEuC;YAAU;UAC7C,CAAC;QACH;QAEA9F,SAAS,CAAC+E,OAAO,CAAC,WAAW;MAE/B,EAAE,OAAOjB,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC9D,SAAS,CAAC8D,KAAK,CAAC,YAAW,GAAIA,KAAK,CAACC,OAAO;MAC9C;IACF;;IAEA;IACA,MAAM2B,oBAAmB,GAAI,MAAAA,CAAA,KAAY;MACvC,IAAI;QACFjC,OAAO,CAACC,GAAG,CAAC,WAAW;;QAEvB;QACAuB,cAAc,CAACc,UAAU,CAAC,mBAAmB;;QAE7C;QACA,MAAMC,YAAW,GAAI,CAAC,aAAa,EAAE,iBAAiB,EAAE,gBAAgB;QACxEA,YAAY,CAACC,OAAO,CAACC,GAAE,IAAK;UAC1B,IAAI;YACFjB,cAAc,CAACc,UAAU,CAACG,GAAG;UAC/B,EAAE,OAAOC,CAAC,EAAE;YACV1C,OAAO,CAAC2C,IAAI,CAAC,SAASF,GAAG,MAAM,EAAEC,CAAC;UACpC;QACF,CAAC;MAEH,EAAE,OAAOrC,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;MAClC;IACF;;IAEA;IACA,MAAM2B,uBAAsB,GAAI,MAAOP,QAAQ,IAAK;MAClD,IAAI;QACF,MAAMmB,MAAK,GAAI,MAAMpG,YAAY,CAACqG,OAAO,CACvC,4BAA4BpB,QAAQ,CAAC3B,MAAM,QAAQ,EACnD,MAAM,EACN;UACEgD,iBAAiB,EAAE,SAAS;UAC5BC,gBAAgB,EAAE,QAAQ;UAC1BhE,IAAI,EAAE,UAAU;UAChBiE,yBAAyB,EAAE,IAAI;UAC/BC,iBAAiB,EAAE,KAAK;UACxBC,kBAAkB,EAAE,KAAK;UACzBC,SAAS,EAAE,KAAK;UAChBC,WAAW,EAAE;QACf,CACF;;QAEA;QACA,IAAIR,MAAK,KAAM,SAAS,EAAE;UACxB5C,OAAO,CAACC,GAAG,CAAC,aAAa;UACzBhC,aAAa,CAACkB,KAAI,GAAIsC,QAAQ,CAAC3B,MAAK;UACpC,MAAMK,yBAAyB,CAACsB,QAAQ,CAAC3B,MAAM;UAC/CvD,SAAS,CAAC8E,IAAI,CAAC,YAAY;QAC7B;MAEF,EAAE,OAAOgC,MAAM,EAAE;QACf;QACA,IAAIA,MAAK,KAAM,QAAQ,EAAE;UACvBrD,OAAO,CAACC,GAAG,CAAC,YAAY;UACxB,MAAMC,oBAAoB,CAAC;QAC7B,OAAO;UACL;UACAF,OAAO,CAACC,GAAG,CAAC,eAAe;UAC3B,MAAMC,oBAAoB,CAAC;QAC7B;MACF;IACF;IAEA,MAAMoD,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC;MACA,MAAMlD,iBAAiB,CAAC;IAC1B;IAEA,MAAMe,sBAAqB,GAAKrB,MAAM,IAAK;MACzC;MACA,IAAIjB,iBAAiB,CAACM,KAAK,EAAE;QAC3BoE,aAAa,CAAC1E,iBAAiB,CAACM,KAAK;MACvC;MAEAa,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEH,MAAM;MAE/BjB,iBAAiB,CAACM,KAAI,GAAIqE,WAAW,CAAC,YAAY;QAChD,IAAI;UACF,MAAMrD,yBAAyB,CAACL,MAAM;;UAEtC;UACA,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAACoB,QAAQ,CAAChD,WAAW,CAACiB,KAAK,CAACf,MAAM,CAAC,EAAE;YAC9DmF,aAAa,CAAC1E,iBAAiB,CAACM,KAAK;YACrCN,iBAAiB,CAACM,KAAI,GAAI,IAAG;UAC/B;QAEF,EAAE,OAAOkB,KAAK,EAAE;UACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;QAClC;MACF,CAAC,EAAE,IAAI,GAAE;IACX;IAEA,MAAM6B,qBAAoB,GAAIA,CAAA,KAAM;MAClC,IAAIrD,iBAAiB,CAACM,KAAK,EAAE;QAC3BoE,aAAa,CAAC1E,iBAAiB,CAACM,KAAK;QACrCN,iBAAiB,CAACM,KAAI,GAAI,IAAG;MAC/B;IACF;;IAEA;IACA,MAAMsE,iBAAgB,GAAIA,CAAC3D,MAAM,EAAE4D,cAAa,GAAI,CAAC,CAAC,KAAK;MACzD,IAAI;QACF,MAAMnC,WAAU,GAAI;UAClBzB,MAAM,EAAEA,MAAM;UACdgC,SAAS,EAAEvD,IAAI,CAACsD,GAAG,CAAC,CAAC;UACrB8B,SAAS,EAAE,WAAWpF,IAAI,CAACsD,GAAG,CAAC,CAAC,IAAI+B,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC7EnE,IAAI,EAAE,QAAQ;UACd,GAAG8D;QACL;QAEAlC,cAAc,CAACwC,OAAO,CAAC,mBAAmB,EAAEtC,IAAI,CAACuC,SAAS,CAAC1C,WAAW,CAAC;QACvEvB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEsB,WAAW;MAExC,EAAE,OAAOlB,KAAK,EAAE;QACdL,OAAO,CAAC2C,IAAI,CAAC,gBAAgB,EAAEtC,KAAK;MACtC;IACF;IAEA,MAAM6D,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,IAAI;QACF,MAAM3C,WAAU,GAAIC,cAAc,CAACf,OAAO,CAAC,mBAAmB;QAC9D,IAAIc,WAAW,EAAE;UACf,MAAME,QAAO,GAAIC,IAAI,CAACC,KAAK,CAACJ,WAAW;;UAEvC;UACA,MAAMK,UAAS,GAAIrD,IAAI,CAACsD,GAAG,CAAC,IAAIJ,QAAQ,CAACK,SAAQ;UACjD,MAAMC,aAAY,GAAI,EAAC,GAAI,EAAC,GAAI,EAAC,GAAI,IAAG,EAAE;;UAE1C,IAAIH,UAAS,IAAKG,aAAa,EAAE;YAC/B,OAAON,QAAO;UAChB,OAAO;YACLzB,OAAO,CAACC,GAAG,CAAC,cAAc;YAC1BuB,cAAc,CAACc,UAAU,CAAC,mBAAmB;UAC/C;QACF;MACF,EAAE,OAAOjC,KAAK,EAAE;QACdL,OAAO,CAAC2C,IAAI,CAAC,gBAAgB,EAAEtC,KAAK;MACtC;MACA,OAAO,IAAG;IACZ;;IAEA;IACA,MAAM8D,mBAAkB,GAAKxD,QAAQ,IAAK;MACxC,MAAMb,MAAK,GAAIa,QAAQ,CAACyD,IAAI,EAAEtE,MAAK,IAAKa,QAAQ,CAACb,MAAK,IAAK,QAAQvB,IAAI,CAACsD,GAAG,CAAC,CAAC,EAAC;MAC9E5D,aAAa,CAACkB,KAAI,GAAIW,MAAK;;MAE3B;MACA2D,iBAAiB,CAAC3D,MAAM,EAAE;QACxBuE,UAAU,EAAE,IAAI9F,IAAI,CAAC,CAAC,CAAC+F,WAAW,CAAC,CAAC;QACpClG,MAAM,EAAE;MACV,CAAC;;MAED;MACAF,WAAW,CAACiB,KAAI,GAAI;QAClBhB,EAAE,EAAE2B,MAAM;QACVnC,IAAI,EAAE,WAAW;QACjBS,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;MACtB;;MAEA;MACAT,MAAM,CAACqE,OAAO,CAAC;QACbC,IAAI,EAAErE,KAAK,CAACqE,IAAI;QAChBzC,KAAK,EAAE;UAAE,GAAG5B,KAAK,CAAC4B,KAAK;UAAEG,MAAM,EAAEA;QAAO;MAC1C,CAAC;MAED9B,WAAW,CAACmB,KAAI,GAAI;MACpB5C,SAAS,CAAC+E,OAAO,CAAC,eAAe;;MAEjC;MACAH,sBAAsB,CAACrB,MAAM;IAC/B;IAEA,MAAMyE,iBAAgB,GAAKlE,KAAK,IAAK;MACnC9D,SAAS,CAAC8D,KAAK,CAAC,UAAS,GAAIA,KAAK,CAACC,OAAO;IAC5C;IAEA,MAAMkE,oBAAmB,GAAKnG,QAAQ,IAAK;MACzC2B,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE5B,QAAQ;IAC/B;IAEA,MAAMoG,wBAAuB,GAAKrG,MAAM,IAAK;MAC3C4B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE7B,MAAM;IAC/B;IAEA,MAAMsG,qBAAoB,GAAKN,IAAI,IAAK;MACtCpE,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEmE,IAAI;;MAEzB;MACA,IAAIlG,WAAW,CAACiB,KAAI,IAAKiF,IAAI,CAAC/F,QAAQ,EAAE;QACtCH,WAAW,CAACiB,KAAK,CAACd,QAAO,GAAIuF,IAAI,CAACe,GAAG,CAACP,IAAI,CAAC/F,QAAQ,EAAE,EAAE,GAAE;QACzDH,WAAW,CAACiB,KAAK,CAACX,SAAQ,GAAI,IAAID,IAAI,CAAC;MACzC;IACF;IAEA,MAAMqG,2BAA0B,GAAKxG,MAAM,IAAK;MAC9C,IAAIA,MAAK,KAAM,WAAW,EAAE;QAC1B;QACA,IAAIF,WAAW,CAACiB,KAAK,EAAE;UACrBjB,WAAW,CAACiB,KAAK,CAACf,MAAK,GAAI,WAAU;UACrCF,WAAW,CAACiB,KAAK,CAACd,QAAO,GAAI,GAAE;UAC/BH,WAAW,CAACiB,KAAK,CAACX,SAAQ,GAAI,IAAID,IAAI,CAAC;QACzC;QAEAP,WAAW,CAACmB,KAAI,GAAI;QACpB5C,SAAS,CAAC+E,OAAO,CAAC,eAAe;MACnC;IACF;;IAEA;IACA,MAAMuD,sBAAqB,GAAI,MAAOC,YAAY,IAAK;MACrD,IAAI;QACF9E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE6E,YAAY;;QAE3C;QACA,IAAI5G,WAAW,CAACiB,KAAK,EAAE;UACrBjB,WAAW,CAACiB,KAAK,CAACf,MAAK,GAAI,WAAU;UACrCF,WAAW,CAACiB,KAAK,CAACd,QAAO,GAAI,GAAE;UAC/BH,WAAW,CAACiB,KAAK,CAACX,SAAQ,GAAI,IAAID,IAAI,CAAC;QACzC;;QAEA;QACAhC,SAAS,CAAC;UACR+D,OAAO,EAAE,eAAewE,YAAY,CAACC,OAAO,EAAEC,aAAY,IAAK,CAAC,kBAAkB;UAClFjG,IAAI,EAAE,SAAS;UACfkG,QAAQ,EAAE;QACZ,CAAC;;QAED;QACA,IAAI;UACF,MAAMC,8BAA8B,CAACJ,YAAY;;UAEjD;UACAK,UAAU,CAAC,MAAM;YACfnH,WAAW,CAACmB,KAAI,GAAI;YACpB5C,SAAS,CAAC8E,IAAI,CAAC,cAAc;UAC/B,CAAC,EAAE,IAAI;QACT,EAAE,OAAOhB,KAAK,EAAE;UACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;UAChC;UACA8E,UAAU,CAAC,MAAM;YACfnH,WAAW,CAACmB,KAAI,GAAI;YACpB5C,SAAS,CAAC6E,OAAO,CAAC,oBAAoB;UACxC,CAAC,EAAE,IAAI;QACT;MAEF,EAAE,OAAOf,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,aAAa,EAAEA,KAAK;QAClC9D,SAAS,CAAC8D,KAAK,CAAC,qBAAqB;MACvC;IACF;IAEA,MAAM+E,wBAAuB,GAAKhB,IAAI,IAAK;MACzC3F,UAAU,CAACU,KAAI,GAAIiF,IAAG;MACtBpG,WAAW,CAACmB,KAAI,GAAI;MACpB5C,SAAS,CAAC+E,OAAO,CAAC,eAAe;IACnC;;IAEA;IACA,MAAM4D,8BAA6B,GAAI,MAAOJ,YAAY,IAAK;MAC7D,IAAI;QACF,IAAI,CAAC7G,aAAa,CAACkB,KAAK,EAAE;UACxB,MAAM,IAAIuB,KAAK,CAAC,QAAQ;QAC1B;QAEAV,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE6E,YAAY;;QAE1C;QACA,IAAIO,eAAc,GAAI,IAAG;QACzB,IAAI;UACF,MAAM1E,QAAO,GAAI,MAAMlD,wBAAwB,CAACQ,aAAa,CAACkB,KAAK;UACnEkG,eAAc,GAAI1E,QAAQ,CAACyD,IAAG;UAC9BpE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEoF,eAAe;QAC7C,EAAE,OAAOC,QAAQ,EAAE;UACjBtF,OAAO,CAAC2C,IAAI,CAAC,6BAA6B,EAAE2C,QAAQ;QACtD;;QAEA;QACA,MAAMC,UAAS,GAAI;UACjB,GAAGT,YAAY;UACf,IAAIO,eAAc,IAAK,CAAC,CAAC;QAC3B;;QAEA;QACA,MAAML,aAAY,GAAIO,UAAU,CAACR,OAAO,EAAEC,aAAY,IAAK;QAC3D,MAAMQ,kBAAiB,GAAID,UAAU,CAACR,OAAO,EAAES,kBAAiB,IAAK;QACrE,MAAMC,UAAS,GAAIF,UAAU,CAACE,UAAS,IAAK,CAAC;;QAE7C;QACA,MAAMC,eAAc,GAAIC,MAAM,CAACC,MAAM,CAACH,UAAU,CAAC,CAACI,GAAG,CAACC,CAAA,IAAKA,CAAC,CAACC,YAAW,IAAK,CAAC;QAC9E,MAAMC,QAAO,GAAIpC,IAAI,CAACqC,GAAG,CAAC,GAAGP,eAAe,EAAE,CAAC;QAC/C,MAAMQ,QAAO,GAAItC,IAAI,CAACe,GAAG,CAAC,GAAGe,eAAe,EAAE,CAAC;QAC/C,MAAMS,WAAU,GAAIH,QAAO,GAAI,IAAIpC,IAAI,CAACwC,KAAK,CAAC,CAAC,IAAI,CAACJ,QAAO,GAAIE,QAAQ,IAAIF,QAAQ,IAAI,GAAG,IAAI,EAAC;;QAE/F;QACA,MAAMK,WAAU,GAAIrB,aAAY,GAAI;QACpC,IAAIsB,eAAc,GAAI,IAAG;QACzB,IAAID,WAAU,GAAI,EAAE,EAAEC,eAAc,GAAI,MAAK,MACxC,IAAID,WAAU,GAAI,EAAE,EAAEC,eAAc,GAAI,MAAK,MAC7C,IAAID,WAAU,GAAI,EAAE,EAAEC,eAAc,GAAI,MAAK;;QAElD;QACA,MAAM7H,UAAS,GAAI;UACjBqB,MAAM,EAAE7B,aAAa,CAACkB,KAAK;UAC3BoH,WAAW,EAAE,IAAIhI,IAAI,CAAC,CAAC;UACvBiI,YAAY,EAAE,SAAS;UACvBzB,OAAO,EAAE;YACPC,aAAa,EAAEA,aAAa;YAC5ByB,eAAe,EAAE,CAAC;YAAE;YACpBjB,kBAAkB,EAAEA,kBAAkB;YACtCkB,UAAU,EAAE9C,IAAI,CAACwC,KAAK,CAAEpB,aAAY,GAAIpB,IAAI,CAACqC,GAAG,CAAC,CAAC,EAAET,kBAAiB,GAAI,EAAE,CAAC,GAAI,GAAG,IAAI,GAAG;YAC1FmB,aAAa,EAAEpB,UAAU,CAACR,OAAO,EAAE4B,aAAY,IAAK,IAAI;YACxDC,cAAc,EAAEZ,QAAO,GAAI,IAAIpC,IAAI,CAACwC,KAAK,CAAEJ,QAAO,GAAIhB,aAAa,GAAI,GAAG,IAAI,CAAC;YAC/EsB,eAAe,EAAEA,eAAe;YAChCO,eAAe,EAAE;UACnB,CAAC;UACDpB,UAAU,EAAEA,UAAU;UACtBqB,mBAAmB,EAAE;YACnBX,WAAW,EAAEA,WAAW;YACxBY,SAAS,EAAE,0BAA0B;YACrCC,SAAS,EAAEhC,aAAY,GAAI,EAAC,GAAI,MAAK,GAAI,IAAI;YAC7CiC,oBAAoB,EAAEX,eAAc,KAAM,IAAG,GAAI,KAAI,GAAI,MAAM;YAC/DY,kBAAkB,EAAE3B,UAAU,CAAC4B,eAAe,EAAED,kBAAiB,IAAK;cACpEE,gBAAgB,EAAE,GAAG;cACrBC,mBAAmB,EAAE;gBACnBC,IAAI,EAAE,EAAE;gBACRC,KAAK,EAAE,EAAE;gBACTC,IAAI,EAAE,EAAE;gBACRC,KAAK,EAAE;cACT,CAAC;cACDC,mBAAmB,EAAE;YACvB;UACF,CAAC;UACDC,eAAe,EAAE,CAACpC,UAAU,CAAC4B,eAAe,EAAEQ,eAAc,IAAK,CAC/D,eAAe,EACf,kBAAkB,EAClB,cAAa,CACd,EAAE9B,GAAG,CAAC,CAAC+B,GAAG,EAAEC,KAAK,KAAK;YACrB,IAAI,OAAOD,GAAE,KAAM,QAAQ,EAAE;cAC3B,OAAO;gBACLE,KAAK,EAAE,QAAQD,KAAI,GAAI,CAAC,EAAE;gBAC1BE,WAAW,EAAEH,GAAG;gBAChB7I,IAAI,EAAE,QAAQ;gBACdiJ,QAAQ,EAAEH,KAAI,KAAM,IAAI,MAAK,GAAIA,KAAI,KAAM,IAAI,QAAO,GAAI,KAAK;gBAC/DH,mBAAmB,EAAE;cACvB;YACF;YACA,OAAOE,GAAE;UACX,CAAC,CAAC;UACFK,gBAAgB,EAAE;YAChBC,QAAQ,EAAE,IAAI;YACdC,eAAe,EAAE,IAAI;YACrBC,SAAS,EAAE,IAAI;YACfC,aAAa,EAAE,IAAI;YACnBC,YAAY,EAAE,GAAG;YACjBC,WAAW,EAAE,IAAI;YACjBC,QAAQ,EAAE;UACZ;QACF;;QAEA;QACApD,wBAAwB,CAAC3G,UAAU;QAEnCuB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAExB,UAAU;QACnC,OAAOA,UAAS;MAElB,EAAE,OAAO4B,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,MAAMA,KAAI;MACZ;IACF;IAEA,MAAMoI,kBAAiB,GAAK3I,MAAM,IAAK;MACrCvD,SAAS,CAAC+E,OAAO,CAAC,QAAQ;IAC5B;IAEA,MAAMoH,uBAAsB,GAAK5I,MAAM,IAAK;MAC1CvD,SAAS,CAAC+E,OAAO,CAAC,UAAU;IAC9B;;IAEA;IACA,MAAMqH,UAAS,GAAIA,CAAA,KAAM;MACvB3K,WAAW,CAACmB,KAAI,GAAI;IACtB;IAEA,MAAMyJ,cAAa,GAAIA,CAAA,KAAM;MAC3B,IAAItJ,SAAS,CAACH,KAAK,EAAE;QACnBnB,WAAW,CAACmB,KAAI,GAAI;MACtB,OAAO;QACL5C,SAAS,CAAC6E,OAAO,CAAC,UAAU;MAC9B;IACF;IAEA,MAAMyH,gBAAe,GAAIA,CAAA,KAAM;MAC7B,IAAItJ,UAAU,CAACJ,KAAK,EAAE;QACpBnB,WAAW,CAACmB,KAAI,GAAI;MACtB,OAAO;QACL5C,SAAS,CAAC6E,OAAO,CAAC,UAAU;MAC9B;IACF;IAEA,MAAM0H,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAItJ,SAAS,CAACL,KAAK,EAAE;QACnBnB,WAAW,CAACmB,KAAI,GAAI;MACtB,OAAO;QACL5C,SAAS,CAAC6E,OAAO,CAAC,UAAU;MAC9B;IACF;IAEA,MAAM2H,aAAY,GAAIA,CAAA,KAAM;MAC1BpK,cAAc,CAACQ,KAAI,GAAI,IAAIZ,IAAI,CAAC,CAAC,CAACyK,kBAAkB,CAAC;MACrDzM,SAAS,CAAC+E,OAAO,CAAC,SAAS;IAC7B;IAEA,MAAM2H,cAAa,GAAIA,CAAA,KAAM;MAC3BzM,YAAY,CAAC0M,KAAK,CAAC,oBAAoB,EAAE,MAAM,EAAE;QAC/CpG,iBAAiB,EAAE;MACrB,CAAC;IACH;;IAEA;IACA,MAAMqG,gBAAe,GAAKC,SAAS,IAAK;MACtCpJ,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEmJ,SAAS;;MAEhC;MACA,IAAIA,SAAQ,IAAKpL,WAAW,CAACmB,KAAK,EAAE;QAClCnB,WAAW,CAACmB,KAAI,GAAIiK,SAAQ;QAC5B7M,SAAS,CAAC8E,IAAI,CAAC,UAAU+H,SAAQ,GAAI,CAAC,EAAE;MAC1C,OAAO;QACL7M,SAAS,CAAC6E,OAAO,CAAC,WAAW;MAC/B;IACF;IAEA,MAAMiI,iBAAgB,GAAKC,SAAS,IAAK;MACvCtJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEqJ,SAAS;MAE9B,QAAQA,SAAS;QACf,KAAK,QAAQ;UACXX,UAAU,CAAC;UACX;QACF,KAAK,WAAW;UACdC,cAAc,CAAC;UACf;QACF,KAAK,UAAU;UACbC,gBAAgB,CAAC;UACjB;QACF,KAAK,QAAQ;UACXC,YAAY,CAAC;UACb;QACF;UACE9I,OAAO,CAAC2C,IAAI,CAAC,UAAU,EAAE2G,SAAS;MACtC;IACF;;IAEA;IACA,MAAMC,iBAAgB,GAAI,MAAAA,CAAA,KAAY;MACpC,IAAI;QACFvJ,OAAO,CAACC,GAAG,CAAC,UAAU;;QAEtB;QACA,MAAMuJ,aAAY,GAAIC,WAAW,CAACC,UAAU,EAAE3K,IAAG,KAAM;QAEvD,IAAIyK,aAAa,EAAE;UACjBxJ,OAAO,CAACC,GAAG,CAAC,eAAe;UAC3B,MAAMC,oBAAoB,CAAC;UAC3B;QACF;;QAEA;QACA,MAAMyJ,WAAU,GAAIzF,kBAAkB,CAAC;QACvC,IAAIyF,WAAW,EAAE;UACf3J,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE0J,WAAW;UACvC1L,aAAa,CAACkB,KAAI,GAAIwK,WAAW,CAAC7J,MAAK;UACvC,MAAMK,yBAAyB,CAACwJ,WAAW,CAAC7J,MAAM;QACpD,OAAO;UACLE,OAAO,CAACC,GAAG,CAAC,qBAAqB;UACjC,MAAMC,oBAAoB,CAAC;QAC7B;MAEF,EAAE,OAAOG,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,MAAMH,oBAAoB,CAAC;MAC7B;IACF;IAEA,MAAM0J,mBAAkB,GAAI,MAAAA,CAAA,KAAY;MACtC,IAAI3L,aAAa,CAACkB,KAAK,EAAE;QACvB5C,SAAS,CAAC8E,IAAI,CAAC,aAAa;QAC5B,MAAMlB,yBAAyB,CAAClC,aAAa,CAACkB,KAAK;MACrD,OAAO;QACL5C,SAAS,CAAC6E,OAAO,CAAC,SAAS;MAC7B;IACF;;IAEA;IACA,MAAMyI,yBAAwB,GAAIA,CAAA,KAAM;MACtC,IAAI;QACF,MAAMhK,SAAQ,GAAI9B,KAAK,CAAC4B,KAAK,CAACG,MAAK;QACnC,MAAM6J,WAAU,GAAIzF,kBAAkB,CAAC;QACvC,MAAM4F,kBAAiB,GAAI7L,aAAa,CAACkB,KAAI;QAE7Ca,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;UACxBJ,SAAS;UACTkK,aAAa,EAAEJ,WAAW,EAAE7J,MAAM;UAClCgK;QACF,CAAC;;QAED;QACA,IAAIjK,SAAQ,IAAK8J,WAAU,IAAK9J,SAAQ,KAAM8J,WAAW,CAAC7J,MAAM,EAAE;UAChEE,OAAO,CAACC,GAAG,CAAC,mBAAmB;UAC/BwD,iBAAiB,CAAC5D,SAAS;UAC3B5B,aAAa,CAACkB,KAAI,GAAIU,SAAQ;QAChC;MAEF,EAAE,OAAOQ,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,cAAc,EAAEA,KAAK;MACrC;IACF;;IAEA;IACA,MAAM2J,iBAAgB,GAAK5L,MAAM,IAAK;MACpC,MAAM6L,SAAQ,GAAI;QAChB,SAAS,EAAE,MAAM;QACjB,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,SAAS;QACtB,QAAQ,EAAE,QAAQ;QAClB,QAAQ,EAAE;MACZ;MACA,OAAOA,SAAS,CAAC7L,MAAM,KAAK,MAAK;IACnC;IAEA,MAAM8L,iBAAgB,GAAK9L,MAAM,IAAK;MACpC,MAAM6L,SAAQ,GAAI;QAChB,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,KAAK;QAClB,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE;MACZ;MACA,OAAOA,SAAS,CAAC7L,MAAM,KAAK,IAAG;IACjC;IAEA,MAAM+L,iBAAgB,GAAK/L,MAAM,IAAK;MACpC,IAAIA,MAAK,KAAM,WAAW,EAAE,OAAO,SAAQ;MAC3C,IAAIA,MAAK,KAAM,QAAQ,EAAE,OAAO,WAAU;MAC1C,OAAO,IAAG;IACZ;IAEA,MAAMgM,UAAS,GAAKC,IAAI,IAAK;MAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,GAAE;MACpB,OAAO,IAAI9L,IAAI,CAAC8L,IAAI,CAAC,CAACC,cAAc,CAAC;IACvC;IAEA,MAAMC,qBAAoB,GAAKC,IAAI,IAAK;MACtC,IAAI,CAACA,IAAG,IAAK,CAACA,IAAI,CAAClM,SAAS,EAAE,OAAO,GAAE;MACvC,MAAMmM,KAAI,GAAI,IAAIlM,IAAI,CAACiM,IAAI,CAAClM,SAAS;MACrC,MAAMoM,GAAE,GAAIF,IAAI,CAAChM,SAAQ,GAAI,IAAID,IAAI,CAACiM,IAAI,CAAChM,SAAS,IAAI,IAAID,IAAI,CAAC;MACjE,MAAM0G,QAAO,GAAIrB,IAAI,CAAC+G,KAAK,CAAC,CAACD,GAAE,GAAID,KAAK,IAAI,IAAI;MAEhD,IAAIxF,QAAO,GAAI,EAAE,EAAE,OAAO,GAAGA,QAAQ,GAAE;MACvC,IAAIA,QAAO,GAAI,IAAI,EAAE,OAAO,GAAGrB,IAAI,CAAC+G,KAAK,CAAC1F,QAAO,GAAI,EAAE,CAAC,IAAG;MAC3D,OAAO,GAAGrB,IAAI,CAAC+G,KAAK,CAAC1F,QAAO,GAAI,IAAI,CAAC,IAAG;IAC1C;;IAEA;IACA7I,KAAK,CAAC,MAAM2B,KAAK,CAAC4B,KAAK,EAAE,CAACiL,QAAQ,EAAEC,QAAQ,KAAK;MAC/C,MAAMC,SAAQ,GAAIF,QAAQ,CAAC9K,MAAK;MAChC,MAAMiL,OAAM,GAAIH,QAAQ,CAAChL,IAAG;MAC5B,MAAMoL,SAAQ,GAAIH,QAAQ,EAAE/K,MAAK;MACjC,MAAMmL,OAAM,GAAIJ,QAAQ,EAAEjL,IAAG;MAE7BI,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QAAE6K,SAAS;QAAEC,OAAO;QAAEC,SAAS;QAAEC;MAAQ,CAAC;;MAEjE;MACA,IAAIF,OAAM,KAAME,OAAO,EAAE;QACvB,IAAIF,OAAM,KAAM,KAAK,EAAE;UACrB/K,OAAO,CAACC,GAAG,CAAC,YAAY;UACxBC,oBAAoB,CAAC;UACrB;QACF;MACF;;MAEA;MACA,IAAI4K,SAAQ,IAAKA,SAAQ,KAAM7M,aAAa,CAACkB,KAAK,EAAE;QAClDa,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE6K,SAAS;QAC1C7M,aAAa,CAACkB,KAAI,GAAI2L,SAAQ;QAC9B3K,yBAAyB,CAAC2K,SAAS;MACrC;IACF,CAAC,EAAE;MAAEI,IAAI,EAAE;IAAK,CAAC;;IAEjB;IACA,MAAMC,sBAAqB,GAAKrL,MAAM,IAAK;MACzC,OAAO,WAAWA,MAAM,IAAIvB,IAAI,CAACsD,GAAG,CAAC,CAAC,EAAC;IACzC;IAEA,MAAMuJ,uBAAsB,GAAIA,CAAC3I,GAAG,EAAE3C,MAAK,GAAI,IAAI,KAAK;MACtD,MAAMuL,OAAM,GAAIvL,MAAK,GAAI,GAAG2C,GAAG,IAAI3C,MAAM,EAAC,GAAI2C,GAAE;MAChD,OAAO,kBAAkB4I,OAAO,EAAC;IACnC;IAEA,MAAMC,oBAAmB,GAAIA,CAAC7I,GAAG,EAAEtD,KAAK,EAAEW,MAAK,GAAI,IAAI,KAAK;MAC1D,IAAI;QACF,MAAMyL,aAAY,GAAIH,uBAAuB,CAAC3I,GAAG,EAAE3C,MAAM;QACzD0B,cAAc,CAACwC,OAAO,CAACuH,aAAa,EAAE7J,IAAI,CAACuC,SAAS,CAAC9E,KAAK,CAAC;MAC7D,EAAE,OAAOkB,KAAK,EAAE;QACdL,OAAO,CAAC2C,IAAI,CAAC,aAAa,EAAEtC,KAAK;MACnC;IACF;IAEA,MAAMmL,oBAAmB,GAAIA,CAAC/I,GAAG,EAAE3C,MAAK,GAAI,IAAI,KAAK;MACnD,IAAI;QACF,MAAMyL,aAAY,GAAIH,uBAAuB,CAAC3I,GAAG,EAAE3C,MAAM;QACzD,MAAMX,KAAI,GAAIqC,cAAc,CAACf,OAAO,CAAC8K,aAAa;QAClD,OAAOpM,KAAI,GAAIuC,IAAI,CAACC,KAAK,CAACxC,KAAK,IAAI,IAAG;MACxC,EAAE,OAAOkB,KAAK,EAAE;QACdL,OAAO,CAAC2C,IAAI,CAAC,aAAa,EAAEtC,KAAK;QACjC,OAAO,IAAG;MACZ;IACF;IAEA,MAAMoL,sBAAqB,GAAIA,CAAC3L,MAAK,GAAI,IAAI,KAAK;MAChD,IAAI;QACF,MAAM4L,MAAK,GAAI5L,MAAK,GAAI,kBAAkBA,MAAM,EAAC,GAAI,iBAAgB;QACrE,MAAMyC,YAAW,GAAI,EAAC;QAEtB,KAAK,IAAIoJ,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAInK,cAAc,CAACoK,MAAM,EAAED,CAAC,EAAE,EAAE;UAC9C,MAAMlJ,GAAE,GAAIjB,cAAc,CAACiB,GAAG,CAACkJ,CAAC;UAChC,IAAIlJ,GAAE,IAAKA,GAAG,CAACoJ,UAAU,CAACH,MAAM,CAAC,EAAE;YACjCnJ,YAAY,CAACuJ,IAAI,CAACrJ,GAAG;UACvB;QACF;QAEAF,YAAY,CAACC,OAAO,CAACC,GAAE,IAAKjB,cAAc,CAACc,UAAU,CAACG,GAAG,CAAC;QAC1DzC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEsC,YAAY,CAACqJ,MAAM,EAAE,KAAK;MAEtD,EAAE,OAAOvL,KAAK,EAAE;QACdL,OAAO,CAAC2C,IAAI,CAAC,aAAa,EAAEtC,KAAK;MACnC;IACF;;IAEA;IACAnE,SAAS,CAAC,YAAY;MACpB;MACAyC,cAAc,CAACQ,KAAI,GAAI,IAAIZ,IAAI,CAAC,CAAC,CAACyK,kBAAkB,CAAC;MACrDtK,iBAAiB,CAACS,KAAI,GAAI;;MAE1B;MACA0K,yBAAyB,CAAC;;MAE1B;MACA,MAAMpK,mBAAmB,CAAC;IAC5B,CAAC;IAEDtD,WAAW,CAAC,MAAM;MAChB;MACA+F,qBAAqB,CAAC;IACxB,CAAC;IAED,OAAO;MACL;MACAxF,MAAM;MACNC,WAAW;MACXC,YAAY;MACZC,QAAQ;MACRC,IAAI;MACJC,UAAU;MACVC,OAAO;MACPC,UAAU;MAEV;MACAe,WAAW;MACXC,aAAa;MACbC,WAAW;MACXO,UAAU;MACVC,iBAAiB;MACjBC,cAAc;MACdG,YAAY;MACZF,cAAc;MAEd;MACAK,UAAU;MACVC,WAAW;MACXE,cAAc;MACdC,SAAS;MACTC,SAAS;MACTC,UAAU;MACVC,SAAS;MAET;MACAuM,gBAAgB,EAAEA,CAACzL,OAAO,EAAEvB,IAAG,GAAI,MAAM,EAAEkG,QAAO,GAAI,IAAI,KAAK;QAC7D1I,SAAS,CAAC;UACR+D,OAAO;UACPvB,IAAI;UACJkG,QAAQ;UACR9B,SAAS,EAAE;QACb,CAAC;MACH,CAAC;MAED6I,iBAAiB,EAAE,MAAAA,CAAO1L,OAAO,EAAEwH,KAAI,GAAI,MAAM,KAAK;QACpD,IAAI;UACF,MAAMtL,YAAY,CAACqG,OAAO,CAACvC,OAAO,EAAEwH,KAAK,EAAE;YACzChF,iBAAiB,EAAE,IAAI;YACvBC,gBAAgB,EAAE,IAAI;YACtBhE,IAAI,EAAE;UACR,CAAC;UACD,OAAO,IAAG;QACZ,EAAE,MAAM;UACN,OAAO,KAAI;QACb;MACF,CAAC;MAED;MACAU,mBAAmB;MACnBU,yBAAyB;MACzBc,mBAAmB;MACnBqC,eAAe;MACflD,iBAAiB;MACjBF,oBAAoB;MACpB+B,oBAAoB;MACpBD,uBAAuB;MACvByB,iBAAiB;MACjBS,kBAAkB;MAClBqF,iBAAiB;MACjBM,yBAAyB;MACzBsB,sBAAsB;MACtBC,uBAAuB;MACvBE,oBAAoB;MACpBE,oBAAoB;MACpBC,sBAAsB;MACtBtK,sBAAsB;MACtBe,qBAAqB;MACrBiC,mBAAmB;MACnBI,iBAAiB;MACjBC,oBAAoB;MACpBC,wBAAwB;MACxBC,qBAAqB;MACrBE,2BAA2B;MAC3BC,sBAAsB;MACtBO,wBAAwB;MACxBqD,kBAAkB;MAClBC,uBAAuB;MACvBC,UAAU;MACVC,cAAc;MACdC,gBAAgB;MAChBC,YAAY;MACZC,aAAa;MACbE,cAAc;MACdE,gBAAgB;MAChBE,iBAAiB;MACjBO,mBAAmB;MAEnB;MACAI,iBAAiB;MACjBE,iBAAiB;MACjBC,iBAAiB;MACjBC,UAAU;MACVG;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}