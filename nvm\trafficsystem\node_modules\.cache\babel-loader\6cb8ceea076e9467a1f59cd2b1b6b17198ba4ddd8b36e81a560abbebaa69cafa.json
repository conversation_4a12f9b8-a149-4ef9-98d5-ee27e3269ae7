{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, normalizeStyle as _normalizeStyle, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"video-report-panel\"\n};\nconst _hoisted_2 = {\n  class: \"optimization-section\"\n};\nconst _hoisted_3 = {\n  class: \"congestion-content\"\n};\nconst _hoisted_4 = {\n  class: \"congestion-level-container\"\n};\nconst _hoisted_5 = {\n  class: \"congestion-meter\"\n};\nconst _hoisted_6 = {\n  class: \"congestion-label\"\n};\nconst _hoisted_7 = {\n  class: \"congestion-details\"\n};\nconst _hoisted_8 = {\n  class: \"solution-content\"\n};\nconst _hoisted_9 = {\n  class: \"traffic-light-config\"\n};\nconst _hoisted_10 = {\n  class: \"light-direction\"\n};\nconst _hoisted_11 = {\n  class: \"light-timings\"\n};\nconst _hoisted_12 = {\n  class: \"light-item\"\n};\nconst _hoisted_13 = {\n  class: \"light-time\"\n};\nconst _hoisted_14 = {\n  class: \"light-item\"\n};\nconst _hoisted_15 = {\n  class: \"light-time\"\n};\nconst _hoisted_16 = {\n  class: \"light-direction\"\n};\nconst _hoisted_17 = {\n  class: \"light-timings\"\n};\nconst _hoisted_18 = {\n  class: \"light-item\"\n};\nconst _hoisted_19 = {\n  class: \"light-time\"\n};\nconst _hoisted_20 = {\n  class: \"light-item\"\n};\nconst _hoisted_21 = {\n  class: \"light-time\"\n};\nconst _hoisted_22 = {\n  class: \"suggestion-content\"\n};\nconst _hoisted_23 = {\n  class: \"suggestion-details\"\n};\nconst _hoisted_24 = {\n  class: \"suggestion-list\"\n};\nconst _hoisted_25 = {\n  class: \"report-section\"\n};\nconst _hoisted_26 = {\n  class: \"report-header\"\n};\nconst _hoisted_27 = {\n  class: \"report-actions\"\n};\nconst _hoisted_28 = {\n  class: \"report-container\"\n};\nconst _hoisted_29 = [\"src\"];\nconst _hoisted_30 = {\n  key: 1,\n  class: \"no-report\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_collapse_item = _resolveComponent(\"el-collapse-item\");\n  const _component_el_collapse = _resolveComponent(\"el-collapse\");\n  const _component_document = _resolveComponent(\"document\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_tooltip = _resolveComponent(\"el-tooltip\");\n  const _component_warning = _resolveComponent(\"warning\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[19] || (_cache[19] = _createElementVNode(\"h3\", null, \"交通拥堵解决方案\", -1 /* HOISTED */)), _createCommentVNode(\" 拥堵状态卡片 \"), _createVNode(_component_el_card, {\n    class: \"congestion-card\"\n  }, {\n    header: _withCtx(() => _cache[3] || (_cache[3] = [_createElementVNode(\"div\", {\n      class: \"card-header\"\n    }, [_createElementVNode(\"span\", null, \"当前拥堵状态评估\")], -1 /* HOISTED */)])),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", {\n      class: \"meter-indicator\",\n      style: _normalizeStyle({\n        left: $setup.getCongestionPercentage + '%'\n      })\n    }, null, 4 /* STYLE */), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n      class: \"meter-scale\"\n    }, [_createElementVNode(\"span\", null, \"不拥挤\"), _createElementVNode(\"span\", null, \"一般\"), _createElementVNode(\"span\", null, \"较拥挤\"), _createElementVNode(\"span\", null, \"拥挤\")], -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.getCongestionLevel), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"p\", null, [_cache[5] || (_cache[5] = _createTextVNode(\"当前检测到 \")), _createElementVNode(\"strong\", null, _toDisplayString($setup.vehicleCount), 1 /* TEXT */), _cache[6] || (_cache[6] = _createTextVNode(\" 辆车\"))]), _createElementVNode(\"p\", null, [_cache[7] || (_cache[7] = _createTextVNode(\"建议采用: \")), _createElementVNode(\"strong\", null, _toDisplayString($setup.getSolutionTitle), 1 /* TEXT */)])])])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 交通方案建议 \"), _createVNode(_component_el_collapse, {\n    modelValue: $setup.activeNames,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.activeNames = $event),\n    class: \"mt-4\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_collapse_item, {\n      title: \"交通信号灯优化方案\",\n      name: \"1\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [_cache[16] || (_cache[16] = _createElementVNode(\"p\", null, \"基于当前交通流量分析，推荐以下信号灯配置：\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_row, {\n        gutter: 20\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_col, {\n          span: 12\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_10, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n            class: \"direction-label\"\n          }, \"东西方向\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n            class: \"light green\"\n          }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_13, _toDisplayString($setup.getEastWestGreenTime) + \"秒\", 1 /* TEXT */)]), _cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n            class: \"light-item\"\n          }, [_createElementVNode(\"div\", {\n            class: \"light yellow\"\n          }), _createElementVNode(\"div\", {\n            class: \"light-time\"\n          }, \"3秒\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n            class: \"light red\"\n          }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_15, _toDisplayString($setup.getEastWestRedTime) + \"秒\", 1 /* TEXT */)])])])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_col, {\n          span: 12\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_16, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n            class: \"direction-label\"\n          }, \"南北方向\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n            class: \"light green\"\n          }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_19, _toDisplayString($setup.getNorthSouthGreenTime) + \"秒\", 1 /* TEXT */)]), _cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n            class: \"light-item\"\n          }, [_createElementVNode(\"div\", {\n            class: \"light yellow\"\n          }), _createElementVNode(\"div\", {\n            class: \"light-time\"\n          }, \"3秒\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_20, [_cache[13] || (_cache[13] = _createElementVNode(\"div\", {\n            class: \"light red\"\n          }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_21, _toDisplayString($setup.getNorthSouthRedTime) + \"秒\", 1 /* TEXT */)])])])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })])])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_collapse_item, {\n      title: \"具体优化建议\",\n      name: \"2\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"p\", null, _toDisplayString($setup.getSolutionDescription), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_23, [_cache[17] || (_cache[17] = _createElementVNode(\"h4\", null, \"详细建议：\", -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_24, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.getDetailedSuggestions, (suggestion, index) => {\n        return _openBlock(), _createElementBlock(\"li\", {\n          key: index\n        }, _toDisplayString(suggestion), 1 /* TEXT */);\n      }), 128 /* KEYED_FRAGMENT */))])])])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_collapse_item, {\n      title: \"长期改进措施\",\n      name: \"3\"\n    }, {\n      default: _withCtx(() => _cache[18] || (_cache[18] = [_createElementVNode(\"div\", {\n        class: \"suggestion-content\"\n      }, [_createElementVNode(\"p\", null, \"除了短期交通信号调整外，还建议考虑以下长期改进措施：\"), _createElementVNode(\"ul\", {\n        class: \"suggestion-list\"\n      }, [_createElementVNode(\"li\", null, \"优化道路设计，增加车道数量或调整车道分配\"), _createElementVNode(\"li\", null, \"安装智能交通系统，实时监控和调整交通信号\"), _createElementVNode(\"li\", null, \"实施交通需求管理策略，如错峰出行和公共交通优先\"), _createElementVNode(\"li\", null, \"在交通高峰期增加交通引导人员\"), _createElementVNode(\"li\", null, \"建设更完善的公共交通网络，减少私家车使用\")])], -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_25, [_createVNode(_component_el_row, {\n    gutter: 20\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 24\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_26, [_cache[21] || (_cache[21] = _createElementVNode(\"h3\", null, \"完整分析报告\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_27, [_createVNode(_component_el_tooltip, {\n        content: \"导出PDF报告\",\n        placement: \"top\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.exportPDF,\n          loading: $setup.exporting\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_document)]),\n            _: 1 /* STABLE */\n          }), _cache[20] || (_cache[20] = _createTextVNode(\" 导出PDF \"))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n        _: 1 /* STABLE */\n      })])])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_28, [$props.reportUrl ? (_openBlock(), _createElementBlock(\"iframe\", {\n    key: 0,\n    src: $setup.getFixedReportUrl($props.reportUrl),\n    onLoad: _cache[1] || (_cache[1] = (...args) => $setup.iframeLoaded && $setup.iframeLoaded(...args)),\n    onError: _cache[2] || (_cache[2] = (...args) => $setup.handleIframeError && $setup.handleIframeError(...args)),\n    ref: \"reportIframe\",\n    frameborder: \"0\",\n    sandbox: \"allow-same-origin allow-scripts allow-forms\",\n    style: {\n      \"width\": \"100%\",\n      \"height\": \"500px\"\n    }\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_29)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_30, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_warning)]),\n    _: 1 /* STABLE */\n  }), _cache[23] || (_cache[23] = _createElementVNode(\"p\", null, \"报告生成中或无法加载\", -1 /* HOISTED */)), _createVNode(_component_el_button, {\n    type: \"primary\",\n    size: \"small\",\n    onClick: $setup.generateReport,\n    loading: $setup.generating\n  }, {\n    default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\" 生成报告 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"loading\"])]))])), [[_directive_loading, $setup.iframeLoading]])])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createCommentVNode", "_createVNode", "_component_el_card", "header", "_withCtx", "_cache", "default", "_hoisted_3", "_hoisted_4", "_hoisted_5", "style", "_normalizeStyle", "left", "$setup", "getCongestionPercentage", "_hoisted_6", "_toDisplayString", "getCongestionLevel", "_hoisted_7", "_createTextVNode", "vehicleCount", "getSolutionTitle", "_", "_component_el_collapse", "modelValue", "activeNames", "$event", "_component_el_collapse_item", "title", "name", "_hoisted_8", "_hoisted_9", "_component_el_row", "gutter", "_component_el_col", "span", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "getEastWestGreenTime", "_hoisted_14", "_hoisted_15", "getEastWestRedTime", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "getNorthSouthGreenTime", "_hoisted_20", "_hoisted_21", "getNorthSouthRedTime", "_hoisted_22", "getSolutionDescription", "_hoisted_23", "_hoisted_24", "_Fragment", "_renderList", "getDetailedSuggestions", "suggestion", "index", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_component_el_tooltip", "content", "placement", "_component_el_button", "type", "onClick", "exportPDF", "loading", "exporting", "_component_el_icon", "_component_document", "_hoisted_28", "$props", "reportUrl", "src", "getFixedReportUrl", "onLoad", "args", "iframeLoaded", "onError", "handleIframeError", "ref", "frameborder", "sandbox", "_hoisted_29", "_hoisted_30", "_component_warning", "size", "generateReport", "generating", "iframeLoading"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\video\\VideoReportPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"video-report-panel\">\n    <div class=\"optimization-section\">\n      <h3>交通拥堵解决方案</h3>\n      \n      <!-- 拥堵状态卡片 -->\n      <el-card class=\"congestion-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <span>当前拥堵状态评估</span>\n          </div>\n        </template>\n        <div class=\"congestion-content\">\n          <div class=\"congestion-level-container\">\n            <div class=\"congestion-meter\">\n              <div class=\"meter-indicator\" :style=\"{ left: getCongestionPercentage + '%' }\"></div>\n              <div class=\"meter-scale\">\n                <span>不拥挤</span>\n                <span>一般</span>\n                <span>较拥挤</span>\n                <span>拥挤</span>\n              </div>\n            </div>\n            <div class=\"congestion-label\">\n              {{ getCongestionLevel }}\n            </div>\n          </div>\n          <div class=\"congestion-details\">\n            <p>当前检测到 <strong>{{ vehicleCount }}</strong> 辆车</p>\n            <p>建议采用: <strong>{{ getSolutionTitle }}</strong></p>\n          </div>\n        </div>\n      </el-card>\n      \n      <!-- 交通方案建议 -->\n      <el-collapse v-model=\"activeNames\" class=\"mt-4\">\n        <el-collapse-item title=\"交通信号灯优化方案\" name=\"1\">\n          <div class=\"solution-content\">\n            <p>基于当前交通流量分析，推荐以下信号灯配置：</p>\n            <div class=\"traffic-light-config\">\n              <el-row :gutter=\"20\">\n                <el-col :span=\"12\">\n                  <div class=\"light-direction\">\n                    <div class=\"direction-label\">东西方向</div>\n                    <div class=\"light-timings\">\n                      <div class=\"light-item\">\n                        <div class=\"light green\"></div>\n                        <div class=\"light-time\">{{ getEastWestGreenTime }}秒</div>\n                      </div>\n                      <div class=\"light-item\">\n                        <div class=\"light yellow\"></div>\n                        <div class=\"light-time\">3秒</div>\n                      </div>\n                      <div class=\"light-item\">\n                        <div class=\"light red\"></div>\n                        <div class=\"light-time\">{{ getEastWestRedTime }}秒</div>\n                      </div>\n                    </div>\n                  </div>\n                </el-col>\n                <el-col :span=\"12\">\n                  <div class=\"light-direction\">\n                    <div class=\"direction-label\">南北方向</div>\n                    <div class=\"light-timings\">\n                      <div class=\"light-item\">\n                        <div class=\"light green\"></div>\n                        <div class=\"light-time\">{{ getNorthSouthGreenTime }}秒</div>\n                      </div>\n                      <div class=\"light-item\">\n                        <div class=\"light yellow\"></div>\n                        <div class=\"light-time\">3秒</div>\n                      </div>\n                      <div class=\"light-item\">\n                        <div class=\"light red\"></div>\n                        <div class=\"light-time\">{{ getNorthSouthRedTime }}秒</div>\n                      </div>\n                    </div>\n                  </div>\n                </el-col>\n              </el-row>\n            </div>\n          </div>\n        </el-collapse-item>\n        \n        <el-collapse-item title=\"具体优化建议\" name=\"2\">\n          <div class=\"suggestion-content\">\n            <p>{{ getSolutionDescription }}</p>\n            <div class=\"suggestion-details\">\n              <h4>详细建议：</h4>\n              <ul class=\"suggestion-list\">\n                <li v-for=\"(suggestion, index) in getDetailedSuggestions\" :key=\"index\">\n                  {{ suggestion }}\n              </li>\n            </ul>\n            </div>\n          </div>\n        </el-collapse-item>\n        \n        <el-collapse-item title=\"长期改进措施\" name=\"3\">\n          <div class=\"suggestion-content\">\n            <p>除了短期交通信号调整外，还建议考虑以下长期改进措施：</p>\n            <ul class=\"suggestion-list\">\n              <li>优化道路设计，增加车道数量或调整车道分配</li>\n              <li>安装智能交通系统，实时监控和调整交通信号</li>\n              <li>实施交通需求管理策略，如错峰出行和公共交通优先</li>\n              <li>在交通高峰期增加交通引导人员</li>\n              <li>建设更完善的公共交通网络，减少私家车使用</li>\n            </ul>\n          </div>\n        </el-collapse-item>\n      </el-collapse>\n    </div>\n    \n    <div class=\"report-section\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"24\">\n          <div class=\"report-header\">\n            <h3>完整分析报告</h3>\n            <div class=\"report-actions\">\n              <el-tooltip content=\"导出PDF报告\" placement=\"top\">\n                <el-button type=\"primary\" @click=\"exportPDF\" :loading=\"exporting\">\n                  <el-icon><document /></el-icon> 导出PDF\n                </el-button>\n              </el-tooltip>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n      \n      <div class=\"report-container\" v-loading=\"iframeLoading\">\n        <iframe \n          v-if=\"reportUrl\" \n          :src=\"getFixedReportUrl(reportUrl)\" \n          @load=\"iframeLoaded\"\n          @error=\"handleIframeError\"\n          ref=\"reportIframe\"\n          frameborder=\"0\" \n          sandbox=\"allow-same-origin allow-scripts allow-forms\"\n          style=\"width:100%; height:500px;\"\n        ></iframe>\n        <div v-else class=\"no-report\">\n          <el-icon><warning /></el-icon>\n          <p>报告生成中或无法加载</p>\n          <el-button type=\"primary\" size=\"small\" @click=\"generateReport\" :loading=\"generating\">\n            生成报告\n          </el-button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport html2pdf from 'html2pdf.js';\nimport { Document, Warning } from '@element-plus/icons-vue';\nimport { exportPdfReport } from '@/api/video';\n\nexport default {\n  name: 'VideoReportPanel',\n  components: {\n    Document,\n    Warning\n  },\n  props: {\n    // 分析结果数据\n    result: {\n      type: Object,\n      default: () => ({})\n    },\n    // 报告的URL\n    reportUrl: {\n      type: String,\n      default: ''\n    },\n    // 视频路径（用于报告名称）\n    videoPath: {\n      type: String,\n      default: ''\n    }\n  },\n  setup(props, { emit }) {\n    const activeNames = ref(['1', '2']); // 默认展开前两个建议\n    const iframeLoading = ref(true);\n    const exporting = ref(false);\n    const generating = ref(false);\n    const actualReportUrl = ref('');\n    \n    // 获取车辆数量\n    const vehicleCount = computed(() => props.result?.vehicle_count || 0);\n    \n    // 计算拥堵百分比位置\n    const getCongestionPercentage = computed(() => {\n      const count = vehicleCount.value;\n      if (count <= 5) return 12.5; // 不拥挤，位于第一档中间\n      if (count <= 10) return 37.5; // 一般，位于第二档中间\n      if (count <= 20) return 62.5; // 较拥挤，位于第三档中间\n      return 87.5; // 拥挤，位于第四档中间\n    });\n    \n    // 获取拥堵等级\n    const getCongestionLevel = computed(() => {\n      const count = vehicleCount.value;\n      if (count <= 5) return '不拥挤';\n      if (count <= 10) return '一般';\n      if (count <= 20) return '较拥挤';\n      return '拥挤';\n    });\n    \n    // 获取解决方案标题\n    const getSolutionTitle = computed(() => {\n      const count = vehicleCount.value;\n      if (count <= 5) return '方案一：正常红绿灯交换';\n      if (count <= 10) return '方案二：延长横向绿灯时间';\n      if (count <= 20) return '方案三：延长纵向绿灯时间';\n      return '方案四：发出提醒（需人为干预）';\n    });\n    \n    // 获取解决方案描述\n    const getSolutionDescription = computed(() => {\n      const count = vehicleCount.value;\n      if (count <= 5) return '当前车流量正常，可维持默认信号灯配置。各方向交通流量均衡，无需特殊干预。';\n      if (count <= 10) return '当前车流量略大，建议延长东西方向绿灯时间，确保车辆顺畅通行。';\n      if (count <= 20) return '当前车流量较大，建议延长南北方向绿灯时间，减少车辆积压。';\n      return '当前车流量大，可能出现拥堵，建议启动交通应急预案，必要时派遣交警现场指挥。';\n    });\n    \n    // 获取详细建议\n    const getDetailedSuggestions = computed(() => {\n      const count = vehicleCount.value;\n      const direction = props.result?.direction || 'horizontal';\n      \n      if (count <= 5) {\n        return [\n          '维持当前信号灯配置，绿灯时间保持在30秒',\n          '保持交通监控系统正常运行',\n          '定期检查道路标志和标线的清晰度'\n        ];\n      } else if (count <= 10) {\n        const suggestions = [\n          '将横向方向绿灯时间延长至45秒',\n          '纵向方向绿灯时间可适当缩短至25秒',\n          '加强对驾驶员的提醒，避免不必要的车道变换'\n        ];\n        \n        if (direction === 'horizontal') {\n          suggestions.push('重点关注东西方向的车流量变化');\n        }\n        \n        return suggestions;\n      } else if (count <= 20) {\n        const suggestions = [\n          '将纵向方向绿灯时间延长至45秒',\n          '横向方向绿灯时间可适当缩短至25秒',\n          '开启辅助车道，增加通行能力',\n          '考虑实施临时交通管制措施'\n        ];\n        \n        if (direction === 'vertical') {\n          suggestions.push('重点关注南北方向的车流量变化');\n        }\n        \n        return suggestions;\n      } else {\n        return [\n          '建议派遣交警到现场指挥交通',\n          '启动交通应急预案，必要时实施单向通行',\n          '向驾驶员发布交通提醒，建议选择替代路线',\n          '临时关闭部分入口，减少车辆进入',\n          '协调相邻路口信号灯，形成绿波带'\n        ];\n      }\n    });\n    \n    // 计算东西向绿灯时间\n    const getEastWestGreenTime = computed(() => {\n      const count = vehicleCount.value;\n      const direction = props.result?.direction || 'horizontal';\n      \n      if (count <= 5) return 30;\n      if (count <= 10) return direction === 'horizontal' ? 45 : 25;\n      if (count <= 20) return direction === 'horizontal' ? 25 : 20;\n      return 20; // 拥挤状态下，适当减少东西向绿灯时间\n    });\n    \n    // 计算南北向绿灯时间\n    const getNorthSouthGreenTime = computed(() => {\n      const count = vehicleCount.value;\n      const direction = props.result?.direction || 'horizontal';\n      \n      if (count <= 5) return 30;\n      if (count <= 10) return direction === 'vertical' ? 40 : 25;\n      if (count <= 20) return direction === 'vertical' ? 45 : 30;\n      return 25; // 拥挤状态下，适当减少南北向绿灯时间\n    });\n    \n    // 计算东西向红灯时间\n    const getEastWestRedTime = computed(() => {\n      return getNorthSouthGreenTime.value + 3; // 绿灯时间 + 黄灯时间\n    });\n    \n    // 计算南北向红灯时间\n    const getNorthSouthRedTime = computed(() => {\n      return getEastWestGreenTime.value + 3; // 绿灯时间 + 黄灯时间\n    });\n    \n    // 尝试修复报告URL\n    const getFixedReportUrl = (url) => {\n      if (!url) return '';\n      \n      console.log('修复报告URL:', url);\n      \n      // 如果已经是完整URL，直接返回\n      if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('/api/')) {\n        return url;\n      }\n      \n      // 如果是TaskID格式（可能是24位十六进制字符串），构建规范的API URL\n      if (/^[0-9a-f]{24}$/i.test(url)) {\n        return `/api/video-analysis/${url}/report`;\n      }\n      \n      // 如果是相对路径，添加API前缀\n      if (url.startsWith('/')) {\n        return `/api${url}`;\n      }\n      \n      // 其他情况，使用标准报告URL格式\n      // 如果有taskId属性，优先使用\n      if (props.result && props.result.taskId) {\n        return `/api/video-analysis/${props.result.taskId}/report`;\n      }\n      \n      // 最后尝试构建一个合理的URL\n      return `/api/static/reports/${url}`;\n    };\n    \n    // iframe加载完成\n    const iframeLoaded = () => {\n      console.log('iframe加载完成，URL:', props.reportUrl);\n      try {\n        const iframe = document.querySelector('.report-container iframe');\n        if (iframe) {\n          // 检查iframe的内容是否加载成功\n          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;\n          if (iframeDoc) {\n            console.log('iframe文档加载成功，标题:', iframeDoc.title);\n            if (iframeDoc.body.innerHTML === '') {\n              console.warn('iframe内容为空，可能加载失败');\n              // 内容为空时也触发错误处理\n              handleIframeError(new Error('iframe内容为空'));\n              return;\n            }\n          } else {\n            console.warn('无法访问iframe内容，可能是跨域问题');\n          }\n        }\n      } catch (err) {\n        console.error('iframe检查失败:', err);\n        handleIframeError(err);\n      }\n      iframeLoading.value = false;\n    };\n    \n    // 处理iframe加载错误\n    const handleIframeError = (error) => {\n      console.error('iframe加载失败:', error);\n      iframeLoading.value = false;\n      ElMessage.warning({\n        message: '报告加载失败，是否在新窗口打开？',\n        duration: 5000,\n        showClose: true,\n        type: 'warning',\n        onClose: () => {}\n      });\n      \n      // 提供在新窗口打开的选项\n      ElMessageBox.confirm(\n        '报告在框架中加载失败，是否在新窗口打开查看？',\n        '报告加载失败',\n        {\n          confirmButtonText: '在新窗口打开',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }\n      ).then(() => {\n        const url = getFixedReportUrl(props.reportUrl);\n        window.open(url, '_blank');\n      }).catch(() => {\n        // 用户取消，不做任何操作\n      });\n    };\n    \n    // 从视频路径中提取文件名作为报告名称\n    const getReportName = () => {\n      if (!props.videoPath) return '交通分析报告';\n      \n      const pathParts = props.videoPath.split('/');\n      const fileName = pathParts[pathParts.length - 1];\n      const nameWithoutExt = fileName.split('.')[0];\n      \n      return `${nameWithoutExt}_交通分析报告`;\n    };\n    \n    // 导出PDF报告\n    const exportPDF = async () => {\n      if (exporting.value) {\n        return;\n      }\n      \n      exporting.value = true;\n      \n      try {\n        // 检查是否有iframe\n        const iframe = document.querySelector('.report-container iframe');\n        if (!iframe && props.result?.taskId) {\n          // 尝试通过API导出\n          console.log('尝试通过API导出PDF报告');\n          \n          try {\n            const response = await exportPdfReport(props.result.taskId);\n            \n            // 处理Blob响应\n            const blob = new Blob([response.data], { type: 'application/pdf' });\n            const url = URL.createObjectURL(blob);\n            \n            // 创建下载链接\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `${getReportName()}.pdf`;\n            document.body.appendChild(link);\n            link.click();\n            \n            // 清理资源\n            setTimeout(() => {\n              URL.revokeObjectURL(url);\n              document.body.removeChild(link);\n            }, 100);\n            \n            ElMessage.success('PDF报告已导出');\n          } catch (error) {\n            console.error('API导出PDF失败:', error);\n            \n            // 处理HTML响应或认证错误\n            if (error.htmlResponse) {\n              ElMessage.error('导出失败：服务器返回了HTML而不是PDF (可能是认证问题)');\n              \n              // 询问用户是否重新登录\n              ElMessageBox.confirm(\n                '您的登录会话可能已过期。是否重新登录？',\n                '认证错误',\n                {\n                  confirmButtonText: '重新登录',\n                  cancelButtonText: '取消',\n                  type: 'warning'\n                }\n              ).then(() => {\n                // 清除认证信息并跳转到登录页面\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('user');\n                window.location.href = '/login';\n              }).catch(() => {});\n              return;\n            }\n            \n            throw new Error(error.message || '通过API导出PDF失败');\n          }\n          return;\n        }\n        \n        if (!iframe) {\n          throw new Error('找不到报告iframe');\n        }\n        \n        // 尝试访问iframe内容\n        let iframeContent;\n        try {\n          iframeContent = iframe.contentDocument || (iframe.contentWindow && iframe.contentWindow.document);\n          \n          // 检查iframe是否加载了HTML内容\n          if (iframeContent && iframeContent.body.innerHTML === '') {\n            throw new Error('iframe内容为空，可能是因为报告未生成或加载失败');\n          }\n          \n          // 检查iframe内容是否为错误页面\n          if (iframeContent && iframeContent.title && \n              (iframeContent.title.includes('Error') || \n               iframeContent.title.includes('错误') ||\n               iframeContent.title.includes('Not Found'))) {\n            throw new Error(`报告加载失败: ${iframeContent.title}`);\n          }\n        } catch (error) {\n          console.error('无法访问iframe内容（可能是跨域问题）:', error);\n          throw new Error('无法访问报告内容，可能是由于跨域限制或报告未生成');\n        }\n        \n        if (!iframeContent) {\n          throw new Error('无法访问iframe内容');\n        }\n        \n        // 创建一个新的容器，复制iframe内容\n        const element = iframeContent.body.cloneNode(true);\n        \n        // 添加样式以确保正确打印\n        const styles = document.createElement('style');\n        styles.innerHTML = `\n          body {\n            font-family: Arial, sans-serif;\n            color: #333;\n            background-color: white;\n          }\n          h1, h2, h3 {\n            color: #1a1a1a;\n          }\n          .chart-container {\n            page-break-inside: avoid;\n            margin-bottom: 20px;\n          }\n        `;\n        element.appendChild(styles);\n        \n        // 使用html2pdf库进行转换\n        const opt = {\n          margin: [10, 10, 10, 10],\n          filename: `${getReportName()}.pdf`,\n          image: { type: 'jpeg', quality: 0.98 },\n          html2canvas: { scale: 2, useCORS: true, logging: false },\n          jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }\n        };\n        \n        await html2pdf().set(opt).from(element).save();\n        ElMessage.success('PDF报告已导出');\n      } catch (err) {\n        console.error('导出PDF失败:', err);\n        ElMessage.error('导出PDF失败: ' + err.message);\n        \n        // 如果客户端导出失败，尝试使用服务器端导出\n        if (props.result?.taskId) {\n          ElMessage.info('正在尝试从服务器导出...');\n          try {\n            const response = await exportPdfReport(props.result.taskId);\n            \n            // 处理Blob响应\n            const blob = new Blob([response.data], { type: 'application/pdf' });\n            const url = URL.createObjectURL(blob);\n            \n            // 创建下载链接\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `${getReportName()}.pdf`;\n            document.body.appendChild(link);\n            link.click();\n            \n            // 清理资源\n            setTimeout(() => {\n              URL.revokeObjectURL(url);\n              document.body.removeChild(link);\n            }, 100);\n            \n            ElMessage.success('PDF报告已从服务器导出');\n          } catch (apiErr) {\n            console.error('服务器导出PDF失败:', apiErr);\n            \n            // 检查是否是认证错误\n            if (apiErr.htmlResponse) {\n              ElMessage.error('认证失败，请重新登录后再试');\n              // 询问是否重新登录\n              ElMessageBox.confirm(\n                '您的登录会话可能已过期，是否重新登录？',\n                '认证错误',\n                {\n                  confirmButtonText: '重新登录',\n                  cancelButtonText: '取消',\n                  type: 'warning'\n                }\n              ).then(() => {\n                // 清除认证信息并跳转到登录页面\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('user');\n                window.location.href = '/login';\n              }).catch(() => {});\n            } else {\n              ElMessage.error('服务器导出PDF也失败: ' + (apiErr.message || '未知错误'));\n            }\n          }\n        }\n      } finally {\n        exporting.value = false;\n      }\n    };\n    \n    // 生成报告\n    const generateReport = async () => {\n      generating.value = true;\n      try {\n        // 检查是否有任务ID\n        if (!props.result?.taskId) {\n          throw new Error('缺少任务ID，无法生成报告');\n        }\n        \n        // 这里应该调用实际的报告生成API\n        // 为了演示，我们模拟一个API调用\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        \n        console.log('向服务器发送报告生成请求，任务ID:', props.result.taskId);\n        \n        // 假设报告生成成功\n        ElMessage.success('报告生成请求已发送，请稍后查看');\n        \n        // 这里应该刷新页面或重新加载报告URL\n        // 如果后端有实时通知功能更好\n      } catch (err) {\n        console.error('生成报告失败:', err);\n        ElMessage.error('生成报告失败: ' + err.message);\n      } finally {\n        generating.value = false;\n      }\n    };\n    \n    // 监听属性变化\n    onMounted(() => {\n      console.log('VideoReportPanel组件已挂载，reportUrl:', props.reportUrl);\n      if (props.reportUrl) {\n        actualReportUrl.value = getFixedReportUrl(props.reportUrl);\n        console.log('处理后的报告URL:', actualReportUrl.value);\n      } else {\n        console.log('无reportUrl提供');\n      }\n    });\n    \n    return {\n      activeNames,\n      vehicleCount,\n      getCongestionPercentage,\n      getCongestionLevel,\n      getSolutionTitle,\n      getSolutionDescription,\n      getDetailedSuggestions,\n      getEastWestGreenTime,\n      getNorthSouthGreenTime,\n      getEastWestRedTime,\n      getNorthSouthRedTime,\n      iframeLoading,\n      exporting,\n      generating,\n      iframeLoaded,\n      handleIframeError,\n      exportPDF,\n      generateReport,\n      actualReportUrl,\n      getFixedReportUrl\n    };\n  }\n};\n</script>\n\n<style scoped>\n.video-report-panel {\n  width: 100%;\n}\n\n.optimization-section,\n.report-section {\n  margin: 20px 0;\n}\n\nh3 {\n  color: #ffffff !important;\n  font-weight: 600 !important;\n  margin-bottom: 15px;\n}\n\nh4 {\n  color: #e5e7eb !important;\n  font-weight: 600 !important;\n  margin: 15px 0 10px 0;\n}\n\n.congestion-card {\n  margin-bottom: 20px;\n  background: rgba(255, 255, 255, 0.05) !important;\n  border: 1px solid rgba(255, 255, 255, 0.06) !important;\n}\n\n:deep(.congestion-card .el-card__header) {\n  background-color: rgba(26, 32, 50, 0.8);\n}\n\n.card-header {\n  font-weight: bold;\n  color: #ffffff;\n}\n\n.congestion-content {\n  padding: 20px 0;\n}\n\n.congestion-level-container {\n  text-align: center;\n  margin-bottom: 20px;\n}\n\n.congestion-meter {\n  height: 20px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  position: relative;\n  margin: 0 auto 15px;\n  width: 80%;\n}\n\n.meter-indicator {\n  width: 20px;\n  height: 20px;\n  background: #6366f1;\n  border-radius: 50%;\n  position: absolute;\n  top: 0;\n  transform: translateX(-50%);\n  box-shadow: 0 0 10px rgba(99, 102, 241, 0.7);\n}\n\n.meter-scale {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 5px;\n  color: #d1d5db;\n  font-size: 0.85rem;\n}\n\n.congestion-label {\n  font-size: 24px;\n  font-weight: 700;\n  color: #6366f1;\n  margin: 15px 0;\n}\n\n.congestion-details {\n  text-align: center;\n  color: #e5e7eb;\n}\n\n.congestion-details p {\n  margin: 5px 0;\n}\n\n.congestion-details strong {\n  color: #ffffff;\n  font-weight: 600;\n}\n\n.mt-4 {\n  margin-top: 1rem;\n}\n\n.suggestion-content {\n  color: #e5e7eb;\n  padding: 10px;\n}\n\n.suggestion-list {\n  margin-top: 10px;\n  padding-left: 20px;\n}\n\n.suggestion-list li {\n  margin-bottom: 8px;\n  position: relative;\n}\n\n.suggestion-list li::before {\n  content: '•';\n  color: #6366f1;\n  font-weight: bold;\n  position: absolute;\n  left: -15px;\n}\n\n.report-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.report-container {\n  background: rgba(17, 24, 39, 0.5);\n  border-radius: 8px;\n  padding: 15px;\n  height: 530px;\n}\n\n.no-report {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  color: #909399;\n}\n\n.no-report .el-icon {\n  font-size: 48px;\n  margin-bottom: 20px;\n}\n\n:deep(.el-collapse) {\n  --el-collapse-header-bg-color: rgba(17, 24, 39, 0.5);\n  --el-collapse-header-text-color: #e5e7eb;\n  --el-collapse-content-bg-color: rgba(31, 41, 55, 0.5);\n  --el-collapse-content-text-color: #e5e7eb;\n  border-radius: 8px;\n  overflow: hidden;\n  border: none;\n}\n\n:deep(.el-collapse-item__header) {\n  font-weight: 600;\n}\n\n:deep(.el-collapse-item__wrap) {\n  border-bottom: none;\n}\n\n/* 信号灯配置样式 */\n.traffic-light-config {\n  margin-top: 15px;\n}\n\n.light-direction {\n  background: rgba(17, 24, 39, 0.7);\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 10px;\n}\n\n.direction-label {\n  text-align: center;\n  font-weight: 600;\n  color: #ffffff;\n  margin-bottom: 15px;\n  font-size: 16px;\n}\n\n.light-timings {\n  display: flex;\n  justify-content: space-around;\n}\n\n.light-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.light {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  margin-bottom: 8px;\n}\n\n.light.red {\n  background-color: #ef4444;\n  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);\n}\n\n.light.yellow {\n  background-color: #f59e0b;\n  box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);\n}\n\n.light.green {\n  background-color: #10b981;\n  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);\n}\n\n.light-time {\n  font-size: 14px;\n  color: #d1d5db;\n}\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAsB;;EAUxBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAkB;;EASxBA,KAAK,EAAC;AAAkB;;EAI1BA,KAAK,EAAC;AAAoB;;EAU1BA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAsB;;EAGtBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAY;;EAMpBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAY;;EAMxBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAY;;EAMpBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAY;;EAWhCA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAoB;;EAEzBA,KAAK,EAAC;AAAiB;;EAwBhCA,KAAK,EAAC;AAAgB;;EAGhBA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAgB;;EAW5BA,KAAK,EAAC;AAAkB;oBAjInC;;EAAAC,GAAA;EA4IoBD,KAAK,EAAC;;;;;;;;;;;;;;uBA3IxBE,mBAAA,CAoJM,OApJNC,UAoJM,GAnJJC,mBAAA,CA6GM,OA7GNC,UA6GM,G,4BA5GJD,mBAAA,CAAiB,YAAb,UAAQ,sBAEZE,mBAAA,YAAe,EACfC,YAAA,CA0BUC,kBAAA;IA1BDR,KAAK,EAAC;EAAiB;IACnBS,MAAM,EAAAC,QAAA,CACf,MAEMC,MAAA,QAAAA,MAAA,OAFNP,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAa,IACtBI,mBAAA,CAAqB,cAAf,UAAQ,E;IAT1BQ,OAAA,EAAAF,QAAA,CAYQ,MAmBM,CAnBNN,mBAAA,CAmBM,OAnBNS,UAmBM,GAlBJT,mBAAA,CAaM,OAbNU,UAaM,GAZJV,mBAAA,CAQM,OARNW,UAQM,GAPJX,mBAAA,CAAoF;MAA/EJ,KAAK,EAAC,iBAAiB;MAAEgB,KAAK,EAfjDC,eAAA;QAAAC,IAAA,EAe2DC,MAAA,CAAAC,uBAAuB;MAAA;uDACpEhB,mBAAA,CAKM;MALDJ,KAAK,EAAC;IAAa,IACtBI,mBAAA,CAAgB,cAAV,KAAG,GACTA,mBAAA,CAAe,cAAT,IAAE,GACRA,mBAAA,CAAgB,cAAV,KAAG,GACTA,mBAAA,CAAe,cAAT,IAAE,E,wBAGZA,mBAAA,CAEM,OAFNiB,UAEM,EAAAC,gBAAA,CADDH,MAAA,CAAAI,kBAAkB,iB,GAGzBnB,mBAAA,CAGM,OAHNoB,UAGM,GAFJpB,mBAAA,CAAmD,Y,0BA5B/DqB,gBAAA,CA4Be,QAAM,IAAArB,mBAAA,CAAmC,gBAAAkB,gBAAA,CAAxBH,MAAA,CAAAO,YAAY,kB,0BA5B5CD,gBAAA,CA4BwD,KAAG,G,GAC/CrB,mBAAA,CAAoD,Y,0BA7BhEqB,gBAAA,CA6Be,QAAM,IAAArB,mBAAA,CAAuC,gBAAAkB,gBAAA,CAA5BH,MAAA,CAAAQ,gBAAgB,iB;IA7BhDC,CAAA;MAkCMtB,mBAAA,YAAe,EACfC,YAAA,CA2EcsB,sBAAA;IA9GpBC,UAAA,EAmC4BX,MAAA,CAAAY,WAAW;IAnCvC,uBAAApB,MAAA,QAAAA,MAAA,MAAAqB,MAAA,IAmC4Bb,MAAA,CAAAY,WAAW,GAAAC,MAAA;IAAEhC,KAAK,EAAC;;IAnC/CY,OAAA,EAAAF,QAAA,CAoCQ,MA8CmB,CA9CnBH,YAAA,CA8CmB0B,2BAAA;MA9CDC,KAAK,EAAC,WAAW;MAACC,IAAI,EAAC;;MApCjDvB,OAAA,EAAAF,QAAA,CAqCU,MA4CM,CA5CNN,mBAAA,CA4CM,OA5CNgC,UA4CM,G,4BA3CJhC,mBAAA,CAA4B,WAAzB,uBAAqB,sBACxBA,mBAAA,CAyCM,OAzCNiC,UAyCM,GAxCJ9B,YAAA,CAuCS+B,iBAAA;QAvCAC,MAAM,EAAE;MAAE;QAxCjC3B,OAAA,EAAAF,QAAA,CAyCgB,MAkBS,CAlBTH,YAAA,CAkBSiC,iBAAA;UAlBAC,IAAI,EAAE;QAAE;UAzCjC7B,OAAA,EAAAF,QAAA,CA0CkB,MAgBM,CAhBNN,mBAAA,CAgBM,OAhBNsC,WAgBM,G,4BAfJtC,mBAAA,CAAuC;YAAlCJ,KAAK,EAAC;UAAiB,GAAC,MAAI,sBACjCI,mBAAA,CAaM,OAbNuC,WAaM,GAZJvC,mBAAA,CAGM,OAHNwC,WAGM,G,0BAFJxC,mBAAA,CAA+B;YAA1BJ,KAAK,EAAC;UAAa,6BACxBI,mBAAA,CAAyD,OAAzDyC,WAAyD,EAAAvB,gBAAA,CAA9BH,MAAA,CAAA2B,oBAAoB,IAAG,GAAC,gB,+BAErD1C,mBAAA,CAGM;YAHDJ,KAAK,EAAC;UAAY,IACrBI,mBAAA,CAAgC;YAA3BJ,KAAK,EAAC;UAAc,IACzBI,mBAAA,CAAgC;YAA3BJ,KAAK,EAAC;UAAY,GAAC,IAAE,E,sBAE5BI,mBAAA,CAGM,OAHN2C,WAGM,G,0BAFJ3C,mBAAA,CAA6B;YAAxBJ,KAAK,EAAC;UAAW,6BACtBI,mBAAA,CAAuD,OAAvD4C,WAAuD,EAAA1B,gBAAA,CAA5BH,MAAA,CAAA8B,kBAAkB,IAAG,GAAC,gB;UAvDzErB,CAAA;YA4DgBrB,YAAA,CAkBSiC,iBAAA;UAlBAC,IAAI,EAAE;QAAE;UA5DjC7B,OAAA,EAAAF,QAAA,CA6DkB,MAgBM,CAhBNN,mBAAA,CAgBM,OAhBN8C,WAgBM,G,4BAfJ9C,mBAAA,CAAuC;YAAlCJ,KAAK,EAAC;UAAiB,GAAC,MAAI,sBACjCI,mBAAA,CAaM,OAbN+C,WAaM,GAZJ/C,mBAAA,CAGM,OAHNgD,WAGM,G,4BAFJhD,mBAAA,CAA+B;YAA1BJ,KAAK,EAAC;UAAa,6BACxBI,mBAAA,CAA2D,OAA3DiD,WAA2D,EAAA/B,gBAAA,CAAhCH,MAAA,CAAAmC,sBAAsB,IAAG,GAAC,gB,+BAEvDlD,mBAAA,CAGM;YAHDJ,KAAK,EAAC;UAAY,IACrBI,mBAAA,CAAgC;YAA3BJ,KAAK,EAAC;UAAc,IACzBI,mBAAA,CAAgC;YAA3BJ,KAAK,EAAC;UAAY,GAAC,IAAE,E,sBAE5BI,mBAAA,CAGM,OAHNmD,WAGM,G,4BAFJnD,mBAAA,CAA6B;YAAxBJ,KAAK,EAAC;UAAW,6BACtBI,mBAAA,CAAyD,OAAzDoD,WAAyD,EAAAlC,gBAAA,CAA9BH,MAAA,CAAAsC,oBAAoB,IAAG,GAAC,gB;UA1E3E7B,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAoFQrB,YAAA,CAYmB0B,2BAAA;MAZDC,KAAK,EAAC,QAAQ;MAACC,IAAI,EAAC;;MApF9CvB,OAAA,EAAAF,QAAA,CAqFU,MAUM,CAVNN,mBAAA,CAUM,OAVNsD,WAUM,GATJtD,mBAAA,CAAmC,WAAAkB,gBAAA,CAA7BH,MAAA,CAAAwC,sBAAsB,kBAC5BvD,mBAAA,CAOM,OAPNwD,WAOM,G,4BANJxD,mBAAA,CAAc,YAAV,OAAK,sBACTA,mBAAA,CAIG,MAJHyD,WAIG,I,kBAHD3D,mBAAA,CAEG4D,SAAA,QA5FnBC,WAAA,CA0FkD5C,MAAA,CAAA6C,sBAAsB,EA1FxE,CA0F4BC,UAAU,EAAEC,KAAK;6BAA7BhE,mBAAA,CAEG;UAFwDD,GAAG,EAAEiE;QAAK,GAAA5C,gBAAA,CAChE2C,UAAU;;MA3F/BrC,CAAA;QAkGQrB,YAAA,CAWmB0B,2BAAA;MAXDC,KAAK,EAAC,QAAQ;MAACC,IAAI,EAAC;;MAlG9CvB,OAAA,EAAAF,QAAA,CAmGU,MASMC,MAAA,SAAAA,MAAA,QATNP,mBAAA,CASM;QATDJ,KAAK,EAAC;MAAoB,IAC7BI,mBAAA,CAAiC,WAA9B,4BAA0B,GAC7BA,mBAAA,CAMK;QANDJ,KAAK,EAAC;MAAiB,IACzBI,mBAAA,CAA6B,YAAzB,sBAAoB,GACxBA,mBAAA,CAA6B,YAAzB,sBAAoB,GACxBA,mBAAA,CAAgC,YAA5B,yBAAuB,GAC3BA,mBAAA,CAAuB,YAAnB,gBAAc,GAClBA,mBAAA,CAA6B,YAAzB,sBAAoB,E;MA1GtCwB,CAAA;;IAAAA,CAAA;uCAiHIxB,mBAAA,CAmCM,OAnCN+D,WAmCM,GAlCJ5D,YAAA,CAaS+B,iBAAA;IAbAC,MAAM,EAAE;EAAE;IAlHzB3B,OAAA,EAAAF,QAAA,CAmHQ,MAWS,CAXTH,YAAA,CAWSiC,iBAAA;MAXAC,IAAI,EAAE;IAAE;MAnHzB7B,OAAA,EAAAF,QAAA,CAoHU,MASM,CATNN,mBAAA,CASM,OATNgE,WASM,G,4BARJhE,mBAAA,CAAe,YAAX,QAAM,sBACVA,mBAAA,CAMM,OANNiE,WAMM,GALJ9D,YAAA,CAIa+D,qBAAA;QAJDC,OAAO,EAAC,SAAS;QAACC,SAAS,EAAC;;QAvHtD5D,OAAA,EAAAF,QAAA,CAwHgB,MAEY,CAFZH,YAAA,CAEYkE,oBAAA;UAFDC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAExD,MAAA,CAAAyD,SAAS;UAAGC,OAAO,EAAE1D,MAAA,CAAA2D;;UAxHvElE,OAAA,EAAAF,QAAA,CAyHkB,MAA+B,CAA/BH,YAAA,CAA+BwE,kBAAA;YAzHjDnE,OAAA,EAAAF,QAAA,CAyH2B,MAAY,CAAZH,YAAA,CAAYyE,mBAAA,E;YAzHvCpD,CAAA;0CAAAH,gBAAA,CAyHiD,SACjC,G;UA1HhBG,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;qCAiIM1B,mBAAA,CAkBM,OAlBN+E,WAkBM,GAhBIC,MAAA,CAAAC,SAAS,I,cADjBjF,mBAAA,CASU;IA3IlBD,GAAA;IAoIWmF,GAAG,EAAEjE,MAAA,CAAAkE,iBAAiB,CAACH,MAAA,CAAAC,SAAS;IAChCG,MAAI,EAAA3E,MAAA,QAAAA,MAAA,UAAA4E,IAAA,KAAEpE,MAAA,CAAAqE,YAAA,IAAArE,MAAA,CAAAqE,YAAA,IAAAD,IAAA,CAAY;IAClBE,OAAK,EAAA9E,MAAA,QAAAA,MAAA,UAAA4E,IAAA,KAAEpE,MAAA,CAAAuE,iBAAA,IAAAvE,MAAA,CAAAuE,iBAAA,IAAAH,IAAA,CAAiB;IACzBI,GAAG,EAAC,cAAc;IAClBC,WAAW,EAAC,GAAG;IACfC,OAAO,EAAC,6CAA6C;IACrD7E,KAAiC,EAAjC;MAAA;MAAA;IAAA;2CA1IV8E,WAAA,M,cA4IQ5F,mBAAA,CAMM,OANN6F,WAMM,GALJxF,YAAA,CAA8BwE,kBAAA;IA7IxCnE,OAAA,EAAAF,QAAA,CA6ImB,MAAW,CAAXH,YAAA,CAAWyF,kBAAA,E;IA7I9BpE,CAAA;kCA8IUxB,mBAAA,CAAiB,WAAd,YAAU,sBACbG,YAAA,CAEYkE,oBAAA;IAFDC,IAAI,EAAC,SAAS;IAACuB,IAAI,EAAC,OAAO;IAAEtB,OAAK,EAAExD,MAAA,CAAA+E,cAAc;IAAGrB,OAAO,EAAE1D,MAAA,CAAAgF;;IA/InFvF,OAAA,EAAAF,QAAA,CA+I+F,MAErFC,MAAA,SAAAA,MAAA,QAjJVc,gBAAA,CA+I+F,QAErF,E;IAjJVG,CAAA;yEAiI+CT,MAAA,CAAAiF,aAAa,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}