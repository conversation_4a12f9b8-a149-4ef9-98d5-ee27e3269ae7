{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"traffic-trend-chart\"\n};\nconst _hoisted_2 = {\n  class: \"chart-header\"\n};\nconst _hoisted_3 = {\n  class: \"chart-container\"\n};\nconst _hoisted_4 = {\n  ref: \"chartRef\",\n  class: \"chart\"\n};\nconst _hoisted_5 = {\n  class: \"moving-averages\"\n};\nconst _hoisted_6 = {\n  class: \"average-item\"\n};\nconst _hoisted_7 = {\n  class: \"average-value\"\n};\nconst _hoisted_8 = {\n  class: \"average-item\"\n};\nconst _hoisted_9 = {\n  class: \"average-value\"\n};\nconst _hoisted_10 = {\n  class: \"average-item\"\n};\nconst _hoisted_11 = {\n  class: \"average-value\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[0] || (_cache[0] = _createElementVNode(\"h4\", null, \"车流趋势分析\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"trend-indicator\", $setup.trendClass])\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent($setup.trendIcon)))]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"span\", null, _toDisplayString($props.trendInfo.description), 1 /* TEXT */)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, null, 512 /* NEED_PATCH */)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[1] || (_cache[1] = _createElementVNode(\"span\", {\n    class: \"average-label\"\n  }, \"5帧平均:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_7, _toDisplayString($props.movingAverages.frame5), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_8, [_cache[2] || (_cache[2] = _createElementVNode(\"span\", {\n    class: \"average-label\"\n  }, \"10帧平均:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_9, _toDisplayString($props.movingAverages.frame10), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_10, [_cache[3] || (_cache[3] = _createElementVNode(\"span\", {\n    class: \"average-label\"\n  }, \"30帧平均:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_11, _toDisplayString($props.movingAverages.frame30), 1 /* TEXT */)])])]);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_normalizeClass", "$setup", "trendClass", "_createVNode", "_component_el_icon", "default", "_withCtx", "_createBlock", "_resolveDynamicComponent", "trendIcon", "_", "_toDisplayString", "$props", "trendInfo", "description", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "movingAverages", "frame5", "_hoisted_8", "_hoisted_9", "frame10", "_hoisted_10", "_hoisted_11", "frame30"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\traffic\\TrafficTrendChart.vue"], "sourcesContent": ["<template>\n  <div class=\"traffic-trend-chart\">\n    <div class=\"chart-header\">\n      <h4>车流趋势分析</h4>\n      <div class=\"trend-indicator\" :class=\"trendClass\">\n        <el-icon>\n          <component :is=\"trendIcon\" />\n        </el-icon>\n        <span>{{ trendInfo.description }}</span>\n      </div>\n    </div>\n    \n    <div class=\"chart-container\">\n      <div ref=\"chartRef\" class=\"chart\"></div>\n    </div>\n    \n    <div class=\"moving-averages\">\n      <div class=\"average-item\">\n        <span class=\"average-label\">5帧平均:</span>\n        <span class=\"average-value\">{{ movingAverages.frame5 }}</span>\n      </div>\n      <div class=\"average-item\">\n        <span class=\"average-label\">10帧平均:</span>\n        <span class=\"average-value\">{{ movingAverages.frame10 }}</span>\n      </div>\n      <div class=\"average-item\">\n        <span class=\"average-label\">30帧平均:</span>\n        <span class=\"average-value\">{{ movingAverages.frame30 }}</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, watch, onMounted, onUnmounted } from 'vue'\nimport * as echarts from 'echarts'\nimport { TrendCharts, ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'TrafficTrendChart',\n  components: {\n    TrendCharts,\n    ArrowUp,\n    ArrowDown,\n    Minus\n  },\n  props: {\n    vehicleData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    movingAverages: {\n      type: Object,\n      required: true,\n      default: () => ({ frame5: 0, frame10: 0, frame30: 0 })\n    },\n    trendInfo: {\n      type: Object,\n      required: true,\n      default: () => ({ trend: 'stable', strength: 0, description: '车流稳定' })\n    }\n  },\n  setup(props) {\n    const chartRef = ref(null)\n    let chartInstance = null\n    \n    const trendClass = computed(() => `trend-${props.trendInfo.trend}`)\n    \n    const trendIcon = computed(() => {\n      switch (props.trendInfo.trend) {\n        case 'rising': return ArrowUp\n        case 'falling': return ArrowDown\n        default: return Minus\n      }\n    })\n    \n    const initChart = () => {\n      if (!chartRef.value) return\n      \n      chartInstance = echarts.init(chartRef.value)\n      updateChart()\n    }\n    \n    const updateChart = () => {\n      if (!chartInstance) return\n      \n      const data = props.vehicleData.slice(-20) // 显示最近20个数据点\n      const xAxisData = data.map((_, index) => `帧${index + 1}`)\n      \n      const option = {\n        grid: {\n          top: 20,\n          right: 20,\n          bottom: 40,\n          left: 40\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData,\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: { color: '#e8e8e8' }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '车辆数',\n          nameTextStyle: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: { color: '#e8e8e8' }\n          },\n          splitLine: {\n            lineStyle: { color: '#f0f0f0' }\n          }\n        },\n        series: [\n          {\n            name: '实时车辆数',\n            type: 'line',\n            data: data,\n            smooth: true,\n            lineStyle: {\n              color: '#1890ff',\n              width: 2\n            },\n            itemStyle: {\n              color: '#1890ff'\n            },\n            areaStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },\n                  { offset: 1, color: 'rgba(24, 144, 255, 0.05)' }\n                ]\n              }\n            },\n            symbol: 'circle',\n            symbolSize: 4\n          },\n          {\n            name: '5帧平均',\n            type: 'line',\n            data: new Array(data.length).fill(props.movingAverages.frame5),\n            lineStyle: {\n              color: '#52c41a',\n              width: 1,\n              type: 'dashed'\n            },\n            symbol: 'none'\n          },\n          {\n            name: '10帧平均',\n            type: 'line',\n            data: new Array(data.length).fill(props.movingAverages.frame10),\n            lineStyle: {\n              color: '#faad14',\n              width: 1,\n              type: 'dashed'\n            },\n            symbol: 'none'\n          }\n        ],\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          textStyle: { color: '#fff', fontSize: 12 },\n          formatter: (params) => {\n            let result = `${params[0].axisValue}<br/>`\n            params.forEach(param => {\n              result += `${param.seriesName}: ${param.value}辆<br/>`\n            })\n            return result\n          }\n        }\n      }\n      \n      chartInstance.setOption(option)\n    }\n    \n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize()\n      }\n    }\n    \n    watch(() => [props.vehicleData, props.movingAverages], updateChart, { deep: true })\n    \n    onMounted(() => {\n      initChart()\n      window.addEventListener('resize', resizeChart)\n    })\n    \n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose()\n      }\n      window.removeEventListener('resize', resizeChart)\n    })\n    \n    return {\n      chartRef,\n      trendClass,\n      trendIcon\n    }\n  }\n}\n</script>\n\n<style scoped>\n.traffic-trend-chart {\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e8e8e8;\n  overflow: hidden;\n}\n\n.chart-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.chart-header h4 {\n  margin: 0;\n  font-size: 16px;\n  color: #333;\n}\n\n.trend-indicator {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 4px 8px;\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.trend-rising {\n  background: #f6ffed;\n  color: #52c41a;\n}\n\n.trend-falling {\n  background: #fff2e8;\n  color: #fa8c16;\n}\n\n.trend-stable {\n  background: #e6f7ff;\n  color: #1890ff;\n}\n\n.chart-container {\n  padding: 20px;\n}\n\n.chart {\n  width: 100%;\n  height: 200px;\n}\n\n.moving-averages {\n  display: flex;\n  justify-content: space-around;\n  padding: 16px 20px;\n  background: #fafafa;\n  border-top: 1px solid #e8e8e8;\n}\n\n.average-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 4px;\n}\n\n.average-label {\n  font-size: 12px;\n  color: #666;\n}\n\n.average-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .chart-header {\n    flex-direction: column;\n    gap: 8px;\n    align-items: flex-start;\n  }\n  \n  .chart {\n    height: 150px;\n  }\n  \n  .moving-averages {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .average-item {\n    flex-direction: row;\n    justify-content: space-between;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAc;;EAUpBA,KAAK,EAAC;AAAiB;;EACrBC,GAAG,EAAC,UAAU;EAACD,KAAK,EAAC;;;EAGvBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAe;;EAExBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAe;;EAExBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAe;;;uBA1BjCE,mBAAA,CA6BM,OA7BNC,UA6BM,GA5BJC,mBAAA,CAQM,OARNC,UAQM,G,0BAPJD,mBAAA,CAAe,YAAX,QAAM,sBACVA,mBAAA,CAKM;IALDJ,KAAK,EAJhBM,eAAA,EAIiB,iBAAiB,EAASC,MAAA,CAAAC,UAAU;MAC7CC,YAAA,CAEUC,kBAAA;IAPlBC,OAAA,EAAAC,QAAA,CAMU,MAA6B,E,cAA7BC,YAAA,CAA6BC,wBANvC,CAM0BP,MAAA,CAAAQ,SAAS,I;IANnCC,CAAA;MAQQZ,mBAAA,CAAwC,cAAAa,gBAAA,CAA/BC,MAAA,CAAAC,SAAS,CAACC,WAAW,iB,oBAIlChB,mBAAA,CAEM,OAFNiB,UAEM,GADJjB,mBAAA,CAAwC,OAAxCkB,UAAwC,8B,GAG1ClB,mBAAA,CAaM,OAbNmB,UAaM,GAZJnB,mBAAA,CAGM,OAHNoB,UAGM,G,0BAFJpB,mBAAA,CAAwC;IAAlCJ,KAAK,EAAC;EAAe,GAAC,OAAK,sBACjCI,mBAAA,CAA8D,QAA9DqB,UAA8D,EAAAR,gBAAA,CAA/BC,MAAA,CAAAQ,cAAc,CAACC,MAAM,iB,GAEtDvB,mBAAA,CAGM,OAHNwB,UAGM,G,0BAFJxB,mBAAA,CAAyC;IAAnCJ,KAAK,EAAC;EAAe,GAAC,QAAM,sBAClCI,mBAAA,CAA+D,QAA/DyB,UAA+D,EAAAZ,gBAAA,CAAhCC,MAAA,CAAAQ,cAAc,CAACI,OAAO,iB,GAEvD1B,mBAAA,CAGM,OAHN2B,WAGM,G,0BAFJ3B,mBAAA,CAAyC;IAAnCJ,KAAK,EAAC;EAAe,GAAC,QAAM,sBAClCI,mBAAA,CAA+D,QAA/D4B,WAA+D,EAAAf,gBAAA,CAAhCC,MAAA,CAAAQ,cAAc,CAACO,OAAO,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}