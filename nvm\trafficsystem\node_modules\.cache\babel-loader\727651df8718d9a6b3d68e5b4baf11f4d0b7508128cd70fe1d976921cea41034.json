{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, Fragment as _Fragment, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"four-way-analysis-console\"\n};\nconst _hoisted_2 = {\n  class: \"console-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-content\"\n};\nconst _hoisted_4 = {\n  class: \"console-title\"\n};\nconst _hoisted_5 = {\n  class: \"header-stats\"\n};\nconst _hoisted_6 = {\n  class: \"stat-item\"\n};\nconst _hoisted_7 = {\n  class: \"stat-value\"\n};\nconst _hoisted_8 = {\n  class: \"stat-item\"\n};\nconst _hoisted_9 = {\n  class: \"stat-value\"\n};\nconst _hoisted_10 = {\n  class: \"stat-item\"\n};\nconst _hoisted_11 = {\n  class: \"stat-value\"\n};\nconst _hoisted_12 = {\n  class: \"workflow-navigation\"\n};\nconst _hoisted_13 = {\n  class: \"console-content\"\n};\nconst _hoisted_14 = {\n  class: \"left-panel\"\n};\nconst _hoisted_15 = {\n  class: \"main-content\"\n};\nconst _hoisted_16 = {\n  key: 0,\n  class: \"current-task-info\"\n};\nconst _hoisted_17 = {\n  class: \"task-header\"\n};\nconst _hoisted_18 = {\n  class: \"task-title-section\"\n};\nconst _hoisted_19 = {\n  class: \"task-progress-section\"\n};\nconst _hoisted_20 = {\n  class: \"progress-text\"\n};\nconst _hoisted_21 = {\n  class: \"task-details\"\n};\nconst _hoisted_22 = {\n  class: \"detail-item\"\n};\nconst _hoisted_23 = {\n  class: \"detail-value\"\n};\nconst _hoisted_24 = {\n  class: \"detail-item\"\n};\nconst _hoisted_25 = {\n  class: \"detail-value\"\n};\nconst _hoisted_26 = {\n  class: \"detail-item\"\n};\nconst _hoisted_27 = {\n  class: \"detail-value\"\n};\nconst _hoisted_28 = {\n  class: \"dynamic-content\"\n};\nconst _hoisted_29 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_30 = {\n  class: \"loading-content\"\n};\nconst _hoisted_31 = {\n  class: \"loading-text\"\n};\nconst _hoisted_32 = {\n  class: \"step-content\"\n};\nconst _hoisted_33 = {\n  key: 2,\n  class: \"step-content\"\n};\nconst _hoisted_34 = {\n  key: 3,\n  class: \"step-content\"\n};\nconst _hoisted_35 = {\n  key: 4,\n  class: \"step-content\"\n};\nconst _hoisted_36 = {\n  class: \"console-footer\"\n};\nconst _hoisted_37 = {\n  class: \"footer-info\"\n};\nconst _hoisted_38 = {\n  class: \"footer-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Grid = _resolveComponent(\"Grid\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_step = _resolveComponent(\"el-step\");\n  const _component_el_steps = _resolveComponent(\"el-steps\");\n  const _component_StepNavigator = _resolveComponent(\"StepNavigator\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_skeleton_item = _resolveComponent(\"el-skeleton-item\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_Loading = _resolveComponent(\"Loading\");\n  const _component_FourWayVideoUpload = _resolveComponent(\"FourWayVideoUpload\");\n  const _component_FourWayRealtimeViewer = _resolveComponent(\"FourWayRealtimeViewer\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_TrafficAnalysisDashboard = _resolveComponent(\"TrafficAnalysisDashboard\");\n  const _component_IntelligentTrafficReport = _resolveComponent(\"IntelligentTrafficReport\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_InfoFilled = _resolveComponent(\"InfoFilled\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 页面头部 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"h1\", _hoisted_4, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Grid)]),\n    _: 1 /* STABLE */\n  }), _cache[3] || (_cache[3] = _createTextVNode(\" 四方向智能交通分析控制台 \"))]), _cache[4] || (_cache[4] = _createElementVNode(\"p\", {\n    class: \"console-description\"\n  }, \" 集成视频上传、实时检测、智能分析和报告生成的一站式交通分析平台 \", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString($setup.totalTasks), 1 /* TEXT */), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"总任务数\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.activeTasks), 1 /* TEXT */), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"活跃任务\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString($setup.completedTasks), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"已完成\", -1 /* HOISTED */))])])]), _createCommentVNode(\" 工作流程导航 \"), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_steps, {\n    active: $setup.currentStep,\n    \"align-center\": \"\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_step, {\n      title: \"视频上传\",\n      description: \"上传四方向视频文件\",\n      icon: \"Upload\"\n    }), _createVNode(_component_el_step, {\n      title: \"实时检测\",\n      description: \"AI模型实时分析\",\n      icon: \"VideoCamera\"\n    }), _createVNode(_component_el_step, {\n      title: \"智能分析\",\n      description: \"生成分析结果\",\n      icon: \"DataAnalysis\"\n    }), _createVNode(_component_el_step, {\n      title: \"报告生成\",\n      description: \"导出分析报告\",\n      icon: \"Document\"\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"active\"])]), _createCommentVNode(\" 主要内容区域 \"), _createElementVNode(\"div\", _hoisted_13, [_createCommentVNode(\" 左侧面板 \"), _createElementVNode(\"div\", _hoisted_14, [_createCommentVNode(\" 步骤导航器 \"), _createVNode(_component_StepNavigator, {\n    \"current-step\": $setup.currentStep,\n    \"task-status\": $setup.currentTask.status,\n    \"task-progress\": $setup.currentTask.progress,\n    loading: $setup.isInitializing,\n    \"allow-navigation\": true,\n    onStepChange: $setup.handleStepChange,\n    onAction: $setup.handleQuickAction,\n    onRefresh: $setup.handleRefreshStatus\n  }, null, 8 /* PROPS */, [\"current-step\", \"task-status\", \"task-progress\", \"loading\", \"onStepChange\", \"onAction\", \"onRefresh\"])]), _createCommentVNode(\" 右侧主内容 \"), _createElementVNode(\"div\", _hoisted_15, [_createCommentVNode(\" 当前任务信息 \"), $setup.currentTask ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"h3\", null, _toDisplayString($setup.currentTask.name), 1 /* TEXT */), _createVNode(_component_el_tag, {\n      type: $setup.getTaskStatusType($setup.currentTask.status)\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getTaskStatusText($setup.currentTask.status)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"div\", _hoisted_19, [_createVNode(_component_el_progress, {\n      percentage: $setup.currentTask.progress,\n      status: $setup.getProgressStatus($setup.currentTask.status),\n      \"stroke-width\": 8\n    }, null, 8 /* PROPS */, [\"percentage\", \"status\"]), _createElementVNode(\"span\", _hoisted_20, _toDisplayString($setup.currentTask.progress) + \"%\", 1 /* TEXT */)])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_22, [_cache[8] || (_cache[8] = _createElementVNode(\"span\", {\n          class: \"detail-label\"\n        }, \"任务ID:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_23, _toDisplayString($setup.currentTask.id), 1 /* TEXT */)])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_24, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", {\n          class: \"detail-label\"\n        }, \"创建时间:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_25, _toDisplayString($setup.formatTime($setup.currentTask.createdAt)), 1 /* TEXT */)])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_26, [_cache[10] || (_cache[10] = _createElementVNode(\"span\", {\n          class: \"detail-label\"\n        }, \"处理时长:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_27, _toDisplayString($setup.getProcessingDuration($setup.currentTask)), 1 /* TEXT */)])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])]),\n    _: 1 /* STABLE */\n  })])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 动态内容区域 \"), _createElementVNode(\"div\", _hoisted_28, [_createCommentVNode(\" 初始化加载状态 \"), $setup.isInitializing ? (_openBlock(), _createElementBlock(\"div\", _hoisted_29, [_createVNode(_component_el_skeleton, {\n    rows: 8,\n    animated: \"\"\n  }, {\n    template: _withCtx(() => [_createElementVNode(\"div\", _hoisted_30, [_createVNode(_component_el_skeleton_item, {\n      variant: \"h1\",\n      style: {\n        \"width\": \"40%\",\n        \"margin-bottom\": \"20px\"\n      }\n    }), _createVNode(_component_el_skeleton_item, {\n      variant: \"text\",\n      style: {\n        \"width\": \"80%\",\n        \"margin-bottom\": \"10px\"\n      }\n    }), _createVNode(_component_el_skeleton_item, {\n      variant: \"text\",\n      style: {\n        \"width\": \"60%\",\n        \"margin-bottom\": \"20px\"\n      }\n    }), _createVNode(_component_el_skeleton_item, {\n      variant: \"rect\",\n      style: {\n        \"width\": \"100%\",\n        \"height\": \"200px\"\n      }\n    })])]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_31, [_createVNode(_component_el_icon, {\n    class: \"loading-icon\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_Loading)]),\n    _: 1 /* STABLE */\n  }), _cache[11] || (_cache[11] = _createTextVNode(\" 正在初始化页面状态... \"))])])) : $setup.currentStep === 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 步骤1: 视频上传 \"), _createElementVNode(\"div\", _hoisted_32, [_createVNode(_component_FourWayVideoUpload, {\n    onUploadSuccess: $setup.handleUploadSuccess,\n    onUploadError: $setup.handleUploadError,\n    onUploadProgress: $setup.handleUploadProgress,\n    onStatusChange: $setup.handleUploadStatusChange\n  }, null, 8 /* PROPS */, [\"onUploadSuccess\", \"onUploadError\", \"onUploadProgress\", \"onStatusChange\"])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 步骤2: 实时检测 \"), $setup.currentStep === 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_33, [$setup.currentTaskId ? (_openBlock(), _createBlock(_component_FourWayRealtimeViewer, {\n    key: 0,\n    \"task-id\": $setup.currentTaskId,\n    \"auto-start\": true,\n    onDetectionUpdate: $setup.handleDetectionUpdate,\n    onStatusChange: $setup.handleDetectionStatusChange,\n    onAnalysisComplete: $setup.handleAnalysisComplete\n  }, null, 8 /* PROPS */, [\"task-id\", \"onDetectionUpdate\", \"onStatusChange\", \"onAnalysisComplete\"])) : (_openBlock(), _createBlock(_component_el_empty, {\n    key: 1,\n    description: \"请先上传视频文件\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[0] || (_cache[0] = $event => $setup.currentStep = 0)\n    }, {\n      default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\" 返回上传 \")])),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 步骤3: 智能分析 \"), $setup.currentStep === 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_34, [$setup.currentTaskId ? (_openBlock(), _createBlock(_component_TrafficAnalysisDashboard, {\n    key: 0,\n    \"task-id\": $setup.currentTaskId,\n    onDataUpdated: $setup.handleAnalysisDataUpdate\n  }, null, 8 /* PROPS */, [\"task-id\", \"onDataUpdated\"])) : (_openBlock(), _createBlock(_component_el_empty, {\n    key: 1,\n    description: \"请先完成视频检测\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[1] || (_cache[1] = $event => $setup.currentStep = 1)\n    }, {\n      default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\" 返回检测 \")])),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 步骤4: 报告生成 \"), $setup.currentStep === 3 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_35, [$setup.currentTaskId && $setup.reportData ? (_openBlock(), _createBlock(_component_IntelligentTrafficReport, {\n    key: 0,\n    \"task-id\": $setup.currentTaskId,\n    \"report-data\": $setup.reportData,\n    onExportReport: $setup.handleExportReport,\n    onRefreshData: $setup.handleRefreshReportData\n  }, null, 8 /* PROPS */, [\"task-id\", \"report-data\", \"onExportReport\", \"onRefreshData\"])) : (_openBlock(), _createBlock(_component_el_empty, {\n    key: 1,\n    description: \"请先完成智能分析\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[2] || (_cache[2] = $event => $setup.currentStep = 2)\n    }, {\n      default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\" 返回分析 \")])),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }))])) : _createCommentVNode(\"v-if\", true)])])]), _createCommentVNode(\" 底部状态栏 \"), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_cache[15] || (_cache[15] = _createElementVNode(\"span\", null, \"系统状态: \", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: $setup.systemStatus.type,\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.systemStatus.text), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"]), _cache[16] || (_cache[16] = _createElementVNode(\"span\", {\n    class: \"separator\"\n  }, \"|\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, \"活跃连接: \" + _toDisplayString($setup.activeConnections), 1 /* TEXT */), _cache[17] || (_cache[17] = _createElementVNode(\"span\", {\n    class: \"separator\"\n  }, \"|\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, \"最后更新: \" + _toDisplayString($setup.lastUpdateTime), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_38, [_createVNode(_component_el_button, {\n    size: \"small\",\n    onClick: $setup.refreshSystem\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[18] || (_cache[18] = _createTextVNode(\" 刷新 \"))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    size: \"small\",\n    onClick: $setup.showSystemInfo\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_InfoFilled)]),\n      _: 1 /* STABLE */\n    }), _cache[19] || (_cache[19] = _createTextVNode(\" 系统信息 \"))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_Grid", "_", "_createTextVNode", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_toDisplayString", "$setup", "totalTasks", "_hoisted_8", "_hoisted_9", "activeTasks", "_hoisted_10", "_hoisted_11", "completedTasks", "_hoisted_12", "_component_el_steps", "active", "currentStep", "_component_el_step", "title", "description", "icon", "_hoisted_13", "_hoisted_14", "_component_StepNavigator", "currentTask", "status", "progress", "loading", "isInitializing", "onStepChange", "handleStepChange", "onAction", "handleQuickAction", "onRefresh", "handleRefreshStatus", "_hoisted_15", "_hoisted_16", "_component_el_card", "header", "_hoisted_17", "_hoisted_18", "name", "_component_el_tag", "type", "getTaskStatusType", "getTaskStatusText", "_hoisted_19", "_component_el_progress", "percentage", "getProgressStatus", "_hoisted_20", "_hoisted_21", "_component_el_row", "gutter", "_component_el_col", "span", "_hoisted_22", "_hoisted_23", "id", "_hoisted_24", "_hoisted_25", "formatTime", "createdAt", "_hoisted_26", "_hoisted_27", "getProcessingDuration", "_hoisted_28", "_hoisted_29", "_component_el_skeleton", "rows", "animated", "template", "_hoisted_30", "_component_el_skeleton_item", "variant", "style", "_hoisted_31", "_component_Loading", "_Fragment", "_hoisted_32", "_component_FourWayVideoUpload", "onUploadSuccess", "handleUploadSuccess", "onUploadError", "handleUploadError", "onUploadProgress", "handleUploadProgress", "onStatusChange", "handleUploadStatusChange", "_hoisted_33", "currentTaskId", "_createBlock", "_component_FourWayRealtimeViewer", "onDetectionUpdate", "handleDetectionUpdate", "handleDetectionStatusChange", "onAnalysisComplete", "handleAnalysisComplete", "_component_el_empty", "_component_el_button", "onClick", "_cache", "$event", "_hoisted_34", "_component_TrafficAnalysisDashboard", "onDataUpdated", "handleAnalysisDataUpdate", "_hoisted_35", "reportData", "_component_IntelligentTrafficReport", "onExportReport", "handleExportReport", "onRefreshData", "handleRefreshReportData", "_hoisted_36", "_hoisted_37", "systemStatus", "size", "text", "activeConnections", "lastUpdateTime", "_hoisted_38", "refreshSystem", "_component_Refresh", "showSystemInfo", "_component_InfoFilled"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\FourWayAnalysisConsole.vue"], "sourcesContent": ["<template>\n  <div class=\"four-way-analysis-console\">\n    <!-- 页面头部 -->\n    <div class=\"console-header\">\n      <div class=\"header-content\">\n        <h1 class=\"console-title\">\n          <el-icon><Grid /></el-icon>\n          四方向智能交通分析控制台\n        </h1>\n        <p class=\"console-description\">\n          集成视频上传、实时检测、智能分析和报告生成的一站式交通分析平台\n        </p>\n      </div>\n      \n      <div class=\"header-stats\">\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ totalTasks }}</div>\n          <div class=\"stat-label\">总任务数</div>\n        </div>\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ activeTasks }}</div>\n          <div class=\"stat-label\">活跃任务</div>\n        </div>\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ completedTasks }}</div>\n          <div class=\"stat-label\">已完成</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 工作流程导航 -->\n    <div class=\"workflow-navigation\">\n      <el-steps :active=\"currentStep\" align-center>\n        <el-step \n          title=\"视频上传\" \n          description=\"上传四方向视频文件\"\n          icon=\"Upload\"\n        />\n        <el-step \n          title=\"实时检测\" \n          description=\"AI模型实时分析\"\n          icon=\"VideoCamera\"\n        />\n        <el-step \n          title=\"智能分析\" \n          description=\"生成分析结果\"\n          icon=\"DataAnalysis\"\n        />\n        <el-step \n          title=\"报告生成\" \n          description=\"导出分析报告\"\n          icon=\"Document\"\n        />\n      </el-steps>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"console-content\">\n      <!-- 左侧面板 -->\n      <div class=\"left-panel\">\n        <!-- 步骤导航器 -->\n        <StepNavigator\n          :current-step=\"currentStep\"\n          :task-status=\"currentTask.status\"\n          :task-progress=\"currentTask.progress\"\n          :loading=\"isInitializing\"\n          :allow-navigation=\"true\"\n          @step-change=\"handleStepChange\"\n          @action=\"handleQuickAction\"\n          @refresh=\"handleRefreshStatus\"\n        />\n      </div>\n\n      <!-- 右侧主内容 -->\n      <div class=\"main-content\">\n        <!-- 当前任务信息 -->\n        <div v-if=\"currentTask\" class=\"current-task-info\">\n          <el-card>\n            <template #header>\n              <div class=\"task-header\">\n                <div class=\"task-title-section\">\n                  <h3>{{ currentTask.name }}</h3>\n                  <el-tag :type=\"getTaskStatusType(currentTask.status)\">\n                    {{ getTaskStatusText(currentTask.status) }}\n                  </el-tag>\n                </div>\n                <div class=\"task-progress-section\">\n                  <el-progress \n                    :percentage=\"currentTask.progress\" \n                    :status=\"getProgressStatus(currentTask.status)\"\n                    :stroke-width=\"8\"\n                  />\n                  <span class=\"progress-text\">{{ currentTask.progress }}%</span>\n                </div>\n              </div>\n            </template>\n            \n            <div class=\"task-details\">\n              <el-row :gutter=\"20\">\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">任务ID:</span>\n                    <span class=\"detail-value\">{{ currentTask.id }}</span>\n                  </div>\n                </el-col>\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">创建时间:</span>\n                    <span class=\"detail-value\">{{ formatTime(currentTask.createdAt) }}</span>\n                  </div>\n                </el-col>\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">处理时长:</span>\n                    <span class=\"detail-value\">{{ getProcessingDuration(currentTask) }}</span>\n                  </div>\n                </el-col>\n              </el-row>\n            </div>\n          </el-card>\n        </div>\n\n        <!-- 动态内容区域 -->\n        <div class=\"dynamic-content\">\n          <!-- 初始化加载状态 -->\n          <div v-if=\"isInitializing\" class=\"loading-container\">\n            <el-skeleton :rows=\"8\" animated>\n              <template #template>\n                <div class=\"loading-content\">\n                  <el-skeleton-item variant=\"h1\" style=\"width: 40%; margin-bottom: 20px;\" />\n                  <el-skeleton-item variant=\"text\" style=\"width: 80%; margin-bottom: 10px;\" />\n                  <el-skeleton-item variant=\"text\" style=\"width: 60%; margin-bottom: 20px;\" />\n                  <el-skeleton-item variant=\"rect\" style=\"width: 100%; height: 200px;\" />\n                </div>\n              </template>\n            </el-skeleton>\n            <div class=\"loading-text\">\n              <el-icon class=\"loading-icon\"><Loading /></el-icon>\n              正在初始化页面状态...\n            </div>\n          </div>\n\n          <!-- 步骤1: 视频上传 -->\n          <div v-else-if=\"currentStep === 0\" class=\"step-content\">\n            <FourWayVideoUpload\n              @upload-success=\"handleUploadSuccess\"\n              @upload-error=\"handleUploadError\"\n              @upload-progress=\"handleUploadProgress\"\n              @status-change=\"handleUploadStatusChange\"\n            />\n          </div>\n\n          <!-- 步骤2: 实时检测 -->\n          <div v-if=\"currentStep === 1\" class=\"step-content\">\n            <FourWayRealtimeViewer\n              v-if=\"currentTaskId\"\n              :task-id=\"currentTaskId\"\n              :auto-start=\"true\"\n              @detection-update=\"handleDetectionUpdate\"\n              @status-change=\"handleDetectionStatusChange\"\n              @analysis-complete=\"handleAnalysisComplete\"\n            />\n            <el-empty v-else description=\"请先上传视频文件\">\n              <el-button type=\"primary\" @click=\"currentStep = 0\">\n                返回上传\n              </el-button>\n            </el-empty>\n          </div>\n\n          <!-- 步骤3: 智能分析 -->\n          <div v-if=\"currentStep === 2\" class=\"step-content\">\n            <TrafficAnalysisDashboard\n              v-if=\"currentTaskId\"\n              :task-id=\"currentTaskId\"\n              @data-updated=\"handleAnalysisDataUpdate\"\n            />\n            <el-empty v-else description=\"请先完成视频检测\">\n              <el-button type=\"primary\" @click=\"currentStep = 1\">\n                返回检测\n              </el-button>\n            </el-empty>\n          </div>\n\n          <!-- 步骤4: 报告生成 -->\n          <div v-if=\"currentStep === 3\" class=\"step-content\">\n            <IntelligentTrafficReport\n              v-if=\"currentTaskId && reportData\"\n              :task-id=\"currentTaskId\"\n              :report-data=\"reportData\"\n              @export-report=\"handleExportReport\"\n              @refresh-data=\"handleRefreshReportData\"\n            />\n            <el-empty v-else description=\"请先完成智能分析\">\n              <el-button type=\"primary\" @click=\"currentStep = 2\">\n                返回分析\n              </el-button>\n            </el-empty>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 底部状态栏 -->\n    <div class=\"console-footer\">\n      <div class=\"footer-info\">\n        <span>系统状态: </span>\n        <el-tag :type=\"systemStatus.type\" size=\"small\">{{ systemStatus.text }}</el-tag>\n        <span class=\"separator\">|</span>\n        <span>活跃连接: {{ activeConnections }}</span>\n        <span class=\"separator\">|</span>\n        <span>最后更新: {{ lastUpdateTime }}</span>\n      </div>\n      \n      <div class=\"footer-actions\">\n        <el-button size=\"small\" @click=\"refreshSystem\">\n          <el-icon><Refresh /></el-icon>\n          刷新\n        </el-button>\n        <el-button size=\"small\" @click=\"showSystemInfo\">\n          <el-icon><InfoFilled /></el-icon>\n          系统信息\n        </el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'\nimport { useRouter, useRoute } from 'vue-router'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport {\n  Grid, Upload, VideoCamera, DataAnalysis, Document, Plus,\n  MoreFilled, Refresh, InfoFilled, Loading\n} from '@element-plus/icons-vue'\n\n// 导入组件\nimport FourWayVideoUpload from '@/components/analysis/FourWayVideoUpload.vue'\nimport FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue'\nimport TrafficAnalysisDashboard from '@/components/analysis/TrafficAnalysisDashboard.vue'\nimport IntelligentTrafficReport from '@/components/analysis/IntelligentTrafficReport.vue'\nimport StepNavigator from '@/components/analysis/StepNavigator.vue'\n\nexport default {\n  name: 'FourWayAnalysisConsole',\n  components: {\n    Grid, Upload, VideoCamera, DataAnalysis, Document, Plus,\n    MoreFilled, Refresh, InfoFilled, Loading,\n    FourWayVideoUpload,\n    FourWayRealtimeViewer,\n    TrafficAnalysisDashboard,\n    IntelligentTrafficReport,\n    StepNavigator\n  },\n  setup() {\n    const router = useRouter()\n    const route = useRoute()\n    \n    // 响应式数据\n    const currentStep = ref(0)\n    const currentTaskId = ref('')\n    const currentTask = ref({\n      id: '',\n      name: '四方向交通分析任务',\n      status: 'waiting',\n      progress: 0,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    })\n    const reportData = ref(null)\n    const activeConnections = ref(0)\n    const lastUpdateTime = ref('')\n    const isInitializing = ref(true)\n    const taskStatusPolling = ref(null)\n\n    // 系统状态\n    const systemStatus = reactive({\n      type: 'success',\n      text: '正常运行'\n    })\n\n    // 计算属性\n    const totalTasks = computed(() => 1)\n    const activeTasks = computed(() => currentStep.value > 0 && currentStep.value < 4 ? 1 : 0)\n    const completedTasks = computed(() => currentStep.value === 4 ? 1 : 0)\n\n    const canUpload = computed(() => true)\n    const canDetect = computed(() => currentStep.value >= 1)\n    const canAnalyze = computed(() => currentStep.value >= 2)\n    const canExport = computed(() => currentStep.value >= 3)\n    \n    // 方法\n\n    // 初始化和状态检测方法\n    const initializeFromRoute = async () => {\n      try {\n        isInitializing.value = true\n\n        // 检查URL参数中的mode参数\n        const sessionMode = route.query.mode\n        const urlTaskId = route.query.taskId || route.params.taskId\n\n        console.log('页面初始化 - 模式:', sessionMode, '任务ID:', urlTaskId)\n\n        // 根据模式参数决定初始化策略\n        if (sessionMode === 'new') {\n          console.log('检测到新会话模式，清理之前的状态')\n          await initializeNewSession()\n        } else if (urlTaskId) {\n          console.log('从URL参数检测到任务ID:', urlTaskId)\n          currentTaskId.value = urlTaskId\n          // 获取任务状态并设置对应步骤\n          await fetchTaskStatusAndSetStep(urlTaskId)\n        } else {\n          // 智能检测会话模式\n          await detectSessionMode()\n        }\n\n      } catch (error) {\n        console.error('初始化失败:', error)\n        ElMessage.error('初始化页面状态失败: ' + error.message)\n      } finally {\n        isInitializing.value = false\n      }\n    }\n\n    const fetchTaskStatusAndSetStep = async (taskId) => {\n      try {\n        const token = localStorage.getItem('auth_token')\n        if (!token) {\n          throw new Error('未找到认证令牌')\n        }\n\n        const response = await fetch(`/api/video-analysis/four-way/${taskId}/status`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`获取任务状态失败: ${response.status}`)\n        }\n\n        const taskData = await response.json()\n        console.log('获取到任务状态:', taskData)\n\n        // 更新当前任务信息\n        currentTask.value = {\n          id: taskData.taskId || taskId,\n          name: taskData.name || '四方向交通分析任务',\n          status: taskData.status || 'waiting',\n          progress: taskData.progress || 0,\n          createdAt: taskData.createdAt ? new Date(taskData.createdAt) : new Date(),\n          updatedAt: taskData.updatedAt ? new Date(taskData.updatedAt) : new Date()\n        }\n\n        // 根据任务状态设置对应步骤\n        setStepByTaskStatus(taskData.status, taskData.progress)\n\n        // 如果任务正在处理中，开始轮询状态\n        if (['queued', 'processing'].includes(taskData.status)) {\n          startTaskStatusPolling(taskId)\n        }\n\n      } catch (error) {\n        console.error('获取任务状态失败:', error)\n        ElMessage.warning('无法获取任务状态，将从上传步骤开始')\n        currentStep.value = 0\n      }\n    }\n\n    const setStepByTaskStatus = (status, progress = 0) => {\n      console.log('根据任务状态设置步骤:', { status, progress })\n\n      switch (status) {\n        case 'queued':\n        case 'uploading':\n          currentStep.value = 0 // 上传步骤\n          ElMessage.info('任务正在排队中，请等待处理')\n          break\n\n        case 'processing':\n          if (progress < 50) {\n            currentStep.value = 1 // 实时检测步骤\n            ElMessage.info('任务正在进行实时检测')\n          } else if (progress < 90) {\n            currentStep.value = 1 // 仍在检测阶段\n            ElMessage.info('实时检测进行中')\n          } else {\n            currentStep.value = 2 // 智能分析步骤\n            ElMessage.info('正在进行智能分析')\n          }\n          break\n\n        case 'completed':\n          currentStep.value = 2 // 跳转到智能分析步骤\n          ElMessage.success('任务已完成，可以查看分析结果')\n          break\n\n        case 'failed':\n          currentStep.value = 0 // 回到上传步骤\n          ElMessage.error('任务处理失败，请重新上传')\n          break\n\n        default:\n          currentStep.value = 0 // 默认从上传开始\n          break\n      }\n    }\n\n    // 智能会话检测机制\n    const detectSessionMode = async () => {\n      try {\n        // 检查会话存储中是否有活跃任务\n        const sessionData = sessionStorage.getItem('fourWayActiveTask')\n        if (sessionData) {\n          const taskInfo = JSON.parse(sessionData)\n          const sessionAge = Date.now() - taskInfo.timestamp\n          const maxSessionAge = 24 * 60 * 60 * 1000 // 24小时\n\n          console.log('检测到会话数据:', taskInfo, '会话年龄:', sessionAge / 1000 / 60, '分钟')\n\n          // 如果会话数据过期，清理并开始新会话\n          if (sessionAge > maxSessionAge) {\n            console.log('会话数据已过期，开始新会话')\n            await initializeNewSession()\n            return\n          }\n\n          // 显示用户选择界面：继续任务 vs 开始新任务\n          await showSessionChoiceDialog(taskInfo)\n        } else {\n          // 没有活跃任务，开始新会话\n          console.log('没有检测到活跃任务，开始新会话')\n          await initializeNewSession()\n        }\n\n      } catch (error) {\n        console.error('会话检测失败:', error)\n        await initializeNewSession()\n      }\n    }\n\n    // 初始化新会话\n    const initializeNewSession = async () => {\n      try {\n        console.log('初始化新会话')\n\n        // 清理之前的会话数据\n        await clearPreviousSession()\n\n        // 重置页面状态\n        currentStep.value = 0\n        currentTaskId.value = null\n        currentTask.value = {}\n        reportData.value = null\n\n        // 停止任何正在进行的轮询\n        stopTaskStatusPolling()\n\n        // 清理URL参数中的taskId\n        if (route.query.taskId) {\n          router.replace({\n            path: route.path,\n            query: { ...route.query, taskId: undefined }\n          })\n        }\n\n        ElMessage.success('已开始新的分析会话')\n\n      } catch (error) {\n        console.error('初始化新会话失败:', error)\n        ElMessage.error('初始化新会话失败: ' + error.message)\n      }\n    }\n\n    // 清理之前的会话\n    const clearPreviousSession = async () => {\n      try {\n        console.log('清理之前的会话数据')\n\n        // 清理会话存储\n        sessionStorage.removeItem('fourWayActiveTask')\n\n        // 清理其他相关的存储数据\n        const keysToRemove = ['uploadState', 'fourWayProgress', 'fourWayResults']\n        keysToRemove.forEach(key => {\n          try {\n            sessionStorage.removeItem(key)\n          } catch (e) {\n            console.warn(`清理存储键 ${key} 失败:`, e)\n          }\n        })\n\n      } catch (error) {\n        console.error('清理会话数据失败:', error)\n      }\n    }\n\n    // 显示会话选择对话框\n    const showSessionChoiceDialog = async (taskInfo) => {\n      try {\n        const result = await ElMessageBox.confirm(\n          `检测到您有一个未完成的四方向分析任务（任务ID: ${taskInfo.taskId}）。您希望：`,\n          '会话选择',\n          {\n            confirmButtonText: '继续之前的任务',\n            cancelButtonText: '开始新的分析',\n            type: 'question',\n            distinguishCancelAndClose: true,\n            closeOnClickModal: false,\n            closeOnPressEscape: false,\n            showClose: false,\n            customClass: 'session-choice-dialog'\n          }\n        )\n\n        // 用户选择继续之前的任务\n        if (result === 'confirm') {\n          console.log('用户选择继续之前的任务')\n          currentTaskId.value = taskInfo.taskId\n          await fetchTaskStatusAndSetStep(taskInfo.taskId)\n          ElMessage.info('已恢复之前的分析任务')\n        }\n\n      } catch (action) {\n        // 用户选择开始新分析或关闭对话框\n        if (action === 'cancel') {\n          console.log('用户选择开始新的分析')\n          await initializeNewSession()\n        } else {\n          // 用户关闭对话框，默认开始新会话\n          console.log('用户关闭对话框，开始新会话')\n          await initializeNewSession()\n        }\n      }\n    }\n\n    const checkActiveTask = async () => {\n      // 这个方法现在被 detectSessionMode 替代，保留用于向后兼容\n      await detectSessionMode()\n    }\n\n    const startTaskStatusPolling = (taskId) => {\n      // 清除现有的轮询\n      if (taskStatusPolling.value) {\n        clearInterval(taskStatusPolling.value)\n      }\n\n      console.log('开始轮询任务状态:', taskId)\n\n      taskStatusPolling.value = setInterval(async () => {\n        try {\n          await fetchTaskStatusAndSetStep(taskId)\n\n          // 如果任务完成或失败，停止轮询\n          if (['completed', 'failed'].includes(currentTask.value.status)) {\n            clearInterval(taskStatusPolling.value)\n            taskStatusPolling.value = null\n          }\n\n        } catch (error) {\n          console.error('轮询任务状态失败:', error)\n        }\n      }, 3000) // 每3秒轮询一次\n    }\n\n    const stopTaskStatusPolling = () => {\n      if (taskStatusPolling.value) {\n        clearInterval(taskStatusPolling.value)\n        taskStatusPolling.value = null\n      }\n    }\n\n    // 增强的会话存储管理\n    const saveTaskToSession = (taskId, additionalData = {}) => {\n      try {\n        const sessionData = {\n          taskId: taskId,\n          timestamp: Date.now(),\n          sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n          mode: 'active',\n          ...additionalData\n        }\n\n        sessionStorage.setItem('fourWayActiveTask', JSON.stringify(sessionData))\n        console.log('任务已保存到会话存储:', sessionData)\n\n      } catch (error) {\n        console.warn('保存任务信息到会话存储失败:', error)\n      }\n    }\n\n    const getTaskFromSession = () => {\n      try {\n        const sessionData = sessionStorage.getItem('fourWayActiveTask')\n        if (sessionData) {\n          const taskInfo = JSON.parse(sessionData)\n\n          // 检查会话有效性\n          const sessionAge = Date.now() - taskInfo.timestamp\n          const maxSessionAge = 24 * 60 * 60 * 1000 // 24小时\n\n          if (sessionAge <= maxSessionAge) {\n            return taskInfo\n          } else {\n            console.log('会话数据已过期，清理存储')\n            sessionStorage.removeItem('fourWayActiveTask')\n          }\n        }\n      } catch (error) {\n        console.warn('从会话存储获取任务信息失败:', error)\n      }\n      return null\n    }\n\n    // 事件处理\n    const handleUploadSuccess = (response) => {\n      const taskId = response.data?.taskId || response.taskId || `task_${Date.now()}`\n      currentTaskId.value = taskId\n\n      // 使用增强的会话存储管理\n      saveTaskToSession(taskId, {\n        uploadTime: new Date().toISOString(),\n        status: 'processing'\n      })\n\n      // 更新当前任务信息\n      currentTask.value = {\n        id: taskId,\n        name: '四方向交通分析任务',\n        status: 'processing',\n        progress: 10,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      }\n\n      // 更新URL参数但不导航\n      router.replace({\n        path: route.path,\n        query: { ...route.query, taskId: taskId }\n      })\n\n      currentStep.value = 1\n      ElMessage.success('视频上传成功，开始实时检测')\n\n      // 开始轮询任务状态\n      startTaskStatusPolling(taskId)\n    }\n\n    const handleUploadError = (error) => {\n      ElMessage.error('视频上传失败: ' + error.message)\n    }\n\n    const handleUploadProgress = (progress) => {\n      console.log('上传进度:', progress)\n    }\n\n    const handleUploadStatusChange = (status) => {\n      console.log('上传状态变化:', status)\n    }\n\n    const handleDetectionUpdate = (data) => {\n      console.log('检测更新:', data)\n\n      // 更新任务进度\n      if (currentTask.value && data.progress) {\n        currentTask.value.progress = Math.min(data.progress, 90) // 检测阶段最多90%\n        currentTask.value.updatedAt = new Date()\n      }\n    }\n\n    const handleDetectionStatusChange = (status) => {\n      if (status === 'completed') {\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed'\n          currentTask.value.progress = 100\n          currentTask.value.updatedAt = new Date()\n        }\n\n        currentStep.value = 2\n        ElMessage.success('实时检测完成，开始智能分析')\n      }\n    }\n\n    // 处理四方向分析完成事件\n    const handleAnalysisComplete = (completeData) => {\n      try {\n        console.log('🎉 收到四方向分析完成事件:', completeData)\n\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed'\n          currentTask.value.progress = 100\n          currentTask.value.updatedAt = new Date()\n        }\n\n        // 显示完成提示并自动跳转\n        ElMessage({\n          message: `四方向分析完成！检测到 ${completeData.summary?.totalVehicles || 0} 辆车辆，正在跳转到智能分析模块...`,\n          type: 'success',\n          duration: 4000\n        })\n\n        // 延迟跳转到智能分析模块，给用户时间看到完成消息\n        setTimeout(() => {\n          currentStep.value = 2\n          ElMessage.info('已自动跳转到智能分析模块')\n        }, 2000)\n\n      } catch (error) {\n        console.error('处理分析完成事件失败:', error)\n        ElMessage.error('处理完成事件失败，请手动跳转到智能分析')\n      }\n    }\n\n    const handleAnalysisDataUpdate = (data) => {\n      reportData.value = data\n      currentStep.value = 3\n      ElMessage.success('智能分析完成，可以生成报告')\n    }\n\n    const handleExportReport = (taskId) => {\n      ElMessage.success('报告导出成功')\n    }\n\n    const handleRefreshReportData = (taskId) => {\n      ElMessage.success('报告数据刷新成功')\n    }\n    \n    // 快速操作\n    const goToUpload = () => {\n      currentStep.value = 0\n    }\n    \n    const startDetection = () => {\n      if (canDetect.value) {\n        currentStep.value = 1\n      } else {\n        ElMessage.warning('请先上传视频文件')\n      }\n    }\n    \n    const generateAnalysis = () => {\n      if (canAnalyze.value) {\n        currentStep.value = 2\n      } else {\n        ElMessage.warning('请先完成视频检测')\n      }\n    }\n    \n    const exportReport = () => {\n      if (canExport.value) {\n        currentStep.value = 3\n      } else {\n        ElMessage.warning('请先完成智能分析')\n      }\n    }\n\n    const refreshSystem = () => {\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n      ElMessage.success('系统状态已刷新')\n    }\n\n    const showSystemInfo = () => {\n      ElMessageBox.alert('四方向智能交通分析系统 v1.0.0', '系统信息', {\n        confirmButtonText: '确定'\n      })\n    }\n\n    // StepNavigator事件处理\n    const handleStepChange = (stepIndex) => {\n      console.log('步骤切换请求:', stepIndex)\n\n      // 检查是否可以切换到目标步骤\n      if (stepIndex <= currentStep.value) {\n        currentStep.value = stepIndex\n        ElMessage.info(`已切换到步骤 ${stepIndex + 1}`)\n      } else {\n        ElMessage.warning('请先完成前面的步骤')\n      }\n    }\n\n    const handleQuickAction = (actionKey) => {\n      console.log('快速操作:', actionKey)\n\n      switch (actionKey) {\n        case 'upload':\n          goToUpload()\n          break\n        case 'detection':\n          startDetection()\n          break\n        case 'analysis':\n          generateAnalysis()\n          break\n        case 'report':\n          exportReport()\n          break\n        default:\n          console.warn('未知的快速操作:', actionKey)\n      }\n    }\n\n    // 页面刷新优化\n    const handlePageRefresh = async () => {\n      try {\n        console.log('处理页面刷新事件')\n\n        // 检查是否是强制刷新（Ctrl+F5 或 Cmd+Shift+R）\n        const isHardRefresh = performance.navigation?.type === 1\n\n        if (isHardRefresh) {\n          console.log('检测到强制刷新，开始新会话')\n          await initializeNewSession()\n          return\n        }\n\n        // 普通刷新，检查会话状态\n        const sessionTask = getTaskFromSession()\n        if (sessionTask) {\n          console.log('页面刷新 - 恢复会话:', sessionTask)\n          currentTaskId.value = sessionTask.taskId\n          await fetchTaskStatusAndSetStep(sessionTask.taskId)\n        } else {\n          console.log('页面刷新 - 没有有效会话，开始新会话')\n          await initializeNewSession()\n        }\n\n      } catch (error) {\n        console.error('处理页面刷新失败:', error)\n        await initializeNewSession()\n      }\n    }\n\n    const handleRefreshStatus = async () => {\n      if (currentTaskId.value) {\n        ElMessage.info('正在刷新任务状态...')\n        await fetchTaskStatusAndSetStep(currentTaskId.value)\n      } else {\n        ElMessage.warning('没有活跃的任务')\n      }\n    }\n\n    // 页面状态一致性检查\n    const checkPageStateConsistency = () => {\n      try {\n        const urlTaskId = route.query.taskId\n        const sessionTask = getTaskFromSession()\n        const currentTaskIdValue = currentTaskId.value\n\n        console.log('页面状态一致性检查:', {\n          urlTaskId,\n          sessionTaskId: sessionTask?.taskId,\n          currentTaskIdValue\n        })\n\n        // 如果URL和会话存储不一致，以URL为准\n        if (urlTaskId && sessionTask && urlTaskId !== sessionTask.taskId) {\n          console.log('检测到状态不一致，以URL参数为准')\n          saveTaskToSession(urlTaskId)\n          currentTaskId.value = urlTaskId\n        }\n\n      } catch (error) {\n        console.error('页面状态一致性检查失败:', error)\n      }\n    }\n\n    // 任务状态辅助方法\n    const getTaskStatusType = (status) => {\n      const statusMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'failed': 'danger',\n        'paused': 'info'\n      }\n      return statusMap[status] || 'info'\n    }\n\n    const getTaskStatusText = (status) => {\n      const statusMap = {\n        'waiting': '等待中',\n        'processing': '处理中',\n        'completed': '已完成',\n        'failed': '失败',\n        'paused': '已暂停'\n      }\n      return statusMap[status] || '未知'\n    }\n\n    const getProgressStatus = (status) => {\n      if (status === 'completed') return 'success'\n      if (status === 'failed') return 'exception'\n      return null\n    }\n\n    const formatTime = (time) => {\n      if (!time) return '-'\n      return new Date(time).toLocaleString()\n    }\n\n    const getProcessingDuration = (task) => {\n      if (!task || !task.createdAt) return '-'\n      const start = new Date(task.createdAt)\n      const end = task.updatedAt ? new Date(task.updatedAt) : new Date()\n      const duration = Math.floor((end - start) / 1000)\n\n      if (duration < 60) return `${duration}秒`\n      if (duration < 3600) return `${Math.floor(duration / 60)}分钟`\n      return `${Math.floor(duration / 3600)}小时`\n    }\n\n    // 监听路由变化 - 增强版\n    watch(() => route.query, (newQuery, oldQuery) => {\n      const newTaskId = newQuery.taskId\n      const newMode = newQuery.mode\n      const oldTaskId = oldQuery?.taskId\n      const oldMode = oldQuery?.mode\n\n      console.log('路由参数变化:', { newTaskId, newMode, oldTaskId, oldMode })\n\n      // 检测模式变化\n      if (newMode !== oldMode) {\n        if (newMode === 'new') {\n          console.log('检测到新会话模式参数')\n          initializeNewSession()\n          return\n        }\n      }\n\n      // 检测任务ID变化\n      if (newTaskId && newTaskId !== currentTaskId.value) {\n        console.log('检测到URL中的taskId变化:', newTaskId)\n        currentTaskId.value = newTaskId\n        fetchTaskStatusAndSetStep(newTaskId)\n      }\n    }, { deep: true })\n\n    // 状态隔离机制\n    const createSessionNamespace = (taskId) => {\n      return `fourWay_${taskId}_${Date.now()}`\n    }\n\n    const getNamespacedStorageKey = (key, taskId = null) => {\n      const baseKey = taskId ? `${key}_${taskId}` : key\n      return `fourWayConsole_${baseKey}`\n    }\n\n    const setNamespacedStorage = (key, value, taskId = null) => {\n      try {\n        const namespacedKey = getNamespacedStorageKey(key, taskId)\n        sessionStorage.setItem(namespacedKey, JSON.stringify(value))\n      } catch (error) {\n        console.warn('设置命名空间存储失败:', error)\n      }\n    }\n\n    const getNamespacedStorage = (key, taskId = null) => {\n      try {\n        const namespacedKey = getNamespacedStorageKey(key, taskId)\n        const value = sessionStorage.getItem(namespacedKey)\n        return value ? JSON.parse(value) : null\n      } catch (error) {\n        console.warn('获取命名空间存储失败:', error)\n        return null\n      }\n    }\n\n    const clearNamespacedStorage = (taskId = null) => {\n      try {\n        const prefix = taskId ? `fourWayConsole_${taskId}` : 'fourWayConsole_'\n        const keysToRemove = []\n\n        for (let i = 0; i < sessionStorage.length; i++) {\n          const key = sessionStorage.key(i)\n          if (key && key.startsWith(prefix)) {\n            keysToRemove.push(key)\n          }\n        }\n\n        keysToRemove.forEach(key => sessionStorage.removeItem(key))\n        console.log('已清理命名空间存储:', keysToRemove.length, '个项目')\n\n      } catch (error) {\n        console.warn('清理命名空间存储失败:', error)\n      }\n    }\n\n    // 生命周期\n    onMounted(async () => {\n      // 初始化系统状态\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n      activeConnections.value = 1\n\n      // 页面状态一致性检查\n      checkPageStateConsistency()\n\n      // 初始化页面状态\n      await initializeFromRoute()\n    })\n\n    onUnmounted(() => {\n      // 清理轮询\n      stopTaskStatusPolling()\n    })\n    \n    return {\n      // 图标组件\n      Upload,\n      VideoCamera,\n      DataAnalysis,\n      Document,\n      Plus,\n      MoreFilled,\n      Refresh,\n      InfoFilled,\n\n      // 响应式数据\n      currentStep,\n      currentTaskId,\n      currentTask,\n      reportData,\n      activeConnections,\n      lastUpdateTime,\n      systemStatus,\n      isInitializing,\n\n      // 计算属性\n      totalTasks,\n      activeTasks,\n      completedTasks,\n      canUpload,\n      canDetect,\n      canAnalyze,\n      canExport,\n\n      // 用户反馈和提示\n      showUserFeedback: (message, type = 'info', duration = 3000) => {\n        ElMessage({\n          message,\n          type,\n          duration,\n          showClose: true\n        })\n      },\n\n      showConfirmDialog: async (message, title = '确认操作') => {\n        try {\n          await ElMessageBox.confirm(message, title, {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          })\n          return true\n        } catch {\n          return false\n        }\n      },\n\n      // 方法\n      initializeFromRoute,\n      fetchTaskStatusAndSetStep,\n      setStepByTaskStatus,\n      checkActiveTask,\n      detectSessionMode,\n      initializeNewSession,\n      clearPreviousSession,\n      showSessionChoiceDialog,\n      saveTaskToSession,\n      getTaskFromSession,\n      handlePageRefresh,\n      checkPageStateConsistency,\n      createSessionNamespace,\n      getNamespacedStorageKey,\n      setNamespacedStorage,\n      getNamespacedStorage,\n      clearNamespacedStorage,\n      startTaskStatusPolling,\n      stopTaskStatusPolling,\n      handleUploadSuccess,\n      handleUploadError,\n      handleUploadProgress,\n      handleUploadStatusChange,\n      handleDetectionUpdate,\n      handleDetectionStatusChange,\n      handleAnalysisComplete,\n      handleAnalysisDataUpdate,\n      handleExportReport,\n      handleRefreshReportData,\n      goToUpload,\n      startDetection,\n      generateAnalysis,\n      exportReport,\n      refreshSystem,\n      showSystemInfo,\n      handleStepChange,\n      handleQuickAction,\n      handleRefreshStatus,\n\n      // 任务状态辅助方法\n      getTaskStatusType,\n      getTaskStatusText,\n      getProgressStatus,\n      formatTime,\n      getProcessingDuration\n    }\n  }\n}\n</script>\n\n<style scoped>\n.four-way-analysis-console {\n  min-height: 100vh;\n  background: #f5f7fa;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 控制台头部 */\n.console-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 24px 32px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.console-title {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 28px;\n  font-weight: 700;\n  margin: 0 0 8px 0;\n}\n\n.console-description {\n  font-size: 16px;\n  opacity: 0.9;\n  margin: 0;\n  line-height: 1.5;\n}\n\n.header-stats {\n  display: flex;\n  gap: 32px;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: 700;\n  line-height: 1.2;\n}\n\n.stat-label {\n  font-size: 14px;\n  opacity: 0.8;\n  margin-top: 4px;\n}\n\n/* 工作流程导航 */\n.workflow-navigation {\n  background: white;\n  padding: 24px 32px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n/* 主要内容区域 */\n.console-content {\n  flex: 1;\n  display: grid;\n  grid-template-columns: 280px 1fr;\n  gap: 24px;\n  padding: 24px 32px;\n  min-height: 0;\n}\n\n/* 左侧面板 */\n.left-panel {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.quick-actions-card {\n  height: fit-content;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.quick-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.quick-actions .el-button {\n  justify-content: flex-start;\n}\n\n/* 主内容区域 */\n.main-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  min-height: 0;\n}\n\n.current-task-info {\n  flex-shrink: 0;\n}\n\n.task-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.task-title-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.task-title-section h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.task-progress-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  min-width: 200px;\n}\n\n.progress-text {\n  font-size: 14px;\n  font-weight: 500;\n  color: #6b7280;\n  min-width: 40px;\n}\n\n.task-details {\n  margin-top: 16px;\n}\n\n.detail-item {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.detail-label {\n  font-size: 12px;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.detail-value {\n  font-size: 14px;\n  color: #1f2937;\n  font-weight: 500;\n}\n\n.dynamic-content {\n  flex: 1;\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  overflow: auto;\n}\n\n.step-content {\n  height: 100%;\n}\n\n/* 加载状态样式 */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 400px;\n  gap: 20px;\n}\n\n.loading-content {\n  width: 100%;\n  max-width: 600px;\n}\n\n.loading-text {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  color: #6b7280;\n  margin-top: 20px;\n}\n\n.loading-icon {\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n/* 底部状态栏 */\n.console-footer {\n  background: white;\n  border-top: 1px solid #e5e7eb;\n  padding: 12px 32px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 14px;\n}\n\n.footer-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #6b7280;\n}\n\n.separator {\n  color: #d1d5db;\n}\n\n.footer-actions {\n  display: flex;\n  gap: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .console-content {\n    grid-template-columns: 300px 1fr;\n  }\n\n  .header-stats {\n    gap: 16px;\n  }\n}\n\n@media (max-width: 768px) {\n  .console-header {\n    flex-direction: column;\n    gap: 16px;\n    text-align: center;\n  }\n\n  .header-stats {\n    justify-content: center;\n  }\n\n  .console-content {\n    grid-template-columns: 1fr;\n    padding: 16px;\n  }\n\n  .workflow-navigation {\n    padding: 16px;\n  }\n\n  .console-footer {\n    flex-direction: column;\n    gap: 12px;\n    padding: 16px;\n  }\n\n  .task-header {\n    flex-direction: column;\n    gap: 12px;\n    align-items: flex-start;\n  }\n\n  .task-progress-section {\n    width: 100%;\n    min-width: auto;\n  }\n}\n\n/* 滚动条样式 */\n.task-list::-webkit-scrollbar {\n  width: 6px;\n}\n\n.task-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.task-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.task-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 动画效果 */\n.task-item {\n  animation: slideIn 0.3s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.step-content {\n  animation: fadeIn 0.5s ease-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA2B;;EAE/BA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAgB;;EACrBA,KAAK,EAAC;AAAe;;EAStBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAOxBA,KAAK,EAAC;AAAqB;;EA0B3BA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAY;;EAelBA,KAAK,EAAC;AAAc;;EA1E/BC,GAAA;EA4EgCD,KAAK,EAAC;;;EAGnBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAoB;;EAM1BA,KAAK,EAAC;AAAuB;;EAM1BA,KAAK,EAAC;AAAe;;EAK5BA,KAAK,EAAC;AAAc;;EAGdA,KAAK,EAAC;AAAa;;EAEhBA,KAAK,EAAC;AAAc;;EAIvBA,KAAK,EAAC;AAAa;;EAEhBA,KAAK,EAAC;AAAc;;EAIvBA,KAAK,EAAC;AAAa;;EAEhBA,KAAK,EAAC;AAAc;;EASjCA,KAAK,EAAC;AAAiB;;EA3HpCC,GAAA;EA6HqCD,KAAK,EAAC;;;EAGtBA,KAAK,EAAC;AAAiB;;EAQ3BA,KAAK,EAAC;AAAc;;EAOQA,KAAK,EAAC;AAAc;;EA/IjEC,GAAA;EAyJwCD,KAAK,EAAC;;;EAzJ9CC,GAAA;EA0KwCD,KAAK,EAAC;;;EA1K9CC,GAAA;EAwLwCD,KAAK,EAAC;;;EAmBrCA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAa;;EASnBA,KAAK,EAAC;AAAgB;;;;;;;;;;;;;;;;;;;;;;;uBApN/BE,mBAAA,CA+NM,OA/NNC,UA+NM,GA9NJC,mBAAA,UAAa,EACbC,mBAAA,CAyBM,OAzBNC,UAyBM,GAxBJD,mBAAA,CAQM,OARNE,UAQM,GAPJF,mBAAA,CAGK,MAHLG,UAGK,GAFHC,YAAA,CAA2BC,kBAAA;IANrCC,OAAA,EAAAC,QAAA,CAMmB,MAAQ,CAARH,YAAA,CAAQI,eAAA,E;IAN3BC,CAAA;gCAAAC,gBAAA,CAMqC,gBAE7B,G,6BACAV,mBAAA,CAEI;IAFDL,KAAK,EAAC;EAAqB,GAAC,mCAE/B,qB,GAGFK,mBAAA,CAaM,OAbNW,UAaM,GAZJX,mBAAA,CAGM,OAHNY,UAGM,GAFJZ,mBAAA,CAA8C,OAA9Ca,UAA8C,EAAAC,gBAAA,CAAnBC,MAAA,CAAAC,UAAU,kB,0BACrChB,mBAAA,CAAkC;IAA7BL,KAAK,EAAC;EAAY,GAAC,MAAI,qB,GAE9BK,mBAAA,CAGM,OAHNiB,UAGM,GAFJjB,mBAAA,CAA+C,OAA/CkB,UAA+C,EAAAJ,gBAAA,CAApBC,MAAA,CAAAI,WAAW,kB,0BACtCnB,mBAAA,CAAkC;IAA7BL,KAAK,EAAC;EAAY,GAAC,MAAI,qB,GAE9BK,mBAAA,CAGM,OAHNoB,WAGM,GAFJpB,mBAAA,CAAkD,OAAlDqB,WAAkD,EAAAP,gBAAA,CAAvBC,MAAA,CAAAO,cAAc,kB,0BACzCtB,mBAAA,CAAiC;IAA5BL,KAAK,EAAC;EAAY,GAAC,KAAG,qB,OAKjCI,mBAAA,YAAe,EACfC,mBAAA,CAuBM,OAvBNuB,WAuBM,GAtBJnB,YAAA,CAqBWoB,mBAAA;IArBAC,MAAM,EAAEV,MAAA,CAAAW,WAAW;IAAE,cAAY,EAAZ;;IAhCtCpB,OAAA,EAAAC,QAAA,CAiCQ,MAIE,CAJFH,YAAA,CAIEuB,kBAAA;MAHAC,KAAK,EAAC,MAAM;MACZC,WAAW,EAAC,WAAW;MACvBC,IAAI,EAAC;QAEP1B,YAAA,CAIEuB,kBAAA;MAHAC,KAAK,EAAC,MAAM;MACZC,WAAW,EAAC,UAAU;MACtBC,IAAI,EAAC;QAEP1B,YAAA,CAIEuB,kBAAA;MAHAC,KAAK,EAAC,MAAM;MACZC,WAAW,EAAC,QAAQ;MACpBC,IAAI,EAAC;QAEP1B,YAAA,CAIEuB,kBAAA;MAHAC,KAAK,EAAC,MAAM;MACZC,WAAW,EAAC,QAAQ;MACpBC,IAAI,EAAC;;IAnDfrB,CAAA;mCAwDIV,mBAAA,YAAe,EACfC,mBAAA,CA+IM,OA/IN+B,WA+IM,GA9IJhC,mBAAA,UAAa,EACbC,mBAAA,CAYM,OAZNgC,WAYM,GAXJjC,mBAAA,WAAc,EACdK,YAAA,CASE6B,wBAAA;IARC,cAAY,EAAElB,MAAA,CAAAW,WAAW;IACzB,aAAW,EAAEX,MAAA,CAAAmB,WAAW,CAACC,MAAM;IAC/B,eAAa,EAAEpB,MAAA,CAAAmB,WAAW,CAACE,QAAQ;IACnCC,OAAO,EAAEtB,MAAA,CAAAuB,cAAc;IACvB,kBAAgB,EAAE,IAAI;IACtBC,YAAW,EAAExB,MAAA,CAAAyB,gBAAgB;IAC7BC,QAAM,EAAE1B,MAAA,CAAA2B,iBAAiB;IACzBC,SAAO,EAAE5B,MAAA,CAAA6B;mIAId7C,mBAAA,WAAc,EACdC,mBAAA,CA6HM,OA7HN6C,WA6HM,GA5HJ9C,mBAAA,YAAe,EACJgB,MAAA,CAAAmB,WAAW,I,cAAtBrC,mBAAA,CA4CM,OA5CNiD,WA4CM,GA3CJ1C,YAAA,CA0CU2C,kBAAA;IAzCGC,MAAM,EAAAzC,QAAA,CACf,MAeM,CAfNP,mBAAA,CAeM,OAfNiD,WAeM,GAdJjD,mBAAA,CAKM,OALNkD,WAKM,GAJJlD,mBAAA,CAA+B,YAAAc,gBAAA,CAAxBC,MAAA,CAAAmB,WAAW,CAACiB,IAAI,kBACvB/C,YAAA,CAESgD,iBAAA;MAFAC,IAAI,EAAEtC,MAAA,CAAAuC,iBAAiB,CAACvC,MAAA,CAAAmB,WAAW,CAACC,MAAM;;MAlFrE7B,OAAA,EAAAC,QAAA,CAmFoB,MAA2C,CAnF/DG,gBAAA,CAAAI,gBAAA,CAmFuBC,MAAA,CAAAwC,iBAAiB,CAACxC,MAAA,CAAAmB,WAAW,CAACC,MAAM,kB;MAnF3D1B,CAAA;mCAsFgBT,mBAAA,CAOM,OAPNwD,WAOM,GANJpD,YAAA,CAIEqD,sBAAA;MAHCC,UAAU,EAAE3C,MAAA,CAAAmB,WAAW,CAACE,QAAQ;MAChCD,MAAM,EAAEpB,MAAA,CAAA4C,iBAAiB,CAAC5C,MAAA,CAAAmB,WAAW,CAACC,MAAM;MAC5C,cAAY,EAAE;uDAEjBnC,mBAAA,CAA8D,QAA9D4D,WAA8D,EAAA9C,gBAAA,CAA/BC,MAAA,CAAAmB,WAAW,CAACE,QAAQ,IAAG,GAAC,gB;IA5FzE9B,OAAA,EAAAC,QAAA,CAiGY,MAqBM,CArBNP,mBAAA,CAqBM,OArBN6D,WAqBM,GApBJzD,YAAA,CAmBS0D,iBAAA;MAnBAC,MAAM,EAAE;IAAE;MAlGjCzD,OAAA,EAAAC,QAAA,CAmGgB,MAKS,CALTH,YAAA,CAKS4D,iBAAA;QALAC,IAAI,EAAE;MAAC;QAnGhC3D,OAAA,EAAAC,QAAA,CAoGkB,MAGM,CAHNP,mBAAA,CAGM,OAHNkE,WAGM,G,0BAFJlE,mBAAA,CAAuC;UAAjCL,KAAK,EAAC;QAAc,GAAC,OAAK,sBAChCK,mBAAA,CAAsD,QAAtDmE,WAAsD,EAAArD,gBAAA,CAAxBC,MAAA,CAAAmB,WAAW,CAACkC,EAAE,iB;QAtGhE3D,CAAA;UAyGgBL,YAAA,CAKS4D,iBAAA;QALAC,IAAI,EAAE;MAAC;QAzGhC3D,OAAA,EAAAC,QAAA,CA0GkB,MAGM,CAHNP,mBAAA,CAGM,OAHNqE,WAGM,G,0BAFJrE,mBAAA,CAAuC;UAAjCL,KAAK,EAAC;QAAc,GAAC,OAAK,sBAChCK,mBAAA,CAAyE,QAAzEsE,WAAyE,EAAAxD,gBAAA,CAA3CC,MAAA,CAAAwD,UAAU,CAACxD,MAAA,CAAAmB,WAAW,CAACsC,SAAS,kB;QA5GlF/D,CAAA;UA+GgBL,YAAA,CAKS4D,iBAAA;QALAC,IAAI,EAAE;MAAC;QA/GhC3D,OAAA,EAAAC,QAAA,CAgHkB,MAGM,CAHNP,mBAAA,CAGM,OAHNyE,WAGM,G,4BAFJzE,mBAAA,CAAuC;UAAjCL,KAAK,EAAC;QAAc,GAAC,OAAK,sBAChCK,mBAAA,CAA0E,QAA1E0E,WAA0E,EAAA5D,gBAAA,CAA5CC,MAAA,CAAA4D,qBAAqB,CAAC5D,MAAA,CAAAmB,WAAW,kB;QAlHnFzB,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;UAAAV,mBAAA,gBA0HQA,mBAAA,YAAe,EACfC,mBAAA,CA2EM,OA3EN4E,WA2EM,GA1EJ7E,mBAAA,aAAgB,EACLgB,MAAA,CAAAuB,cAAc,I,cAAzBzC,mBAAA,CAeM,OAfNgF,WAeM,GAdJzE,YAAA,CASc0E,sBAAA;IATAC,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAR;;IACVC,QAAQ,EAAA1E,QAAA,CACjB,MAKM,CALNP,mBAAA,CAKM,OALNkF,WAKM,GAJJ9E,YAAA,CAA0E+E,2BAAA;MAAxDC,OAAO,EAAC,IAAI;MAACC,KAAwC,EAAxC;QAAA;QAAA;MAAA;QAC/BjF,YAAA,CAA4E+E,2BAAA;MAA1DC,OAAO,EAAC,MAAM;MAACC,KAAwC,EAAxC;QAAA;QAAA;MAAA;QACjCjF,YAAA,CAA4E+E,2BAAA;MAA1DC,OAAO,EAAC,MAAM;MAACC,KAAwC,EAAxC;QAAA;QAAA;MAAA;QACjCjF,YAAA,CAAuE+E,2BAAA;MAArDC,OAAO,EAAC,MAAM;MAACC,KAAmC,EAAnC;QAAA;QAAA;MAAA;;IApInD5E,CAAA;MAwIYT,mBAAA,CAGM,OAHNsF,WAGM,GAFJlF,YAAA,CAAmDC,kBAAA;IAA1CV,KAAK,EAAC;EAAc;IAzI3CW,OAAA,EAAAC,QAAA,CAyI4C,MAAW,CAAXH,YAAA,CAAWmF,kBAAA,E;IAzIvD9E,CAAA;kCAAAC,gBAAA,CAyIiE,gBAErD,G,OAIcK,MAAA,CAAAW,WAAW,U,cAA3B7B,mBAAA,CAOM2F,SAAA;IAtJhB5F,GAAA;EAAA,IA8IUG,mBAAA,eAAkB,EAClBC,mBAAA,CAOM,OAPNyF,WAOM,GANJrF,YAAA,CAKEsF,6BAAA;IAJCC,eAAc,EAAE5E,MAAA,CAAA6E,mBAAmB;IACnCC,aAAY,EAAE9E,MAAA,CAAA+E,iBAAiB;IAC/BC,gBAAe,EAAEhF,MAAA,CAAAiF,oBAAoB;IACrCC,cAAa,EAAElF,MAAA,CAAAmF;4JApJ9BnG,mBAAA,gBAwJUA,mBAAA,eAAkB,EACPgB,MAAA,CAAAW,WAAW,U,cAAtB7B,mBAAA,CAcM,OAdNsG,WAcM,GAZIpF,MAAA,CAAAqF,aAAa,I,cADrBC,YAAA,CAOEC,gCAAA;IAjKd1G,GAAA;IA4Je,SAAO,EAAEmB,MAAA,CAAAqF,aAAa;IACtB,YAAU,EAAE,IAAI;IAChBG,iBAAgB,EAAExF,MAAA,CAAAyF,qBAAqB;IACvCP,cAAa,EAAElF,MAAA,CAAA0F,2BAA2B;IAC1CC,kBAAiB,EAAE3F,MAAA,CAAA4F;sHAEtBN,YAAA,CAIWO,mBAAA;IAtKvBhH,GAAA;IAkK6BiC,WAAW,EAAC;;IAlKzCvB,OAAA,EAAAC,QAAA,CAmKc,MAEY,CAFZH,YAAA,CAEYyG,oBAAA;MAFDxD,IAAI,EAAC,SAAS;MAAEyD,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEjG,MAAA,CAAAW,WAAW;;MAnK3DpB,OAAA,EAAAC,QAAA,CAmKiE,MAEnDwG,MAAA,SAAAA,MAAA,QArKdrG,gBAAA,CAmKiE,QAEnD,E;MArKdD,CAAA;;IAAAA,CAAA;WAAAV,mBAAA,gBAyKUA,mBAAA,eAAkB,EACPgB,MAAA,CAAAW,WAAW,U,cAAtB7B,mBAAA,CAWM,OAXNoH,WAWM,GATIlG,MAAA,CAAAqF,aAAa,I,cADrBC,YAAA,CAIEa,mCAAA;IA/KdtH,GAAA;IA6Ke,SAAO,EAAEmB,MAAA,CAAAqF,aAAa;IACtBe,aAAY,EAAEpG,MAAA,CAAAqG;0EAEjBf,YAAA,CAIWO,mBAAA;IApLvBhH,GAAA;IAgL6BiC,WAAW,EAAC;;IAhLzCvB,OAAA,EAAAC,QAAA,CAiLc,MAEY,CAFZH,YAAA,CAEYyG,oBAAA;MAFDxD,IAAI,EAAC,SAAS;MAAEyD,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEjG,MAAA,CAAAW,WAAW;;MAjL3DpB,OAAA,EAAAC,QAAA,CAiLiE,MAEnDwG,MAAA,SAAAA,MAAA,QAnLdrG,gBAAA,CAiLiE,QAEnD,E;MAnLdD,CAAA;;IAAAA,CAAA;WAAAV,mBAAA,gBAuLUA,mBAAA,eAAkB,EACPgB,MAAA,CAAAW,WAAW,U,cAAtB7B,mBAAA,CAaM,OAbNwH,WAaM,GAXItG,MAAA,CAAAqF,aAAa,IAAIrF,MAAA,CAAAuG,UAAU,I,cADnCjB,YAAA,CAMEkB,mCAAA;IA/Ld3H,GAAA;IA2Le,SAAO,EAAEmB,MAAA,CAAAqF,aAAa;IACtB,aAAW,EAAErF,MAAA,CAAAuG,UAAU;IACvBE,cAAa,EAAEzG,MAAA,CAAA0G,kBAAkB;IACjCC,aAAY,EAAE3G,MAAA,CAAA4G;2GAEjBtB,YAAA,CAIWO,mBAAA;IApMvBhH,GAAA;IAgM6BiC,WAAW,EAAC;;IAhMzCvB,OAAA,EAAAC,QAAA,CAiMc,MAEY,CAFZH,YAAA,CAEYyG,oBAAA;MAFDxD,IAAI,EAAC,SAAS;MAAEyD,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEjG,MAAA,CAAAW,WAAW;;MAjM3DpB,OAAA,EAAAC,QAAA,CAiMiE,MAEnDwG,MAAA,SAAAA,MAAA,QAnMdrG,gBAAA,CAiMiE,QAEnD,E;MAnMdD,CAAA;;IAAAA,CAAA;WAAAV,mBAAA,e,OA0MIA,mBAAA,WAAc,EACdC,mBAAA,CAoBM,OApBN4H,WAoBM,GAnBJ5H,mBAAA,CAOM,OAPN6H,WAOM,G,4BANJ7H,mBAAA,CAAmB,cAAb,QAAM,sBACZI,YAAA,CAA+EgD,iBAAA;IAAtEC,IAAI,EAAEtC,MAAA,CAAA+G,YAAY,CAACzE,IAAI;IAAE0E,IAAI,EAAC;;IA9M/CzH,OAAA,EAAAC,QAAA,CA8MuD,MAAuB,CA9M9EG,gBAAA,CAAAI,gBAAA,CA8M0DC,MAAA,CAAA+G,YAAY,CAACE,IAAI,iB;IA9M3EvH,CAAA;2DA+MQT,mBAAA,CAAgC;IAA1BL,KAAK,EAAC;EAAW,GAAC,GAAC,sBACzBK,mBAAA,CAA0C,cAApC,QAAM,GAAAc,gBAAA,CAAGC,MAAA,CAAAkH,iBAAiB,kB,4BAChCjI,mBAAA,CAAgC;IAA1BL,KAAK,EAAC;EAAW,GAAC,GAAC,sBACzBK,mBAAA,CAAuC,cAAjC,QAAM,GAAAc,gBAAA,CAAGC,MAAA,CAAAmH,cAAc,iB,GAG/BlI,mBAAA,CASM,OATNmI,WASM,GARJ/H,YAAA,CAGYyG,oBAAA;IAHDkB,IAAI,EAAC,OAAO;IAAEjB,OAAK,EAAE/F,MAAA,CAAAqH;;IAtNxC9H,OAAA,EAAAC,QAAA,CAuNU,MAA8B,CAA9BH,YAAA,CAA8BC,kBAAA;MAvNxCC,OAAA,EAAAC,QAAA,CAuNmB,MAAW,CAAXH,YAAA,CAAWiI,kBAAA,E;MAvN9B5H,CAAA;oCAAAC,gBAAA,CAuNwC,MAEhC,G;IAzNRD,CAAA;kCA0NQL,YAAA,CAGYyG,oBAAA;IAHDkB,IAAI,EAAC,OAAO;IAAEjB,OAAK,EAAE/F,MAAA,CAAAuH;;IA1NxChI,OAAA,EAAAC,QAAA,CA2NU,MAAiC,CAAjCH,YAAA,CAAiCC,kBAAA;MA3N3CC,OAAA,EAAAC,QAAA,CA2NmB,MAAc,CAAdH,YAAA,CAAcmI,qBAAA,E;MA3NjC9H,CAAA;oCAAAC,gBAAA,CA2N2C,QAEnC,G;IA7NRD,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}