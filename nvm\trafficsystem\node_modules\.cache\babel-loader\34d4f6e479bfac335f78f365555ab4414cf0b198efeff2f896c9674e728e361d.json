{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, computed, watch, onMounted, onUnmounted } from 'vue';\nimport * as echarts from 'echarts';\nimport { TrendCharts, ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue';\nexport default {\n  name: 'TrafficTrendChart',\n  components: {\n    TrendCharts,\n    ArrowUp,\n    ArrowDown,\n    Minus\n  },\n  props: {\n    vehicleData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    movingAverages: {\n      type: Object,\n      required: true,\n      default: () => ({\n        frame5: 0,\n        frame10: 0,\n        frame30: 0\n      })\n    },\n    trendInfo: {\n      type: Object,\n      required: true,\n      default: () => ({\n        trend: 'stable',\n        strength: 0,\n        description: '车流稳定'\n      })\n    }\n  },\n  setup(props) {\n    const chartRef = ref(null);\n    let chartInstance = null;\n    const trendClass = computed(() => `trend-${props.trendInfo.trend}`);\n    const trendIcon = computed(() => {\n      switch (props.trendInfo.trend) {\n        case 'rising':\n          return ArrowUp;\n        case 'falling':\n          return ArrowDown;\n        default:\n          return Minus;\n      }\n    });\n    const initChart = () => {\n      if (!chartRef.value) return;\n      chartInstance = echarts.init(chartRef.value);\n      updateChart();\n    };\n    const updateChart = () => {\n      if (!chartInstance) return;\n      const data = props.vehicleData.slice(-20); // 显示最近20个数据点\n      const xAxisData = data.map((_, index) => `帧${index + 1}`);\n      const option = {\n        grid: {\n          top: 20,\n          right: 20,\n          bottom: 40,\n          left: 40\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData,\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: {\n              color: '#e8e8e8'\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '车辆数',\n          nameTextStyle: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: {\n              color: '#e8e8e8'\n            }\n          },\n          splitLine: {\n            lineStyle: {\n              color: '#f0f0f0'\n            }\n          }\n        },\n        series: [{\n          name: '实时车辆数',\n          type: 'line',\n          data: data,\n          smooth: true,\n          lineStyle: {\n            color: '#1890ff',\n            width: 2\n          },\n          itemStyle: {\n            color: '#1890ff'\n          },\n          areaStyle: {\n            color: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 0,\n              y2: 1,\n              colorStops: [{\n                offset: 0,\n                color: 'rgba(24, 144, 255, 0.3)'\n              }, {\n                offset: 1,\n                color: 'rgba(24, 144, 255, 0.05)'\n              }]\n            }\n          },\n          symbol: 'circle',\n          symbolSize: 4\n        }, {\n          name: '5帧平均',\n          type: 'line',\n          data: new Array(data.length).fill(props.movingAverages.frame5),\n          lineStyle: {\n            color: '#52c41a',\n            width: 1,\n            type: 'dashed'\n          },\n          symbol: 'none'\n        }, {\n          name: '10帧平均',\n          type: 'line',\n          data: new Array(data.length).fill(props.movingAverages.frame10),\n          lineStyle: {\n            color: '#faad14',\n            width: 1,\n            type: 'dashed'\n          },\n          symbol: 'none'\n        }],\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          textStyle: {\n            color: '#fff',\n            fontSize: 12\n          },\n          formatter: params => {\n            let result = `${params[0].axisValue}<br/>`;\n            params.forEach(param => {\n              result += `${param.seriesName}: ${param.value}辆<br/>`;\n            });\n            return result;\n          }\n        }\n      };\n      chartInstance.setOption(option);\n    };\n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize();\n      }\n    };\n    watch(() => [props.vehicleData, props.movingAverages], updateChart, {\n      deep: true\n    });\n    onMounted(() => {\n      initChart();\n      window.addEventListener('resize', resizeChart);\n    });\n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose();\n      }\n      window.removeEventListener('resize', resizeChart);\n    });\n    return {\n      chartRef,\n      trendClass,\n      trendIcon\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "watch", "onMounted", "onUnmounted", "echarts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ArrowUp", "ArrowDown", "Minus", "name", "components", "props", "vehicleData", "type", "Array", "required", "default", "movingAverages", "Object", "frame5", "frame10", "frame30", "trendInfo", "trend", "strength", "description", "setup", "chartRef", "chartInstance", "trendClass", "trendIcon", "initChart", "value", "init", "updateChart", "data", "slice", "xAxisData", "map", "_", "index", "option", "grid", "top", "right", "bottom", "left", "xAxis", "axisLabel", "fontSize", "color", "axisLine", "lineStyle", "yAxis", "nameTextStyle", "splitLine", "series", "smooth", "width", "itemStyle", "areaStyle", "x", "y", "x2", "y2", "colorStops", "offset", "symbol", "symbolSize", "length", "fill", "tooltip", "trigger", "backgroundColor", "textStyle", "formatter", "params", "result", "axisValue", "for<PERSON>ach", "param", "seriesName", "setOption", "resizeChart", "resize", "deep", "window", "addEventListener", "dispose", "removeEventListener"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\traffic\\TrafficTrendChart.vue"], "sourcesContent": ["<template>\n  <div class=\"traffic-trend-chart\">\n    <div class=\"chart-header\">\n      <h4>车流趋势分析</h4>\n      <div class=\"trend-indicator\" :class=\"trendClass\">\n        <el-icon>\n          <component :is=\"trendIcon\" />\n        </el-icon>\n        <span>{{ trendInfo.description }}</span>\n      </div>\n    </div>\n    \n    <div class=\"chart-container\">\n      <div ref=\"chartRef\" class=\"chart\"></div>\n    </div>\n    \n    <div class=\"moving-averages\">\n      <div class=\"average-item\">\n        <span class=\"average-label\">5帧平均:</span>\n        <span class=\"average-value\">{{ movingAverages.frame5 }}</span>\n      </div>\n      <div class=\"average-item\">\n        <span class=\"average-label\">10帧平均:</span>\n        <span class=\"average-value\">{{ movingAverages.frame10 }}</span>\n      </div>\n      <div class=\"average-item\">\n        <span class=\"average-label\">30帧平均:</span>\n        <span class=\"average-value\">{{ movingAverages.frame30 }}</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, watch, onMounted, onUnmounted } from 'vue'\nimport * as echarts from 'echarts'\nimport { TrendCharts, ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'TrafficTrendChart',\n  components: {\n    TrendCharts,\n    ArrowUp,\n    ArrowDown,\n    Minus\n  },\n  props: {\n    vehicleData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    movingAverages: {\n      type: Object,\n      required: true,\n      default: () => ({ frame5: 0, frame10: 0, frame30: 0 })\n    },\n    trendInfo: {\n      type: Object,\n      required: true,\n      default: () => ({ trend: 'stable', strength: 0, description: '车流稳定' })\n    }\n  },\n  setup(props) {\n    const chartRef = ref(null)\n    let chartInstance = null\n    \n    const trendClass = computed(() => `trend-${props.trendInfo.trend}`)\n    \n    const trendIcon = computed(() => {\n      switch (props.trendInfo.trend) {\n        case 'rising': return ArrowUp\n        case 'falling': return ArrowDown\n        default: return Minus\n      }\n    })\n    \n    const initChart = () => {\n      if (!chartRef.value) return\n      \n      chartInstance = echarts.init(chartRef.value)\n      updateChart()\n    }\n    \n    const updateChart = () => {\n      if (!chartInstance) return\n      \n      const data = props.vehicleData.slice(-20) // 显示最近20个数据点\n      const xAxisData = data.map((_, index) => `帧${index + 1}`)\n      \n      const option = {\n        grid: {\n          top: 20,\n          right: 20,\n          bottom: 40,\n          left: 40\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData,\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: { color: '#e8e8e8' }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '车辆数',\n          nameTextStyle: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLabel: {\n            fontSize: 10,\n            color: '#666'\n          },\n          axisLine: {\n            lineStyle: { color: '#e8e8e8' }\n          },\n          splitLine: {\n            lineStyle: { color: '#f0f0f0' }\n          }\n        },\n        series: [\n          {\n            name: '实时车辆数',\n            type: 'line',\n            data: data,\n            smooth: true,\n            lineStyle: {\n              color: '#1890ff',\n              width: 2\n            },\n            itemStyle: {\n              color: '#1890ff'\n            },\n            areaStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },\n                  { offset: 1, color: 'rgba(24, 144, 255, 0.05)' }\n                ]\n              }\n            },\n            symbol: 'circle',\n            symbolSize: 4\n          },\n          {\n            name: '5帧平均',\n            type: 'line',\n            data: new Array(data.length).fill(props.movingAverages.frame5),\n            lineStyle: {\n              color: '#52c41a',\n              width: 1,\n              type: 'dashed'\n            },\n            symbol: 'none'\n          },\n          {\n            name: '10帧平均',\n            type: 'line',\n            data: new Array(data.length).fill(props.movingAverages.frame10),\n            lineStyle: {\n              color: '#faad14',\n              width: 1,\n              type: 'dashed'\n            },\n            symbol: 'none'\n          }\n        ],\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          textStyle: { color: '#fff', fontSize: 12 },\n          formatter: (params) => {\n            let result = `${params[0].axisValue}<br/>`\n            params.forEach(param => {\n              result += `${param.seriesName}: ${param.value}辆<br/>`\n            })\n            return result\n          }\n        }\n      }\n      \n      chartInstance.setOption(option)\n    }\n    \n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize()\n      }\n    }\n    \n    watch(() => [props.vehicleData, props.movingAverages], updateChart, { deep: true })\n    \n    onMounted(() => {\n      initChart()\n      window.addEventListener('resize', resizeChart)\n    })\n    \n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose()\n      }\n      window.removeEventListener('resize', resizeChart)\n    })\n    \n    return {\n      chartRef,\n      trendClass,\n      trendIcon\n    }\n  }\n}\n</script>\n\n<style scoped>\n.traffic-trend-chart {\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e8e8e8;\n  overflow: hidden;\n}\n\n.chart-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.chart-header h4 {\n  margin: 0;\n  font-size: 16px;\n  color: #333;\n}\n\n.trend-indicator {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 4px 8px;\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.trend-rising {\n  background: #f6ffed;\n  color: #52c41a;\n}\n\n.trend-falling {\n  background: #fff2e8;\n  color: #fa8c16;\n}\n\n.trend-stable {\n  background: #e6f7ff;\n  color: #1890ff;\n}\n\n.chart-container {\n  padding: 20px;\n}\n\n.chart {\n  width: 100%;\n  height: 200px;\n}\n\n.moving-averages {\n  display: flex;\n  justify-content: space-around;\n  padding: 16px 20px;\n  background: #fafafa;\n  border-top: 1px solid #e8e8e8;\n}\n\n.average-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 4px;\n}\n\n.average-label {\n  font-size: 12px;\n  color: #666;\n}\n\n.average-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .chart-header {\n    flex-direction: column;\n    gap: 8px;\n    align-items: flex-start;\n  }\n  \n  .chart {\n    height: 150px;\n  }\n  \n  .moving-averages {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .average-item {\n    flex-direction: row;\n    justify-content: space-between;\n  }\n}\n</style>\n"], "mappings": ";;;AAkCA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AACjE,OAAO,KAAKC,OAAM,MAAO,SAAQ;AACjC,SAASC,WAAW,EAAEC,OAAO,EAAEC,SAAS,EAAEC,KAAI,QAAS,yBAAwB;AAE/E,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,UAAU,EAAE;IACVL,WAAW;IACXC,OAAO;IACPC,SAAS;IACTC;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,WAAW,EAAE;MACXC,IAAI,EAAEC,KAAK;MACXC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAEA,CAAA,KAAM;IACjB,CAAC;IACDC,cAAc,EAAE;MACdJ,IAAI,EAAEK,MAAM;MACZH,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAEA,CAAA,MAAO;QAAEG,MAAM,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAC;IACvD,CAAC;IACDC,SAAS,EAAE;MACTT,IAAI,EAAEK,MAAM;MACZH,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAEA,CAAA,MAAO;QAAEO,KAAK,EAAE,QAAQ;QAAEC,QAAQ,EAAE,CAAC;QAAEC,WAAW,EAAE;MAAO,CAAC;IACvE;EACF,CAAC;EACDC,KAAKA,CAACf,KAAK,EAAE;IACX,MAAMgB,QAAO,GAAI5B,GAAG,CAAC,IAAI;IACzB,IAAI6B,aAAY,GAAI,IAAG;IAEvB,MAAMC,UAAS,GAAI7B,QAAQ,CAAC,MAAM,SAASW,KAAK,CAACW,SAAS,CAACC,KAAK,EAAE;IAElE,MAAMO,SAAQ,GAAI9B,QAAQ,CAAC,MAAM;MAC/B,QAAQW,KAAK,CAACW,SAAS,CAACC,KAAK;QAC3B,KAAK,QAAQ;UAAE,OAAOjB,OAAM;QAC5B,KAAK,SAAS;UAAE,OAAOC,SAAQ;QAC/B;UAAS,OAAOC,KAAI;MACtB;IACF,CAAC;IAED,MAAMuB,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAI,CAACJ,QAAQ,CAACK,KAAK,EAAE;MAErBJ,aAAY,GAAIxB,OAAO,CAAC6B,IAAI,CAACN,QAAQ,CAACK,KAAK;MAC3CE,WAAW,CAAC;IACd;IAEA,MAAMA,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAI,CAACN,aAAa,EAAE;MAEpB,MAAMO,IAAG,GAAIxB,KAAK,CAACC,WAAW,CAACwB,KAAK,CAAC,CAAC,EAAE,GAAE;MAC1C,MAAMC,SAAQ,GAAIF,IAAI,CAACG,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK,IAAIA,KAAI,GAAI,CAAC,EAAE;MAExD,MAAMC,MAAK,GAAI;QACbC,IAAI,EAAE;UACJC,GAAG,EAAE,EAAE;UACPC,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE;QACR,CAAC;QACDC,KAAK,EAAE;UACLlC,IAAI,EAAE,UAAU;UAChBsB,IAAI,EAAEE,SAAS;UACfW,SAAS,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE;UACT,CAAC;UACDC,QAAQ,EAAE;YACRC,SAAS,EAAE;cAAEF,KAAK,EAAE;YAAU;UAChC;QACF,CAAC;QACDG,KAAK,EAAE;UACLxC,IAAI,EAAE,OAAO;UACbJ,IAAI,EAAE,KAAK;UACX6C,aAAa,EAAE;YACbL,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE;UACT,CAAC;UACDF,SAAS,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE;UACT,CAAC;UACDC,QAAQ,EAAE;YACRC,SAAS,EAAE;cAAEF,KAAK,EAAE;YAAU;UAChC,CAAC;UACDK,SAAS,EAAE;YACTH,SAAS,EAAE;cAAEF,KAAK,EAAE;YAAU;UAChC;QACF,CAAC;QACDM,MAAM,EAAE,CACN;UACE/C,IAAI,EAAE,OAAO;UACbI,IAAI,EAAE,MAAM;UACZsB,IAAI,EAAEA,IAAI;UACVsB,MAAM,EAAE,IAAI;UACZL,SAAS,EAAE;YACTF,KAAK,EAAE,SAAS;YAChBQ,KAAK,EAAE;UACT,CAAC;UACDC,SAAS,EAAE;YACTT,KAAK,EAAE;UACT,CAAC;UACDU,SAAS,EAAE;YACTV,KAAK,EAAE;cACLrC,IAAI,EAAE,QAAQ;cACdgD,CAAC,EAAE,CAAC;cACJC,CAAC,EAAE,CAAC;cACJC,EAAE,EAAE,CAAC;cACLC,EAAE,EAAE,CAAC;cACLC,UAAU,EAAE,CACV;gBAAEC,MAAM,EAAE,CAAC;gBAAEhB,KAAK,EAAE;cAA0B,CAAC,EAC/C;gBAAEgB,MAAM,EAAE,CAAC;gBAAEhB,KAAK,EAAE;cAA2B;YAEnD;UACF,CAAC;UACDiB,MAAM,EAAE,QAAQ;UAChBC,UAAU,EAAE;QACd,CAAC,EACD;UACE3D,IAAI,EAAE,MAAM;UACZI,IAAI,EAAE,MAAM;UACZsB,IAAI,EAAE,IAAIrB,KAAK,CAACqB,IAAI,CAACkC,MAAM,CAAC,CAACC,IAAI,CAAC3D,KAAK,CAACM,cAAc,CAACE,MAAM,CAAC;UAC9DiC,SAAS,EAAE;YACTF,KAAK,EAAE,SAAS;YAChBQ,KAAK,EAAE,CAAC;YACR7C,IAAI,EAAE;UACR,CAAC;UACDsD,MAAM,EAAE;QACV,CAAC,EACD;UACE1D,IAAI,EAAE,OAAO;UACbI,IAAI,EAAE,MAAM;UACZsB,IAAI,EAAE,IAAIrB,KAAK,CAACqB,IAAI,CAACkC,MAAM,CAAC,CAACC,IAAI,CAAC3D,KAAK,CAACM,cAAc,CAACG,OAAO,CAAC;UAC/DgC,SAAS,EAAE;YACTF,KAAK,EAAE,SAAS;YAChBQ,KAAK,EAAE,CAAC;YACR7C,IAAI,EAAE;UACR,CAAC;UACDsD,MAAM,EAAE;QACV,EACD;QACDI,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,eAAe,EAAE,oBAAoB;UACrCC,SAAS,EAAE;YAAExB,KAAK,EAAE,MAAM;YAAED,QAAQ,EAAE;UAAG,CAAC;UAC1C0B,SAAS,EAAGC,MAAM,IAAK;YACrB,IAAIC,MAAK,GAAI,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACE,SAAS,OAAM;YACzCF,MAAM,CAACG,OAAO,CAACC,KAAI,IAAK;cACtBH,MAAK,IAAK,GAAGG,KAAK,CAACC,UAAU,KAAKD,KAAK,CAAChD,KAAK,QAAO;YACtD,CAAC;YACD,OAAO6C,MAAK;UACd;QACF;MACF;MAEAjD,aAAa,CAACsD,SAAS,CAACzC,MAAM;IAChC;IAEA,MAAM0C,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAIvD,aAAa,EAAE;QACjBA,aAAa,CAACwD,MAAM,CAAC;MACvB;IACF;IAEAnF,KAAK,CAAC,MAAM,CAACU,KAAK,CAACC,WAAW,EAAED,KAAK,CAACM,cAAc,CAAC,EAAEiB,WAAW,EAAE;MAAEmD,IAAI,EAAE;IAAK,CAAC;IAElFnF,SAAS,CAAC,MAAM;MACd6B,SAAS,CAAC;MACVuD,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEJ,WAAW;IAC/C,CAAC;IAEDhF,WAAW,CAAC,MAAM;MAChB,IAAIyB,aAAa,EAAE;QACjBA,aAAa,CAAC4D,OAAO,CAAC;MACxB;MACAF,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEN,WAAW;IAClD,CAAC;IAED,OAAO;MACLxD,QAAQ;MACRE,UAAU;MACVC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}