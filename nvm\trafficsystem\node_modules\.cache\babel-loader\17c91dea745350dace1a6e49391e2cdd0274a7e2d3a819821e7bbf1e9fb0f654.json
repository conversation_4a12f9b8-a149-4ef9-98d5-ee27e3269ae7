{"ast": null, "code": "import { toDisplayString as _toDisplayString, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"congestion-grade-indicator\"\n};\nconst _hoisted_2 = {\n  class: \"grade-display\"\n};\nconst _hoisted_3 = {\n  class: \"grade-info\"\n};\nconst _hoisted_4 = {\n  class: \"grade-label\"\n};\nconst _hoisted_5 = {\n  class: \"grade-description\"\n};\nconst _hoisted_6 = {\n  class: \"progress-container\"\n};\nconst _hoisted_7 = {\n  class: \"progress-bar\"\n};\nconst _hoisted_8 = {\n  class: \"progress-text\"\n};\nconst _hoisted_9 = {\n  class: \"vehicle-count\"\n};\nconst _hoisted_10 = {\n  class: \"count-number\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", {\n    class: \"grade-badge\",\n    style: _normalizeStyle({\n      backgroundColor: $setup.gradeConfig.color,\n      boxShadow: `0 0 20px ${$setup.gradeConfig.color}40`\n    })\n  }, _toDisplayString($props.grade), 5 /* TEXT, STYLE */), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.gradeConfig.label), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.gradeConfig.description), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", {\n    class: \"progress-fill\",\n    style: _normalizeStyle({\n      width: `${$setup.percentage}%`,\n      backgroundColor: $setup.gradeConfig.color\n    })\n  }, null, 4 /* STYLE */)]), _createElementVNode(\"div\", _hoisted_8, \"拥挤度: \" + _toDisplayString($setup.percentage.toFixed(1)) + \"%\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"span\", _hoisted_10, _toDisplayString($props.vehicleCount), 1 /* TEXT */), _cache[0] || (_cache[0] = _createElementVNode(\"span\", {\n    class: \"count-label\"\n  }, \"辆车\", -1 /* HOISTED */))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "style", "_normalizeStyle", "$setup", "gradeConfig", "color", "$props", "grade", "_hoisted_3", "_hoisted_4", "_toDisplayString", "label", "_hoisted_5", "description", "_hoisted_6", "_hoisted_7", "percentage", "_hoisted_8", "toFixed", "_hoisted_9", "_hoisted_10", "vehicleCount"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\traffic\\CongestionGradeIndicator.vue"], "sourcesContent": ["<template>\n  <div class=\"congestion-grade-indicator\">\n    <div class=\"grade-display\">\n      <div \n        class=\"grade-badge\"\n        :style=\"{ \n          backgroundColor: gradeConfig.color,\n          boxShadow: `0 0 20px ${gradeConfig.color}40`\n        }\"\n      >\n        {{ grade }}\n      </div>\n      <div class=\"grade-info\">\n        <div class=\"grade-label\">{{ gradeConfig.label }}</div>\n        <div class=\"grade-description\">{{ gradeConfig.description }}</div>\n      </div>\n    </div>\n    \n    <div class=\"progress-container\">\n      <div class=\"progress-bar\">\n        <div \n          class=\"progress-fill\"\n          :style=\"{ \n            width: `${percentage}%`,\n            backgroundColor: gradeConfig.color\n          }\"\n        ></div>\n      </div>\n      <div class=\"progress-text\">拥挤度: {{ percentage.toFixed(1) }}%</div>\n    </div>\n    \n    <div class=\"vehicle-count\">\n      <span class=\"count-number\">{{ vehicleCount }}</span>\n      <span class=\"count-label\">辆车</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { getCongestionGradeConfig, calculateCongestionPercentage } from '@/utils/trafficAnalysisUtils'\n\nexport default {\n  name: 'CongestionGradeIndicator',\n  props: {\n    grade: {\n      type: String,\n      required: true,\n      validator: (value) => ['A', 'B', 'C', 'D', 'E', 'F'].includes(value)\n    },\n    vehicleCount: {\n      type: Number,\n      required: true,\n      default: 0\n    }\n  },\n  setup(props) {\n    const gradeConfig = computed(() => getCongestionGradeConfig(props.grade))\n    const percentage = computed(() => calculateCongestionPercentage(props.grade, props.vehicleCount))\n    \n    return {\n      gradeConfig,\n      percentage\n    }\n  }\n}\n</script>\n\n<style scoped>\n.congestion-grade-indicator {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e8e8e8;\n}\n\n.grade-display {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.grade-badge {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 24px;\n  font-weight: bold;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n  transition: all 0.3s ease;\n}\n\n.grade-info {\n  flex: 1;\n}\n\n.grade-label {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.grade-description {\n  font-size: 14px;\n  color: #666;\n}\n\n.progress-container {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.progress-bar {\n  height: 8px;\n  background: #f0f0f0;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n}\n\n.progress-fill {\n  height: 100%;\n  border-radius: 4px;\n  transition: width 0.5s ease, background-color 0.3s ease;\n  position: relative;\n}\n\n.progress-fill::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  animation: shimmer 2s infinite;\n}\n\n@keyframes shimmer {\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n}\n\n.progress-text {\n  font-size: 12px;\n  color: #666;\n  text-align: center;\n}\n\n.vehicle-count {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  gap: 4px;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n\n.count-number {\n  font-size: 28px;\n  font-weight: bold;\n  color: #333;\n}\n\n.count-label {\n  font-size: 14px;\n  color: #666;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .congestion-grade-indicator {\n    padding: 16px;\n  }\n  \n  .grade-badge {\n    width: 50px;\n    height: 50px;\n    font-size: 20px;\n  }\n  \n  .grade-label {\n    font-size: 16px;\n  }\n  \n  .count-number {\n    font-size: 24px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAe;;EAUnBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAmB;;EAI7BA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAc;;EASpBA,KAAK,EAAC;AAAe;;EAGvBA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAc;;uBA/B9BC,mBAAA,CAkCM,OAlCNC,UAkCM,GAjCJC,mBAAA,CAcM,OAdNC,UAcM,GAbJD,mBAAA,CAQM;IAPJH,KAAK,EAAC,aAAa;IAClBK,KAAK,EALdC,eAAA;uBAK8CC,MAAA,CAAAC,WAAW,CAACC,KAAK;6BAAmCF,MAAA,CAAAC,WAAW,CAACC,KAAK;;sBAKxGC,MAAA,CAAAC,KAAK,yBAEVR,mBAAA,CAGM,OAHNS,UAGM,GAFJT,mBAAA,CAAsD,OAAtDU,UAAsD,EAAAC,gBAAA,CAA1BP,MAAA,CAAAC,WAAW,CAACO,KAAK,kBAC7CZ,mBAAA,CAAkE,OAAlEa,UAAkE,EAAAF,gBAAA,CAAhCP,MAAA,CAAAC,WAAW,CAACS,WAAW,iB,KAI7Dd,mBAAA,CAWM,OAXNe,UAWM,GAVJf,mBAAA,CAQM,OARNgB,UAQM,GAPJhB,mBAAA,CAMO;IALLH,KAAK,EAAC,eAAe;IACpBK,KAAK,EAtBhBC,eAAA;gBAsB2CC,MAAA,CAAAa,UAAU;uBAAkCb,MAAA,CAAAC,WAAW,CAACC;;6BAM7FN,mBAAA,CAAkE,OAAlEkB,UAAkE,EAAvC,OAAK,GAAAP,gBAAA,CAAGP,MAAA,CAAAa,UAAU,CAACE,OAAO,OAAM,GAAC,gB,GAG9DnB,mBAAA,CAGM,OAHNoB,UAGM,GAFJpB,mBAAA,CAAoD,QAApDqB,WAAoD,EAAAV,gBAAA,CAAtBJ,MAAA,CAAAe,YAAY,kB,0BAC1CtB,mBAAA,CAAmC;IAA7BH,KAAK,EAAC;EAAa,GAAC,IAAE,qB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}