{"ast": null, "code": "import { ref, computed } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { Warning } from '@element-plus/icons-vue';\nexport default {\n  name: 'StrategyRecommendation',\n  components: {\n    Warning\n  },\n  props: {\n    strategy: {\n      type: Object,\n      required: true,\n      default: () => ({\n        primary: '',\n        secondary: '',\n        icon: '📋',\n        priority: 'low'\n      })\n    },\n    showActions: {\n      type: Boolean,\n      default: true\n    },\n    grade: {\n      type: String,\n      default: 'A'\n    }\n  },\n  emits: ['apply-strategy', 'view-details'],\n  setup(props, {\n    emit\n  }) {\n    const applying = ref(false);\n    const showDetails = ref(false);\n    const priorityTagType = computed(() => {\n      switch (props.strategy.priority) {\n        case 'critical':\n          return 'danger';\n        case 'high':\n          return 'warning';\n        case 'medium':\n          return 'primary';\n        default:\n          return 'success';\n      }\n    });\n    const priorityText = computed(() => {\n      switch (props.strategy.priority) {\n        case 'critical':\n          return '紧急';\n        case 'high':\n          return '高优先级';\n        case 'medium':\n          return '中优先级';\n        default:\n          return '低优先级';\n      }\n    });\n    const implementationSteps = computed(() => {\n      const stepMap = {\n        A: ['保持当前信号配时', '继续常规监控'],\n        B: ['维持现有交通管制', '准备应急预案'],\n        C: ['调整信号灯配时', '增加交通引导标识', '启动实时监控'],\n        D: ['延长主要方向绿灯时间', '部署交通协管员', '开启可变车道'],\n        E: ['启动应急交通管制', '派遣现场交警', '发布交通预警'],\n        F: ['实施全面交通管制', '启用替代路线', '协调应急部门']\n      };\n      return stepMap[props.grade] || stepMap.A;\n    });\n    const expectedResults = computed(() => {\n      const resultMap = {\n        A: ['维持良好通行状态', '预防拥堵发生'],\n        B: ['保持交通顺畅', '及时应对突发情况'],\n        C: ['缓解轻微拥堵', '提高通行效率'],\n        D: ['显著改善交通状况', '减少等待时间'],\n        E: ['快速疏散车流', '恢复正常通行'],\n        F: ['最大化道路通行能力', '避免交通瘫痪']\n      };\n      return resultMap[props.grade] || resultMap.A;\n    });\n    const warnings = computed(() => {\n      const warningMap = {\n        A: [],\n        B: ['注意监控车流变化'],\n        C: ['避免过度调整信号配时'],\n        D: ['确保其他方向车辆安全', '注意行人通行'],\n        E: ['协调各部门行动', '确保应急车辆通行'],\n        F: ['及时发布公告', '做好长期管制准备']\n      };\n      return warningMap[props.grade] || [];\n    });\n    const handleApplyStrategy = async () => {\n      applying.value = true;\n      try {\n        // 模拟应用策略的过程\n        await new Promise(resolve => setTimeout(resolve, 1500));\n        ElMessage.success('策略应用成功');\n        emit('apply-strategy', props.strategy);\n      } catch (error) {\n        ElMessage.error('策略应用失败');\n      } finally {\n        applying.value = false;\n      }\n    };\n    const handleViewDetails = () => {\n      showDetails.value = !showDetails.value;\n      emit('view-details', {\n        strategy: props.strategy,\n        showDetails: showDetails.value\n      });\n    };\n    return {\n      applying,\n      showDetails,\n      priorityTagType,\n      priorityText,\n      implementationSteps,\n      expectedResults,\n      warnings,\n      handleApplyStrategy,\n      handleViewDetails\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "ElMessage", "Warning", "name", "components", "props", "strategy", "type", "Object", "required", "default", "primary", "secondary", "icon", "priority", "showActions", "Boolean", "grade", "String", "emits", "setup", "emit", "applying", "showDetails", "priorityTagType", "priorityText", "implementationSteps", "stepMap", "A", "B", "C", "D", "E", "F", "expectedResults", "resultMap", "warnings", "warningMap", "handleApplyStrategy", "value", "Promise", "resolve", "setTimeout", "success", "error", "handleViewDetails"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\traffic\\StrategyRecommendation.vue"], "sourcesContent": ["<template>\n  <div class=\"strategy-recommendation\">\n    <div class=\"strategy-header\">\n      <h4>交通管理建议</h4>\n      <el-tag \n        :type=\"priorityTagType\" \n        size=\"small\"\n        class=\"priority-tag\"\n      >\n        {{ priorityText }}\n      </el-tag>\n    </div>\n    \n    <div class=\"strategy-content\">\n      <div class=\"strategy-main\">\n        <div class=\"strategy-icon\">{{ strategy.icon }}</div>\n        <div class=\"strategy-info\">\n          <div class=\"strategy-primary\">{{ strategy.primary }}</div>\n          <div class=\"strategy-secondary\">{{ strategy.secondary }}</div>\n        </div>\n      </div>\n      \n      <div class=\"strategy-actions\" v-if=\"showActions\">\n        <el-button \n          type=\"primary\" \n          size=\"small\"\n          @click=\"handleApplyStrategy\"\n          :loading=\"applying\"\n        >\n          应用建议\n        </el-button>\n        <el-button \n          size=\"small\"\n          @click=\"handleViewDetails\"\n        >\n          查看详情\n        </el-button>\n      </div>\n    </div>\n    \n    <!-- 详细信息展开区域 -->\n    <el-collapse-transition>\n      <div v-show=\"showDetails\" class=\"strategy-details\">\n        <div class=\"detail-section\">\n          <h5>实施步骤:</h5>\n          <ol class=\"implementation-steps\">\n            <li v-for=\"step in implementationSteps\" :key=\"step\">{{ step }}</li>\n          </ol>\n        </div>\n        \n        <div class=\"detail-section\">\n          <h5>预期效果:</h5>\n          <ul class=\"expected-results\">\n            <li v-for=\"result in expectedResults\" :key=\"result\">{{ result }}</li>\n          </ul>\n        </div>\n        \n        <div class=\"detail-section\" v-if=\"warnings.length > 0\">\n          <h5>注意事项:</h5>\n          <ul class=\"warnings\">\n            <li v-for=\"warning in warnings\" :key=\"warning\" class=\"warning-item\">\n              <el-icon><Warning /></el-icon>\n              {{ warning }}\n            </li>\n          </ul>\n        </div>\n      </div>\n    </el-collapse-transition>\n  </div>\n</template>\n\n<script>\nimport { ref, computed } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport { Warning } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'StrategyRecommendation',\n  components: {\n    Warning\n  },\n  props: {\n    strategy: {\n      type: Object,\n      required: true,\n      default: () => ({\n        primary: '',\n        secondary: '',\n        icon: '📋',\n        priority: 'low'\n      })\n    },\n    showActions: {\n      type: Boolean,\n      default: true\n    },\n    grade: {\n      type: String,\n      default: 'A'\n    }\n  },\n  emits: ['apply-strategy', 'view-details'],\n  setup(props, { emit }) {\n    const applying = ref(false)\n    const showDetails = ref(false)\n    \n    const priorityTagType = computed(() => {\n      switch (props.strategy.priority) {\n        case 'critical': return 'danger'\n        case 'high': return 'warning'\n        case 'medium': return 'primary'\n        default: return 'success'\n      }\n    })\n    \n    const priorityText = computed(() => {\n      switch (props.strategy.priority) {\n        case 'critical': return '紧急'\n        case 'high': return '高优先级'\n        case 'medium': return '中优先级'\n        default: return '低优先级'\n      }\n    })\n    \n    const implementationSteps = computed(() => {\n      const stepMap = {\n        A: ['保持当前信号配时', '继续常规监控'],\n        B: ['维持现有交通管制', '准备应急预案'],\n        C: ['调整信号灯配时', '增加交通引导标识', '启动实时监控'],\n        D: ['延长主要方向绿灯时间', '部署交通协管员', '开启可变车道'],\n        E: ['启动应急交通管制', '派遣现场交警', '发布交通预警'],\n        F: ['实施全面交通管制', '启用替代路线', '协调应急部门']\n      }\n      return stepMap[props.grade] || stepMap.A\n    })\n    \n    const expectedResults = computed(() => {\n      const resultMap = {\n        A: ['维持良好通行状态', '预防拥堵发生'],\n        B: ['保持交通顺畅', '及时应对突发情况'],\n        C: ['缓解轻微拥堵', '提高通行效率'],\n        D: ['显著改善交通状况', '减少等待时间'],\n        E: ['快速疏散车流', '恢复正常通行'],\n        F: ['最大化道路通行能力', '避免交通瘫痪']\n      }\n      return resultMap[props.grade] || resultMap.A\n    })\n    \n    const warnings = computed(() => {\n      const warningMap = {\n        A: [],\n        B: ['注意监控车流变化'],\n        C: ['避免过度调整信号配时'],\n        D: ['确保其他方向车辆安全', '注意行人通行'],\n        E: ['协调各部门行动', '确保应急车辆通行'],\n        F: ['及时发布公告', '做好长期管制准备']\n      }\n      return warningMap[props.grade] || []\n    })\n    \n    const handleApplyStrategy = async () => {\n      applying.value = true\n      try {\n        // 模拟应用策略的过程\n        await new Promise(resolve => setTimeout(resolve, 1500))\n        ElMessage.success('策略应用成功')\n        emit('apply-strategy', props.strategy)\n      } catch (error) {\n        ElMessage.error('策略应用失败')\n      } finally {\n        applying.value = false\n      }\n    }\n    \n    const handleViewDetails = () => {\n      showDetails.value = !showDetails.value\n      emit('view-details', { strategy: props.strategy, showDetails: showDetails.value })\n    }\n    \n    return {\n      applying,\n      showDetails,\n      priorityTagType,\n      priorityText,\n      implementationSteps,\n      expectedResults,\n      warnings,\n      handleApplyStrategy,\n      handleViewDetails\n    }\n  }\n}\n</script>\n\n<style scoped>\n.strategy-recommendation {\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e8e8e8;\n  overflow: hidden;\n}\n\n.strategy-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.strategy-header h4 {\n  margin: 0;\n  font-size: 16px;\n  color: #333;\n}\n\n.priority-tag {\n  font-weight: 500;\n}\n\n.strategy-content {\n  padding: 20px;\n}\n\n.strategy-main {\n  display: flex;\n  align-items: flex-start;\n  gap: 16px;\n  margin-bottom: 16px;\n}\n\n.strategy-icon {\n  font-size: 32px;\n  line-height: 1;\n}\n\n.strategy-info {\n  flex: 1;\n}\n\n.strategy-primary {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.strategy-secondary {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n}\n\n.strategy-actions {\n  display: flex;\n  gap: 12px;\n  justify-content: flex-end;\n}\n\n.strategy-details {\n  padding: 20px;\n  background: #fafafa;\n  border-top: 1px solid #e8e8e8;\n}\n\n.detail-section {\n  margin-bottom: 20px;\n}\n\n.detail-section:last-child {\n  margin-bottom: 0;\n}\n\n.detail-section h5 {\n  margin: 0 0 12px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n}\n\n.implementation-steps,\n.expected-results,\n.warnings {\n  margin: 0;\n  padding-left: 20px;\n}\n\n.implementation-steps li,\n.expected-results li {\n  margin-bottom: 8px;\n  color: #666;\n  line-height: 1.5;\n}\n\n.warnings {\n  list-style: none;\n  padding-left: 0;\n}\n\n.warning-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n  color: #fa8c16;\n  font-size: 14px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .strategy-header {\n    flex-direction: column;\n    gap: 8px;\n    align-items: flex-start;\n  }\n  \n  .strategy-main {\n    flex-direction: column;\n    gap: 12px;\n  }\n  \n  .strategy-actions {\n    flex-direction: column;\n  }\n  \n  .strategy-actions .el-button {\n    width: 100%;\n  }\n}\n</style>\n"], "mappings": "AAwEA,SAASA,GAAG,EAAEC,QAAO,QAAS,KAAI;AAClC,SAASC,SAAQ,QAAS,cAAa;AACvC,SAASC,OAAM,QAAS,yBAAwB;AAEhD,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAEA,CAAA,MAAO;QACdC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC;IACDC,WAAW,EAAE;MACXR,IAAI,EAAES,OAAO;MACbN,OAAO,EAAE;IACX,CAAC;IACDO,KAAK,EAAE;MACLV,IAAI,EAAEW,MAAM;MACZR,OAAO,EAAE;IACX;EACF,CAAC;EACDS,KAAK,EAAE,CAAC,gBAAgB,EAAE,cAAc,CAAC;EACzCC,KAAKA,CAACf,KAAK,EAAE;IAAEgB;EAAK,CAAC,EAAE;IACrB,MAAMC,QAAO,GAAIvB,GAAG,CAAC,KAAK;IAC1B,MAAMwB,WAAU,GAAIxB,GAAG,CAAC,KAAK;IAE7B,MAAMyB,eAAc,GAAIxB,QAAQ,CAAC,MAAM;MACrC,QAAQK,KAAK,CAACC,QAAQ,CAACQ,QAAQ;QAC7B,KAAK,UAAU;UAAE,OAAO,QAAO;QAC/B,KAAK,MAAM;UAAE,OAAO,SAAQ;QAC5B,KAAK,QAAQ;UAAE,OAAO,SAAQ;QAC9B;UAAS,OAAO,SAAQ;MAC1B;IACF,CAAC;IAED,MAAMW,YAAW,GAAIzB,QAAQ,CAAC,MAAM;MAClC,QAAQK,KAAK,CAACC,QAAQ,CAACQ,QAAQ;QAC7B,KAAK,UAAU;UAAE,OAAO,IAAG;QAC3B,KAAK,MAAM;UAAE,OAAO,MAAK;QACzB,KAAK,QAAQ;UAAE,OAAO,MAAK;QAC3B;UAAS,OAAO,MAAK;MACvB;IACF,CAAC;IAED,MAAMY,mBAAkB,GAAI1B,QAAQ,CAAC,MAAM;MACzC,MAAM2B,OAAM,GAAI;QACdC,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;QACzBC,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;QACzBC,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;QACpCC,CAAC,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,QAAQ,CAAC;QACtCC,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC;QACnCC,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ;MACpC;MACA,OAAON,OAAO,CAACtB,KAAK,CAACY,KAAK,KAAKU,OAAO,CAACC,CAAA;IACzC,CAAC;IAED,MAAMM,eAAc,GAAIlC,QAAQ,CAAC,MAAM;MACrC,MAAMmC,SAAQ,GAAI;QAChBP,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;QACzBC,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;QACzBC,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;QACvBC,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;QACzBC,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;QACvBC,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ;MAC3B;MACA,OAAOE,SAAS,CAAC9B,KAAK,CAACY,KAAK,KAAKkB,SAAS,CAACP,CAAA;IAC7C,CAAC;IAED,MAAMQ,QAAO,GAAIpC,QAAQ,CAAC,MAAM;MAC9B,MAAMqC,UAAS,GAAI;QACjBT,CAAC,EAAE,EAAE;QACLC,CAAC,EAAE,CAAC,UAAU,CAAC;QACfC,CAAC,EAAE,CAAC,YAAY,CAAC;QACjBC,CAAC,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC;QAC3BC,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;QAC1BC,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU;MAC1B;MACA,OAAOI,UAAU,CAAChC,KAAK,CAACY,KAAK,KAAK,EAAC;IACrC,CAAC;IAED,MAAMqB,mBAAkB,GAAI,MAAAA,CAAA,KAAY;MACtChB,QAAQ,CAACiB,KAAI,GAAI,IAAG;MACpB,IAAI;QACF;QACA,MAAM,IAAIC,OAAO,CAACC,OAAM,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;QACtDxC,SAAS,CAAC0C,OAAO,CAAC,QAAQ;QAC1BtB,IAAI,CAAC,gBAAgB,EAAEhB,KAAK,CAACC,QAAQ;MACvC,EAAE,OAAOsC,KAAK,EAAE;QACd3C,SAAS,CAAC2C,KAAK,CAAC,QAAQ;MAC1B,UAAU;QACRtB,QAAQ,CAACiB,KAAI,GAAI,KAAI;MACvB;IACF;IAEA,MAAMM,iBAAgB,GAAIA,CAAA,KAAM;MAC9BtB,WAAW,CAACgB,KAAI,GAAI,CAAChB,WAAW,CAACgB,KAAI;MACrClB,IAAI,CAAC,cAAc,EAAE;QAAEf,QAAQ,EAAED,KAAK,CAACC,QAAQ;QAAEiB,WAAW,EAAEA,WAAW,CAACgB;MAAM,CAAC;IACnF;IAEA,OAAO;MACLjB,QAAQ;MACRC,WAAW;MACXC,eAAe;MACfC,YAAY;MACZC,mBAAmB;MACnBQ,eAAe;MACfE,QAAQ;MACRE,mBAAmB;MACnBO;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}