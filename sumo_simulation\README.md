# SUMO + TraCI 动态红绿灯决策系统

## 项目概述

本项目实现了基于SUMO仿真和TraCI控制接口的动态红绿灯决策系统，用于展示智能交通控制相比传统固定配时的优化效果。

## 目录结构

```
sumo_simulation/
├── README.md                    # 项目说明文档
├── config/                      # 配置文件目录
│   ├── intersection.net.xml     # 十字路口网络模型
│   ├── traffic_flows.rou.xml    # 交通流配置
│   ├── simulation.sumocfg       # SUMO仿真配置
│   └── detectors.add.xml        # 检测器配置
├── algorithms/                  # 算法实现目录
│   ├── __init__.py
│   ├── fixed_timing.py          # 固定配时算法
│   ├── adaptive_timing.py       # 自适应配时算法
│   └── intelligent_timing.py    # 智能预测算法
├── controllers/                 # 控制器目录
│   ├── __init__.py
│   ├── traffic_controller.py    # 交通控制器
│   └── data_collector.py        # 数据采集器
├── analysis/                    # 分析模块目录
│   ├── __init__.py
│   ├── performance_analyzer.py  # 性能分析器
│   └── visualization.py         # 可视化模块
├── results/                     # 结果输出目录
│   ├── fixed_timing/            # 固定配时结果
│   ├── adaptive_timing/         # 自适应配时结果
│   └── comparison/              # 对比分析结果
├── tests/                       # 测试目录
│   ├── test_algorithms.py       # 算法测试
│   └── test_simulation.py       # 仿真测试
└── main.py                      # 主程序入口
```

## 功能特性

### 1. 仿真环境
- 标准十字路口模型
- 多种交通密度场景
- 实时数据采集

### 2. 控制算法
- 固定配时（对照组）
- 自适应配时（基于实时流量）
- 智能预测配时（机器学习优化）

### 3. 性能评估
- 平均等待时间
- 车辆通行效率
- 排队长度统计
- 燃油消耗估算

### 4. 可视化展示
- 实时仿真画面
- 性能指标图表
- 对比分析报告

## 快速开始

1. 安装依赖：
```bash
pip install traci sumolib matplotlib pandas numpy
```

2. 运行仿真：
```bash
python main.py --algorithm fixed
python main.py --algorithm adaptive
python main.py --algorithm intelligent
```

3. 查看结果：
```bash
python analysis/visualization.py --compare all
```

## 技术架构

- **SUMO**: 交通仿真引擎
- **TraCI**: 实时控制接口
- **Python**: 算法实现语言
- **Matplotlib**: 数据可视化
- **Pandas**: 数据分析处理

## 预期成果

通过对比不同算法的性能指标，展示智能交通控制系统的优化效果：
- 等待时间减少30-40%
- 通行效率提升25-35%
- 排队长度降低35-45%
- 燃油消耗减少20-30%
