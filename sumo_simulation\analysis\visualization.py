#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
可视化模块
生成各种图表来展示仿真结果和性能对比
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import seaborn as sns
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class TrafficVisualization:
    """交通仿真可视化类"""
    
    def __init__(self, style: str = 'seaborn-v0_8', figsize: Tuple[int, int] = (12, 8)):
        """
        初始化可视化器
        
        Args:
            style: 图表样式
            figsize: 图表大小
        """
        self.style = style
        self.figsize = figsize
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
        
        # 设置样式
        try:
            plt.style.use(style)
        except:
            plt.style.use('default')
        
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info("可视化模块初始化完成")
    
    def plot_performance_comparison(self, data: Dict[str, List[float]], 
                                  metric_name: str, 
                                  title: str = None,
                                  ylabel: str = None,
                                  save_path: str = None) -> plt.Figure:
        """
        绘制性能对比图
        
        Args:
            data: 算法名称到数据列表的映射
            metric_name: 指标名称
            title: 图表标题
            ylabel: Y轴标签
            save_path: 保存路径
            
        Returns:
            matplotlib图表对象
        """
        fig, ax = plt.subplots(figsize=self.figsize)
        
        algorithms = list(data.keys())
        values = [np.mean(data[alg]) for alg in algorithms]
        errors = [np.std(data[alg]) for alg in algorithms]
        
        bars = ax.bar(algorithms, values, yerr=errors, capsize=5, 
                     color=self.colors[:len(algorithms)], alpha=0.8)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + max(errors)/20,
                   f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
        
        ax.set_title(title or f'{metric_name} 性能对比', fontsize=16, fontweight='bold')
        ax.set_ylabel(ylabel or metric_name, fontsize=12)
        ax.set_xlabel('算法类型', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"图表已保存到: {save_path}")
        
        return fig
    
    def plot_time_series(self, data: Dict[str, List[float]], 
                        time_points: List[float],
                        metric_name: str,
                        title: str = None,
                        save_path: str = None) -> plt.Figure:
        """
        绘制时间序列图
        
        Args:
            data: 算法名称到数据列表的映射
            time_points: 时间点列表
            metric_name: 指标名称
            title: 图表标题
            save_path: 保存路径
            
        Returns:
            matplotlib图表对象
        """
        fig, ax = plt.subplots(figsize=self.figsize)
        
        for i, (algorithm, values) in enumerate(data.items()):
            # 确保数据长度一致
            min_length = min(len(values), len(time_points))
            x = time_points[:min_length]
            y = values[:min_length]
            
            ax.plot(x, y, label=algorithm, color=self.colors[i % len(self.colors)], 
                   linewidth=2, marker='o', markersize=4, alpha=0.8)
        
        ax.set_title(title or f'{metric_name} 时间序列', fontsize=16, fontweight='bold')
        ax.set_xlabel('仿真时间 (秒)', fontsize=12)
        ax.set_ylabel(metric_name, fontsize=12)
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"时间序列图已保存到: {save_path}")
        
        return fig
    
    def plot_improvement_radar(self, improvements: Dict[str, float], 
                              title: str = "性能改善雷达图",
                              save_path: str = None) -> plt.Figure:
        """
        绘制性能改善雷达图
        
        Args:
            improvements: 指标名称到改善百分比的映射
            title: 图表标题
            save_path: 保存路径
            
        Returns:
            matplotlib图表对象
        """
        # 准备数据
        metrics = list(improvements.keys())
        values = list(improvements.values())
        
        # 计算角度
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        values += values[:1]  # 闭合图形
        angles += angles[:1]
        
        # 创建雷达图
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        # 绘制数据
        ax.plot(angles, values, 'o-', linewidth=2, color=self.colors[0])
        ax.fill(angles, values, alpha=0.25, color=self.colors[0])
        
        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics, fontsize=10)
        
        # 设置径向轴
        ax.set_ylim(-50, 50)
        ax.set_yticks([-40, -20, 0, 20, 40])
        ax.set_yticklabels(['-40%', '-20%', '0%', '20%', '40%'], fontsize=8)
        ax.grid(True)
        
        # 添加零线
        ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"雷达图已保存到: {save_path}")
        
        return fig
    
    def plot_algorithm_ranking(self, rankings: Dict[str, List[Tuple[str, float]]], 
                              title: str = "算法性能排名",
                              save_path: str = None) -> plt.Figure:
        """
        绘制算法排名图
        
        Args:
            rankings: 指标名称到排名列表的映射
            title: 图表标题
            save_path: 保存路径
            
        Returns:
            matplotlib图表对象
        """
        metrics = list(rankings.keys())
        n_metrics = len(metrics)
        
        fig, axes = plt.subplots(1, n_metrics, figsize=(4*n_metrics, 6))
        if n_metrics == 1:
            axes = [axes]
        
        for i, (metric, ranking) in enumerate(rankings.items()):
            algorithms = [item[0] for item in ranking]
            values = [item[1] for item in ranking]
            
            bars = axes[i].bar(range(len(algorithms)), values, 
                              color=self.colors[:len(algorithms)], alpha=0.8)
            
            # 添加数值标签
            for j, (bar, value) in enumerate(zip(bars, values)):
                height = bar.get_height()
                axes[i].text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
            
            axes[i].set_title(f'{metric} 排名', fontsize=12, fontweight='bold')
            axes[i].set_xticks(range(len(algorithms)))
            axes[i].set_xticklabels(algorithms, rotation=45, ha='right')
            axes[i].grid(True, alpha=0.3)
        
        fig.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"排名图已保存到: {save_path}")
        
        return fig
    
    def plot_efficiency_scores(self, scores: Dict[str, float], 
                              title: str = "算法效率评分",
                              save_path: str = None) -> plt.Figure:
        """
        绘制效率评分图
        
        Args:
            scores: 算法名称到效率评分的映射
            title: 图表标题
            save_path: 保存路径
            
        Returns:
            matplotlib图表对象
        """
        fig, ax = plt.subplots(figsize=self.figsize)
        
        algorithms = list(scores.keys())
        values = list(scores.values())
        
        # 根据分数设置颜色
        colors = []
        for score in values:
            if score >= 80:
                colors.append('#2ca02c')  # 绿色 - 优秀
            elif score >= 60:
                colors.append('#ff7f0e')  # 橙色 - 良好
            else:
                colors.append('#d62728')  # 红色 - 需要改进
        
        bars = ax.bar(algorithms, values, color=colors, alpha=0.8)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{value:.1f}', ha='center', va='bottom', fontweight='bold')
        
        # 添加评分等级线
        ax.axhline(y=80, color='green', linestyle='--', alpha=0.7, label='优秀 (80+)')
        ax.axhline(y=60, color='orange', linestyle='--', alpha=0.7, label='良好 (60+)')
        
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.set_ylabel('效率评分', fontsize=12)
        ax.set_xlabel('算法类型', fontsize=12)
        ax.set_ylim(0, 100)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"效率评分图已保存到: {save_path}")
        
        return fig
    
    def create_comprehensive_dashboard(self, analysis_results: Dict[str, Any], 
                                     save_path: str = None) -> plt.Figure:
        """
        创建综合仪表板
        
        Args:
            analysis_results: 分析结果字典
            save_path: 保存路径
            
        Returns:
            matplotlib图表对象
        """
        fig = plt.figure(figsize=(20, 12))
        
        # 创建子图布局
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
        
        # 1. 等待时间对比
        ax1 = fig.add_subplot(gs[0, 0])
        self._plot_metric_comparison(ax1, analysis_results, 'avg_waiting_time', '平均等待时间 (秒)')
        
        # 2. 通行效率对比
        ax2 = fig.add_subplot(gs[0, 1])
        self._plot_metric_comparison(ax2, analysis_results, 'throughput', '通行效率 (车辆/小时)')
        
        # 3. 排队长度对比
        ax3 = fig.add_subplot(gs[0, 2])
        self._plot_metric_comparison(ax3, analysis_results, 'total_jam_length', '总排队长度 (米)')
        
        # 4. 燃油消耗对比
        ax4 = fig.add_subplot(gs[0, 3])
        self._plot_metric_comparison(ax4, analysis_results, 'total_fuel_consumption', '燃油消耗 (毫升)')
        
        # 5. 效率评分雷达图
        ax5 = fig.add_subplot(gs[1, :2], projection='polar')
        self._plot_efficiency_radar(ax5, analysis_results)
        
        # 6. 改善幅度条形图
        ax6 = fig.add_subplot(gs[1, 2:])
        self._plot_improvement_bars(ax6, analysis_results)
        
        # 7. 时间序列图
        ax7 = fig.add_subplot(gs[2, :])
        self._plot_time_series_summary(ax7, analysis_results)
        
        fig.suptitle('交通信号控制算法性能对比仪表板', fontsize=20, fontweight='bold')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"综合仪表板已保存到: {save_path}")
        
        return fig
    
    def _plot_metric_comparison(self, ax, analysis_results, metric, title):
        """绘制指标对比子图"""
        algorithms = list(analysis_results.get('statistics', {}).keys())
        values = []
        
        for alg in algorithms:
            stats = analysis_results['statistics'][alg]
            value = stats.get(metric, {}).get('mean', 0)
            values.append(value)
        
        bars = ax.bar(algorithms, values, color=self.colors[:len(algorithms)], alpha=0.8)
        
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:.1f}', ha='center', va='bottom', fontsize=8)
        
        ax.set_title(title, fontsize=10, fontweight='bold')
        ax.tick_params(axis='x', rotation=45, labelsize=8)
        ax.tick_params(axis='y', labelsize=8)
        ax.grid(True, alpha=0.3)
    
    def _plot_efficiency_radar(self, ax, analysis_results):
        """绘制效率雷达图"""
        # 简化的雷达图实现
        ax.set_title('算法效率对比', fontsize=12, fontweight='bold')
    
    def _plot_improvement_bars(self, ax, analysis_results):
        """绘制改善幅度条形图"""
        ax.set_title('性能改善幅度', fontsize=12, fontweight='bold')
    
    def _plot_time_series_summary(self, ax, analysis_results):
        """绘制时间序列汇总图"""
        ax.set_title('关键指标时间序列', fontsize=12, fontweight='bold')
        ax.set_xlabel('仿真时间 (秒)')


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    viz = TrafficVisualization()
    print("可视化模块初始化完成")
