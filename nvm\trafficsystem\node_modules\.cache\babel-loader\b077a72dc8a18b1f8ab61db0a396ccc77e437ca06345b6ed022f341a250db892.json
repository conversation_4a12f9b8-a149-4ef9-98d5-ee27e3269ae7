{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"intelligent-traffic-panel\"\n};\nconst _hoisted_2 = {\n  class: \"panel-header\"\n};\nconst _hoisted_3 = {\n  class: \"update-info\"\n};\nconst _hoisted_4 = {\n  class: \"panel-content\"\n};\nconst _hoisted_5 = {\n  class: \"section\"\n};\nconst _hoisted_6 = {\n  class: \"section\"\n};\nconst _hoisted_7 = {\n  class: \"section\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_CongestionGradeIndicator = _resolveComponent(\"CongestionGradeIndicator\");\n  const _component_TrafficTrendChart = _resolveComponent(\"TrafficTrendChart\");\n  const _component_StrategyRecommendation = _resolveComponent(\"StrategyRecommendation\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[0] || (_cache[0] = _createElementVNode(\"h3\", null, \"智能交通状态面板\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Refresh)]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"span\", null, _toDisplayString($setup.lastUpdateTime), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 拥挤等级指示器 \"), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_CongestionGradeIndicator, {\n    grade: $setup.currentGrade,\n    \"vehicle-count\": $props.currentVehicleCount\n  }, null, 8 /* PROPS */, [\"grade\", \"vehicle-count\"])]), _createCommentVNode(\" 交通趋势图表 \"), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_TrafficTrendChart, {\n    \"vehicle-data\": $setup.vehicleHistory,\n    \"moving-averages\": $setup.movingAverages,\n    \"trend-info\": $setup.trendInfo\n  }, null, 8 /* PROPS */, [\"vehicle-data\", \"moving-averages\", \"trend-info\"])]), _createCommentVNode(\" 策略推荐 \"), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_StrategyRecommendation, {\n    strategy: $setup.currentStrategy,\n    grade: $setup.currentGrade,\n    onApplyStrategy: $setup.handleApplyStrategy,\n    onViewDetails: $setup.handleViewDetails\n  }, null, 8 /* PROPS */, [\"strategy\", \"grade\", \"onApplyStrategy\", \"onViewDetails\"])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_Refresh", "_", "_toDisplayString", "$setup", "lastUpdateTime", "_hoisted_4", "_createCommentVNode", "_hoisted_5", "_component_CongestionGradeIndicator", "grade", "currentGrade", "$props", "currentVehicleCount", "_hoisted_6", "_component_TrafficTrendChart", "vehicleHistory", "movingAverages", "trendInfo", "_hoisted_7", "_component_StrategyRecommendation", "strategy", "currentStrategy", "onApplyStrategy", "handleApplyStrategy", "onViewDetails", "handleViewDetails"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\traffic\\IntelligentTrafficPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"intelligent-traffic-panel\">\n    <div class=\"panel-header\">\n      <h3>智能交通状态面板</h3>\n      <div class=\"update-info\">\n        <el-icon><Refresh /></el-icon>\n        <span>{{ lastUpdateTime }}</span>\n      </div>\n    </div>\n    \n    <div class=\"panel-content\">\n      <!-- 拥挤等级指示器 -->\n      <div class=\"section\">\n        <CongestionGradeIndicator \n          :grade=\"currentGrade\"\n          :vehicle-count=\"currentVehicleCount\"\n        />\n      </div>\n      \n      <!-- 交通趋势图表 -->\n      <div class=\"section\">\n        <TrafficTrendChart \n          :vehicle-data=\"vehicleHistory\"\n          :moving-averages=\"movingAverages\"\n          :trend-info=\"trendInfo\"\n        />\n      </div>\n      \n      <!-- 策略推荐 -->\n      <div class=\"section\">\n        <StrategyRecommendation \n          :strategy=\"currentStrategy\"\n          :grade=\"currentGrade\"\n          @apply-strategy=\"handleApplyStrategy\"\n          @view-details=\"handleViewDetails\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, watch, onMounted, onUnmounted } from 'vue'\nimport { Refresh } from '@element-plus/icons-vue'\nimport CongestionGradeIndicator from './CongestionGradeIndicator.vue'\nimport TrafficTrendChart from './TrafficTrendChart.vue'\nimport StrategyRecommendation from './StrategyRecommendation.vue'\nimport {\n  calculateCongestionGrade,\n  calculateMultipleMovingAverages,\n  analyzeTrend,\n  generateTrafficStrategy\n} from '@/utils/trafficAnalysisUtils'\n\nexport default {\n  name: 'IntelligentTrafficPanel',\n  components: {\n    Refresh,\n    CongestionGradeIndicator,\n    TrafficTrendChart,\n    StrategyRecommendation\n  },\n  props: {\n    // 当前帧的车辆检测数量\n    currentVehicleCount: {\n      type: Number,\n      default: 0\n    },\n    // 是否自动更新\n    autoUpdate: {\n      type: Boolean,\n      default: true\n    },\n    // 历史数据最大长度\n    maxHistoryLength: {\n      type: Number,\n      default: 50\n    }\n  },\n  emits: ['strategy-applied', 'data-updated'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const vehicleHistory = ref([])\n    const lastUpdateTime = ref('')\n    \n    // 计算属性\n    const currentGrade = computed(() => \n      calculateCongestionGrade(props.currentVehicleCount)\n    )\n    \n    const movingAverages = computed(() => \n      calculateMultipleMovingAverages(vehicleHistory.value)\n    )\n    \n    const trendInfo = computed(() => \n      analyzeTrend(vehicleHistory.value)\n    )\n    \n    const currentStrategy = computed(() => \n      generateTrafficStrategy(currentGrade.value, trendInfo.value, movingAverages.value)\n    )\n    \n    // 方法\n    const updateVehicleHistory = (newCount) => {\n      vehicleHistory.value.push(newCount)\n      \n      // 限制历史数据长度\n      if (vehicleHistory.value.length > props.maxHistoryLength) {\n        vehicleHistory.value.shift()\n      }\n      \n      // 更新时间戳\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n      \n      // 发出数据更新事件\n      emit('data-updated', {\n        currentCount: newCount,\n        grade: currentGrade.value,\n        movingAverages: movingAverages.value,\n        trend: trendInfo.value,\n        strategy: currentStrategy.value\n      })\n    }\n    \n    const handleApplyStrategy = (strategy) => {\n      emit('strategy-applied', {\n        strategy,\n        grade: currentGrade.value,\n        vehicleCount: props.currentVehicleCount,\n        timestamp: new Date().toISOString()\n      })\n    }\n    \n    const handleViewDetails = (details) => {\n      console.log('查看策略详情:', details)\n    }\n    \n    // 监听车辆数量变化\n    watch(() => props.currentVehicleCount, (newCount) => {\n      if (props.autoUpdate) {\n        updateVehicleHistory(newCount)\n      }\n    })\n    \n    // 手动更新数据的方法\n    const manualUpdate = () => {\n      updateVehicleHistory(props.currentVehicleCount)\n    }\n    \n    // 清空历史数据\n    const clearHistory = () => {\n      vehicleHistory.value = []\n      lastUpdateTime.value = ''\n    }\n    \n    // 获取当前状态摘要\n    const getStatusSummary = () => {\n      return {\n        currentVehicleCount: props.currentVehicleCount,\n        grade: currentGrade.value,\n        movingAverages: movingAverages.value,\n        trend: trendInfo.value,\n        strategy: currentStrategy.value,\n        historyLength: vehicleHistory.value.length,\n        lastUpdate: lastUpdateTime.value\n      }\n    }\n    \n    // 组件挂载时初始化\n    onMounted(() => {\n      if (props.currentVehicleCount > 0) {\n        updateVehicleHistory(props.currentVehicleCount)\n      }\n    })\n    \n    // 暴露方法给父组件\n    const updateData = manualUpdate\n    const clearData = clearHistory\n    const getStatus = getStatusSummary\n    \n    return {\n      // 响应式数据\n      vehicleHistory,\n      lastUpdateTime,\n      \n      // 计算属性\n      currentGrade,\n      movingAverages,\n      trendInfo,\n      currentStrategy,\n      \n      // 方法\n      handleApplyStrategy,\n      handleViewDetails,\n      \n      // 暴露给父组件的方法\n      updateData,\n      clearData,\n      getStatus\n    }\n  }\n}\n</script>\n\n<style scoped>\n.intelligent-traffic-panel {\n  background: #f8f9fa;\n  border-radius: 16px;\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  border: 1px solid #e8e8e8;\n}\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.panel-header h3 {\n  margin: 0;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.update-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.panel-content {\n  padding: 24px;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.section {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.section:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .panel-content {\n    gap: 20px;\n  }\n}\n\n@media (max-width: 768px) {\n  .panel-header {\n    flex-direction: column;\n    gap: 12px;\n    align-items: flex-start;\n    padding: 16px 20px;\n  }\n  \n  .panel-header h3 {\n    font-size: 18px;\n  }\n  \n  .panel-content {\n    padding: 20px;\n    gap: 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .panel-header {\n    padding: 12px 16px;\n  }\n  \n  .panel-content {\n    padding: 16px;\n    gap: 12px;\n  }\n}\n\n/* 动画效果 */\n.section {\n  animation: fadeInUp 0.6s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 为不同等级添加主题色彩 */\n.intelligent-traffic-panel[data-grade=\"A\"] .panel-header {\n  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);\n}\n\n.intelligent-traffic-panel[data-grade=\"B\"] .panel-header {\n  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);\n}\n\n.intelligent-traffic-panel[data-grade=\"C\"] .panel-header {\n  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);\n}\n\n.intelligent-traffic-panel[data-grade=\"D\"] .panel-header {\n  background: linear-gradient(135deg, #fa8c16 0%, #ff9c6e 100%);\n}\n\n.intelligent-traffic-panel[data-grade=\"E\"] .panel-header {\n  background: linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%);\n}\n\n.intelligent-traffic-panel[data-grade=\"F\"] .panel-header {\n  background: linear-gradient(135deg, #a8071a 0%, #cf1322 100%);\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAa;;EAMrBA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAS;;EAQfA,KAAK,EAAC;AAAS;;EASfA,KAAK,EAAC;AAAS;;;;;;;uBA5BxBC,mBAAA,CAqCM,OArCNC,UAqCM,GApCJC,mBAAA,CAMM,OANNC,UAMM,G,0BALJD,mBAAA,CAAiB,YAAb,UAAQ,sBACZA,mBAAA,CAGM,OAHNE,UAGM,GAFJC,YAAA,CAA8BC,kBAAA;IALtCC,OAAA,EAAAC,QAAA,CAKiB,MAAW,CAAXH,YAAA,CAAWI,kBAAA,E;IAL5BC,CAAA;MAMQR,mBAAA,CAAiC,cAAAS,gBAAA,CAAxBC,MAAA,CAAAC,cAAc,iB,KAI3BX,mBAAA,CA2BM,OA3BNY,UA2BM,GA1BJC,mBAAA,aAAgB,EAChBb,mBAAA,CAKM,OALNc,UAKM,GAJJX,YAAA,CAGEY,mCAAA;IAFCC,KAAK,EAAEN,MAAA,CAAAO,YAAY;IACnB,eAAa,EAAEC,MAAA,CAAAC;yDAIpBN,mBAAA,YAAe,EACfb,mBAAA,CAMM,OANNoB,UAMM,GALJjB,YAAA,CAIEkB,4BAAA;IAHC,cAAY,EAAEX,MAAA,CAAAY,cAAc;IAC5B,iBAAe,EAAEZ,MAAA,CAAAa,cAAc;IAC/B,YAAU,EAAEb,MAAA,CAAAc;gFAIjBX,mBAAA,UAAa,EACbb,mBAAA,CAOM,OAPNyB,UAOM,GANJtB,YAAA,CAKEuB,iCAAA;IAJCC,QAAQ,EAAEjB,MAAA,CAAAkB,eAAe;IACzBZ,KAAK,EAAEN,MAAA,CAAAO,YAAY;IACnBY,eAAc,EAAEnB,MAAA,CAAAoB,mBAAmB;IACnCC,aAAY,EAAErB,MAAA,CAAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}