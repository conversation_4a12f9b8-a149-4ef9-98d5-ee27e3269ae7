<?xml version="1.0" encoding="UTF-8"?>

<!-- SUMO十字路口网络模型 -->
<net version="1.16" junctionCornerDetail="5" limitTurnSpeed="5.50" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://sumo.dlr.de/xsd/net_file.xsd">

    <location netOffset="0.00,0.00" convBoundary="-300.00,-300.00,300.00,300.00" origBoundary="-300.00,-300.00,300.00,300.00" projParameter="!"/>

    <!-- 定义节点 -->
    <junction id="center" type="traffic_light" x="0.00" y="0.00" incLanes="east_in_0 east_in_1 south_in_0 south_in_1 west_in_0 west_in_1 north_in_0 north_in_1" intLanes=":center_0_0 :center_1_0 :center_1_1 :center_3_0 :center_4_0 :center_5_0 :center_5_1 :center_7_0 :center_8_0 :center_9_0 :center_9_1 :center_11_0 :center_12_0 :center_13_0 :center_13_1 :center_15_0" shape="-6.40,6.40 6.40,6.40 6.84,4.84 7.40,4.40 8.18,4.18 9.18,4.18 10.40,4.40 10.40,-4.40 8.84,-4.84 8.40,-5.40 8.18,-6.18 8.18,-7.18 8.40,-8.40 -8.40,-8.40 -8.84,-6.84 -9.40,-6.40 -10.18,-6.18 -11.18,-6.18 -12.40,-6.40 -12.40,6.40 -10.84,6.84 -10.40,7.40 -10.18,8.18 -10.18,9.18 -10.40,10.40 6.40,10.40">
        <request index="0"  response="0000000000000000" foes="0000100001100000" cont="0"/>
        <request index="1"  response="0000000000000000" foes="1111100011100000" cont="0"/>
        <request index="2"  response="0000000000000000" foes="1111100011100000" cont="0"/>
        <request index="3"  response="0000011000000000" foes="1110011011100000" cont="0"/>
        <request index="4"  response="0000010000000000" foes="0000010000000000" cont="0"/>
        <request index="5"  response="0000110000001111" foes="0000110000001111" cont="0"/>
        <request index="6"  response="0000110000001111" foes="0000110000001111" cont="0"/>
        <request index="7"  response="0000100000001110" foes="0000100000001110" cont="0"/>
        <request index="8"  response="0000000000000000" foes="0000000000001000" cont="0"/>
        <request index="9"  response="0000000000000000" foes="0000000011111000" cont="0"/>
        <request index="10" response="0000000000000000" foes="0000000011111000" cont="0"/>
        <request index="11" response="0000000000000110" foes="0000000011100110" cont="0"/>
        <request index="12" response="0000000000000100" foes="0000000000000100" cont="0"/>
        <request index="13" response="0000111100000100" foes="0000111100000100" cont="0"/>
        <request index="14" response="0000111100000100" foes="0000111100000100" cont="0"/>
        <request index="15" response="0000111000000100" foes="0000111000000100" cont="0"/>
    </junction>
    
    <junction id="east_end" type="dead_end" x="300.00" y="0.00" incLanes="east_out_0 east_out_1" intLanes="" shape="300.00,-4.40 300.00,4.40"/>
    <junction id="south_end" type="dead_end" x="0.00" y="-300.00" incLanes="south_out_0 south_out_1" intLanes="" shape="-4.40,-300.00 4.40,-300.00"/>
    <junction id="west_end" type="dead_end" x="-300.00" y="0.00" incLanes="west_out_0 west_out_1" intLanes="" shape="-300.00,4.40 -300.00,-4.40"/>
    <junction id="north_end" type="dead_end" x="0.00" y="300.00" incLanes="north_out_0 north_out_1" intLanes="" shape="4.40,300.00 -4.40,300.00"/>
    
    <junction id="east_start" type="dead_end" x="300.00" y="0.00" incLanes="" intLanes="" shape="300.00,4.40 300.00,-4.40"/>
    <junction id="south_start" type="dead_end" x="0.00" y="-300.00" incLanes="" intLanes="" shape="4.40,-300.00 -4.40,-300.00"/>
    <junction id="west_start" type="dead_end" x="-300.00" y="0.00" incLanes="" intLanes="" shape="-300.00,-4.40 -300.00,4.40"/>
    <junction id="north_start" type="dead_end" x="0.00" y="300.00" incLanes="" intLanes="" shape="-4.40,300.00 4.40,300.00"/>

    <!-- 定义边和车道 -->
    <!-- 东向进入车道 -->
    <edge id="east_in" from="east_start" to="center" priority="1">
        <lane id="east_in_0" index="0" speed="13.89" length="300.00" shape="300.00,-1.60 10.40,-1.60"/>
        <lane id="east_in_1" index="1" speed="13.89" length="300.00" shape="300.00,1.60 10.40,1.60"/>
    </edge>
    
    <!-- 东向离开车道 -->
    <edge id="east_out" from="center" to="east_end" priority="1">
        <lane id="east_out_0" index="0" speed="13.89" length="300.00" shape="10.40,-1.60 300.00,-1.60"/>
        <lane id="east_out_1" index="1" speed="13.89" length="300.00" shape="10.40,1.60 300.00,1.60"/>
    </edge>

    <!-- 南向进入车道 -->
    <edge id="south_in" from="south_start" to="center" priority="1">
        <lane id="south_in_0" index="0" speed="13.89" length="300.00" shape="1.60,-300.00 1.60,-10.40"/>
        <lane id="south_in_1" index="1" speed="13.89" length="300.00" shape="-1.60,-300.00 -1.60,-10.40"/>
    </edge>
    
    <!-- 南向离开车道 -->
    <edge id="south_out" from="center" to="south_end" priority="1">
        <lane id="south_out_0" index="0" speed="13.89" length="300.00" shape="1.60,-10.40 1.60,-300.00"/>
        <lane id="south_out_1" index="1" speed="13.89" length="300.00" shape="-1.60,-10.40 -1.60,-300.00"/>
    </edge>

    <!-- 西向进入车道 -->
    <edge id="west_in" from="west_start" to="center" priority="1">
        <lane id="west_in_0" index="0" speed="13.89" length="300.00" shape="-300.00,1.60 -10.40,1.60"/>
        <lane id="west_in_1" index="1" speed="13.89" length="300.00" shape="-300.00,-1.60 -10.40,-1.60"/>
    </edge>
    
    <!-- 西向离开车道 -->
    <edge id="west_out" from="center" to="west_end" priority="1">
        <lane id="west_out_0" index="0" speed="13.89" length="300.00" shape="-10.40,1.60 -300.00,1.60"/>
        <lane id="west_out_1" index="1" speed="13.89" length="300.00" shape="-10.40,-1.60 -300.00,-1.60"/>
    </edge>

    <!-- 北向进入车道 -->
    <edge id="north_in" from="north_start" to="center" priority="1">
        <lane id="north_in_0" index="0" speed="13.89" length="300.00" shape="-1.60,300.00 -1.60,10.40"/>
        <lane id="north_in_1" index="1" speed="13.89" length="300.00" shape="1.60,300.00 1.60,10.40"/>
    </edge>
    
    <!-- 北向离开车道 -->
    <edge id="north_out" from="center" to="north_end" priority="1">
        <lane id="north_out_0" index="0" speed="13.89" length="300.00" shape="-1.60,10.40 -1.60,300.00"/>
        <lane id="north_out_1" index="1" speed="13.89" length="300.00" shape="1.60,10.40 1.60,300.00"/>
    </edge>

    <!-- 信号灯配置 -->
    <tlLogic id="center" type="static" programID="0" offset="0">
        <!-- 东西方向绿灯 -->
        <phase duration="30" state="GGGrrrGGGrrr"/>
        <!-- 东西方向黄灯 -->
        <phase duration="3"  state="yyyrrryyyrrr"/>
        <!-- 南北方向绿灯 -->
        <phase duration="30" state="rrrGGGrrrGGG"/>
        <!-- 南北方向黄灯 -->
        <phase duration="3"  state="rrryyyrrryyy"/>
    </tlLogic>

    <!-- 连接定义 -->
    <connection from="east_in" to="east_out" fromLane="0" toLane="0" via=":center_0_0" tl="center" linkIndex="0" dir="s" state="G"/>
    <connection from="east_in" to="south_out" fromLane="0" toLane="0" via=":center_1_0" tl="center" linkIndex="1" dir="r" state="G"/>
    <connection from="east_in" to="south_out" fromLane="1" toLane="1" via=":center_1_1" tl="center" linkIndex="2" dir="r" state="G"/>
    <connection from="east_in" to="north_out" fromLane="1" toLane="1" via=":center_3_0" tl="center" linkIndex="3" dir="l" state="G"/>
    
    <connection from="south_in" to="south_out" fromLane="0" toLane="0" via=":center_4_0" tl="center" linkIndex="4" dir="s" state="r"/>
    <connection from="south_in" to="west_out" fromLane="0" toLane="0" via=":center_5_0" tl="center" linkIndex="5" dir="r" state="r"/>
    <connection from="south_in" to="west_out" fromLane="1" toLane="1" via=":center_5_1" tl="center" linkIndex="6" dir="r" state="r"/>
    <connection from="south_in" to="east_out" fromLane="1" toLane="1" via=":center_7_0" tl="center" linkIndex="7" dir="l" state="r"/>
    
    <connection from="west_in" to="west_out" fromLane="0" toLane="0" via=":center_8_0" tl="center" linkIndex="8" dir="s" state="G"/>
    <connection from="west_in" to="north_out" fromLane="0" toLane="0" via=":center_9_0" tl="center" linkIndex="9" dir="r" state="G"/>
    <connection from="west_in" to="north_out" fromLane="1" toLane="1" via=":center_9_1" tl="center" linkIndex="10" dir="r" state="G"/>
    <connection from="west_in" to="south_out" fromLane="1" toLane="1" via=":center_11_0" tl="center" linkIndex="11" dir="l" state="G"/>
    
    <connection from="north_in" to="north_out" fromLane="0" toLane="0" via=":center_12_0" tl="center" linkIndex="12" dir="s" state="r"/>
    <connection from="north_in" to="east_out" fromLane="0" toLane="0" via=":center_13_0" tl="center" linkIndex="13" dir="r" state="r"/>
    <connection from="north_in" to="east_out" fromLane="1" toLane="1" via=":center_13_1" tl="center" linkIndex="14" dir="r" state="r"/>
    <connection from="north_in" to="west_out" fromLane="1" toLane="1" via=":center_15_0" tl="center" linkIndex="15" dir="l" state="r"/>

</net>
