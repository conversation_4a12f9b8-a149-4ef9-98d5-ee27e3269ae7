#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
性能分析器
分析和比较不同算法的性能指标
"""

import json
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path

class PerformanceAnalyzer:
    """性能分析器类"""
    
    def __init__(self):
        """初始化性能分析器"""
        self.logger = logging.getLogger(self.__class__.__name__)
        self.data = {}  # 存储不同算法的数据
        
    def load_performance_data(self, algorithm_type: str, data_file: str):
        """
        加载性能数据
        
        Args:
            algorithm_type: 算法类型
            data_file: 数据文件路径
        """
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.data[algorithm_type] = data
            self.logger.info(f"已加载 {algorithm_type} 算法的性能数据，共 {len(data)} 个数据点")
            
        except Exception as e:
            self.logger.error(f"加载性能数据失败: {str(e)}")
            raise
    
    def load_multiple_data(self, data_files: Dict[str, str]):
        """
        加载多个算法的性能数据
        
        Args:
            data_files: 算法类型到文件路径的映射
        """
        for algorithm_type, file_path in data_files.items():
            self.load_performance_data(algorithm_type, file_path)
    
    def calculate_summary_statistics(self, algorithm_type: str) -> Dict[str, Any]:
        """
        计算汇总统计信息
        
        Args:
            algorithm_type: 算法类型
            
        Returns:
            汇总统计字典
        """
        if algorithm_type not in self.data:
            raise ValueError(f"未找到算法 {algorithm_type} 的数据")
        
        data = self.data[algorithm_type]
        df = pd.DataFrame(data)
        
        # 计算关键指标的统计信息
        statistics = {}
        
        key_metrics = [
            'avg_waiting_time', 'avg_speed', 'total_jam_length', 
            'total_jam_vehicles', 'throughput', 'total_fuel_consumption', 
            'total_co2_emission'
        ]
        
        for metric in key_metrics:
            if metric in df.columns:
                statistics[metric] = {
                    'mean': df[metric].mean(),
                    'std': df[metric].std(),
                    'min': df[metric].min(),
                    'max': df[metric].max(),
                    'median': df[metric].median(),
                    'q25': df[metric].quantile(0.25),
                    'q75': df[metric].quantile(0.75)
                }
        
        # 添加总体信息
        statistics['total_data_points'] = len(data)
        statistics['simulation_duration'] = df['simulation_time'].max() if 'simulation_time' in df.columns else 0
        
        return statistics
    
    def compare_algorithms(self, baseline: str, comparison: str) -> Dict[str, Any]:
        """
        比较两个算法的性能
        
        Args:
            baseline: 基准算法类型
            comparison: 比较算法类型
            
        Returns:
            比较结果字典
        """
        if baseline not in self.data or comparison not in self.data:
            raise ValueError("缺少比较所需的数据")
        
        baseline_stats = self.calculate_summary_statistics(baseline)
        comparison_stats = self.calculate_summary_statistics(comparison)
        
        comparison_results = {
            'baseline_algorithm': baseline,
            'comparison_algorithm': comparison,
            'improvements': {}
        }
        
        # 计算改善幅度
        key_metrics = [
            'avg_waiting_time', 'avg_speed', 'total_jam_length', 
            'total_jam_vehicles', 'throughput', 'total_fuel_consumption', 
            'total_co2_emission'
        ]
        
        for metric in key_metrics:
            if metric in baseline_stats and metric in comparison_stats:
                baseline_value = baseline_stats[metric]['mean']
                comparison_value = comparison_stats[metric]['mean']
                
                if baseline_value != 0:
                    # 对于等待时间、排队长度、燃油消耗等，减少是好的
                    if metric in ['avg_waiting_time', 'total_jam_length', 'total_jam_vehicles', 
                                'total_fuel_consumption', 'total_co2_emission']:
                        improvement = (baseline_value - comparison_value) / baseline_value * 100
                    # 对于速度、通行效率等，增加是好的
                    else:
                        improvement = (comparison_value - baseline_value) / baseline_value * 100
                    
                    comparison_results['improvements'][metric] = {
                        'baseline_value': baseline_value,
                        'comparison_value': comparison_value,
                        'improvement_percentage': improvement,
                        'absolute_change': comparison_value - baseline_value
                    }
        
        return comparison_results
    
    def generate_performance_report(self, algorithms: List[str]) -> Dict[str, Any]:
        """
        生成性能报告
        
        Args:
            algorithms: 要分析的算法列表
            
        Returns:
            性能报告字典
        """
        report = {
            'algorithms': algorithms,
            'statistics': {},
            'comparisons': {},
            'rankings': {}
        }
        
        # 计算每个算法的统计信息
        for algorithm in algorithms:
            if algorithm in self.data:
                report['statistics'][algorithm] = self.calculate_summary_statistics(algorithm)
        
        # 进行两两比较
        if len(algorithms) >= 2:
            baseline = algorithms[0]  # 使用第一个算法作为基准
            for i in range(1, len(algorithms)):
                comparison = algorithms[i]
                comparison_key = f"{baseline}_vs_{comparison}"
                report['comparisons'][comparison_key] = self.compare_algorithms(baseline, comparison)
        
        # 生成排名
        key_metrics = ['avg_waiting_time', 'avg_speed', 'throughput']
        for metric in key_metrics:
            ranking = []
            for algorithm in algorithms:
                if (algorithm in report['statistics'] and 
                    metric in report['statistics'][algorithm]):
                    value = report['statistics'][algorithm][metric]['mean']
                    ranking.append((algorithm, value))
            
            # 排序（等待时间越小越好，速度和通行效率越大越好）
            if metric == 'avg_waiting_time':
                ranking.sort(key=lambda x: x[1])  # 升序
            else:
                ranking.sort(key=lambda x: x[1], reverse=True)  # 降序
            
            report['rankings'][metric] = ranking
        
        return report
    
    def calculate_efficiency_score(self, algorithm_type: str) -> float:
        """
        计算效率评分
        
        Args:
            algorithm_type: 算法类型
            
        Returns:
            效率评分（0-100）
        """
        if algorithm_type not in self.data:
            return 0
        
        stats = self.calculate_summary_statistics(algorithm_type)
        
        # 定义权重
        weights = {
            'avg_waiting_time': -0.3,  # 负权重，等待时间越小越好
            'avg_speed': 0.2,          # 正权重，速度越大越好
            'throughput': 0.3,         # 正权重，通行效率越大越好
            'total_jam_length': -0.2   # 负权重，排队长度越小越好
        }
        
        score = 50  # 基础分数
        
        for metric, weight in weights.items():
            if metric in stats:
                value = stats[metric]['mean']
                
                # 归一化处理（简化版）
                if metric == 'avg_waiting_time':
                    normalized_value = max(0, min(100, value))  # 0-100秒
                    score += weight * (100 - normalized_value)
                elif metric == 'avg_speed':
                    normalized_value = max(0, min(50, value))   # 0-50 m/s
                    score += weight * normalized_value * 2
                elif metric == 'throughput':
                    normalized_value = max(0, min(2000, value)) # 0-2000 vehicles/hour
                    score += weight * (normalized_value / 20)
                elif metric == 'total_jam_length':
                    normalized_value = max(0, min(1000, value)) # 0-1000 meters
                    score += weight * (100 - normalized_value / 10)
        
        return max(0, min(100, score))
    
    def export_comparison_table(self, algorithms: List[str], output_file: str):
        """
        导出比较表格
        
        Args:
            algorithms: 算法列表
            output_file: 输出文件路径
        """
        try:
            comparison_data = []
            
            for algorithm in algorithms:
                if algorithm in self.data:
                    stats = self.calculate_summary_statistics(algorithm)
                    efficiency_score = self.calculate_efficiency_score(algorithm)
                    
                    row = {
                        'Algorithm': algorithm,
                        'Efficiency Score': f"{efficiency_score:.1f}",
                        'Avg Waiting Time (s)': f"{stats.get('avg_waiting_time', {}).get('mean', 0):.2f}",
                        'Avg Speed (m/s)': f"{stats.get('avg_speed', {}).get('mean', 0):.2f}",
                        'Throughput (veh/h)': f"{stats.get('throughput', {}).get('mean', 0):.0f}",
                        'Total Jam Length (m)': f"{stats.get('total_jam_length', {}).get('mean', 0):.1f}",
                        'Fuel Consumption (ml)': f"{stats.get('total_fuel_consumption', {}).get('mean', 0):.1f}",
                        'CO2 Emission (mg)': f"{stats.get('total_co2_emission', {}).get('mean', 0):.1f}"
                    }
                    comparison_data.append(row)
            
            df = pd.DataFrame(comparison_data)
            
            # 根据文件扩展名选择输出格式
            if output_file.endswith('.csv'):
                df.to_csv(output_file, index=False, encoding='utf-8')
            elif output_file.endswith('.xlsx'):
                df.to_excel(output_file, index=False)
            else:
                # 默认保存为CSV
                df.to_csv(output_file + '.csv', index=False, encoding='utf-8')
            
            self.logger.info(f"比较表格已导出到: {output_file}")
            
        except Exception as e:
            self.logger.error(f"导出比较表格失败: {str(e)}")
            raise
    
    def get_time_series_data(self, algorithm_type: str, metric: str) -> List[float]:
        """
        获取时间序列数据
        
        Args:
            algorithm_type: 算法类型
            metric: 指标名称
            
        Returns:
            时间序列数据列表
        """
        if algorithm_type not in self.data:
            return []
        
        data = self.data[algorithm_type]
        return [item.get(metric, 0) for item in data if metric in item]
    
    def calculate_stability_index(self, algorithm_type: str, metric: str) -> float:
        """
        计算稳定性指数
        
        Args:
            algorithm_type: 算法类型
            metric: 指标名称
            
        Returns:
            稳定性指数（变异系数的倒数）
        """
        time_series = self.get_time_series_data(algorithm_type, metric)
        
        if not time_series or len(time_series) < 2:
            return 0
        
        mean_value = np.mean(time_series)
        std_value = np.std(time_series)
        
        if mean_value == 0:
            return 0
        
        # 变异系数
        cv = std_value / mean_value
        
        # 稳定性指数（变异系数越小，稳定性越高）
        stability_index = 1 / (1 + cv)
        
        return stability_index


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    analyzer = PerformanceAnalyzer()
    print("性能分析器初始化完成")
