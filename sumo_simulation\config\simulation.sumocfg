<?xml version="1.0" encoding="UTF-8"?>

<!-- SUMO仿真配置文件 -->
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://sumo.dlr.de/xsd/sumoConfiguration.xsd">

    <input>
        <!-- 网络文件 -->
        <net-file value="intersection.net.xml"/>
        <!-- 路线文件 -->
        <route-files value="traffic_flows.rou.xml"/>
        <!-- 附加文件（检测器等） -->
        <additional-files value="detectors.add.xml"/>
    </input>

    <output>
        <!-- 仿真输出文件 -->
        <tripinfo-output value="../results/tripinfo.xml"/>
        <summary-output value="../results/summary.xml"/>
        <statistic-output value="../results/statistics.xml"/>
        <!-- 车辆轨迹输出 -->
        <fcd-output value="../results/fcd.xml"/>
        <!-- 排放输出 -->
        <emission-output value="../results/emissions.xml"/>
    </output>

    <time>
        <!-- 仿真开始时间 -->
        <begin value="0"/>
        <!-- 仿真结束时间（1小时） -->
        <end value="3600"/>
        <!-- 仿真步长（1秒） -->
        <step-length value="1"/>
    </time>

    <processing>
        <!-- 忽略路线错误 -->
        <ignore-route-errors value="true"/>
        <!-- 时间触发器 -->
        <time-to-teleport value="300"/>
        <!-- 最大延迟 -->
        <max-depart-delay value="900"/>
        <!-- 车辆插入检查 -->
        <eager-insert value="true"/>
        <!-- 随机数种子 -->
        <seed value="42"/>
    </processing>

    <routing>
        <!-- 启用设备路由 -->
        <device.rerouting.probability value="0.1"/>
        <!-- 路由周期 -->
        <device.rerouting.period value="300"/>
        <!-- 路由适应性 -->
        <device.rerouting.adaptation-steps value="180"/>
    </routing>

    <report>
        <!-- 详细输出 -->
        <verbose value="true"/>
        <!-- 统计输出 -->
        <duration-log.statistics value="true"/>
        <!-- 不输出步骤信息 -->
        <no-step-log value="true"/>
    </report>

    <traci_server>
        <!-- TraCI服务器端口 -->
        <remote-port value="8813"/>
        <!-- 客户端数量 -->
        <num-clients value="1"/>
    </traci_server>

    <gui_only>
        <!-- GUI设置（仅在使用sumo-gui时有效） -->
        <gui-settings-file value="gui-settings.xml"/>
        <!-- 开始时暂停 -->
        <start value="false"/>
        <!-- 退出时关闭 -->
        <quit-on-end value="true"/>
    </gui_only>

</configuration>
