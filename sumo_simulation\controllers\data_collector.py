#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据采集器
收集和处理SUMO仿真中的交通数据
"""

import os
import json
import csv
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import traci

class DataCollector:
    """数据采集器类"""
    
    def __init__(self, output_dir: str = "results"):
        """
        初始化数据采集器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        self.ensure_output_dir()
        
        # 数据存储
        self.vehicle_data = []
        self.traffic_light_data = []
        self.detector_data = []
        self.performance_metrics = []
        
        # 采集配置
        self.collection_interval = 1  # 数据采集间隔（秒）
        self.last_collection_time = 0
        
        # 日志记录
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"数据采集器初始化完成，输出目录: {output_dir}")
    
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def collect_vehicle_data(self, simulation_time: float) -> Dict[str, Any]:
        """
        收集车辆数据
        
        Args:
            simulation_time: 仿真时间
            
        Returns:
            车辆数据字典
        """
        try:
            vehicle_ids = traci.vehicle.getIDList()
            
            vehicle_info = {
                'simulation_time': simulation_time,
                'total_vehicles': len(vehicle_ids),
                'vehicles': []
            }
            
            for veh_id in vehicle_ids:
                try:
                    veh_data = {
                        'id': veh_id,
                        'position': traci.vehicle.getPosition(veh_id),
                        'speed': traci.vehicle.getSpeed(veh_id),
                        'waiting_time': traci.vehicle.getWaitingTime(veh_id),
                        'lane_id': traci.vehicle.getLaneID(veh_id),
                        'route_id': traci.vehicle.getRouteID(veh_id),
                        'vehicle_type': traci.vehicle.getTypeID(veh_id),
                        'fuel_consumption': traci.vehicle.getFuelConsumption(veh_id),
                        'co2_emission': traci.vehicle.getCO2Emission(veh_id)
                    }
                    vehicle_info['vehicles'].append(veh_data)
                    
                except Exception as e:
                    self.logger.warning(f"收集车辆 {veh_id} 数据时出错: {str(e)}")
            
            return vehicle_info
            
        except Exception as e:
            self.logger.error(f"收集车辆数据时出错: {str(e)}")
            return {
                'simulation_time': simulation_time,
                'total_vehicles': 0,
                'vehicles': [],
                'error': str(e)
            }
    
    def collect_traffic_light_data(self, simulation_time: float, 
                                 traffic_light_id: str = 'center') -> Dict[str, Any]:
        """
        收集信号灯数据
        
        Args:
            simulation_time: 仿真时间
            traffic_light_id: 信号灯ID
            
        Returns:
            信号灯数据字典
        """
        try:
            tl_data = {
                'simulation_time': simulation_time,
                'traffic_light_id': traffic_light_id,
                'current_state': traci.trafficlight.getRedYellowGreenState(traffic_light_id),
                'current_phase': traci.trafficlight.getPhase(traffic_light_id),
                'phase_duration': traci.trafficlight.getPhaseDuration(traffic_light_id),
                'next_switch': traci.trafficlight.getNextSwitch(traffic_light_id),
                'controlled_lanes': traci.trafficlight.getControlledLanes(traffic_light_id)
            }
            
            return tl_data
            
        except Exception as e:
            self.logger.error(f"收集信号灯数据时出错: {str(e)}")
            return {
                'simulation_time': simulation_time,
                'traffic_light_id': traffic_light_id,
                'error': str(e)
            }
    
    def collect_detector_data(self, simulation_time: float) -> Dict[str, Any]:
        """
        收集检测器数据
        
        Args:
            simulation_time: 仿真时间
            
        Returns:
            检测器数据字典
        """
        try:
            detector_info = {
                'simulation_time': simulation_time,
                'induction_loops': {},
                'lane_area_detectors': {}
            }
            
            # 收集感应线圈数据
            for detector_id in traci.inductionloop.getIDList():
                try:
                    loop_data = {
                        'vehicle_count': traci.inductionloop.getLastStepVehicleNumber(detector_id),
                        'mean_speed': traci.inductionloop.getLastStepMeanSpeed(detector_id),
                        'mean_length': traci.inductionloop.getLastStepMeanLength(detector_id),
                        'occupancy': traci.inductionloop.getLastStepOccupancy(detector_id),
                        'vehicle_ids': traci.inductionloop.getLastStepVehicleIDs(detector_id)
                    }
                    detector_info['induction_loops'][detector_id] = loop_data
                    
                except Exception as e:
                    self.logger.warning(f"收集感应线圈 {detector_id} 数据时出错: {str(e)}")
            
            # 收集车道区域检测器数据
            for detector_id in traci.lanearea.getIDList():
                try:
                    area_data = {
                        'vehicle_count': traci.lanearea.getLastStepVehicleNumber(detector_id),
                        'mean_speed': traci.lanearea.getLastStepMeanSpeed(detector_id),
                        'occupancy': traci.lanearea.getLastStepOccupancy(detector_id),
                        'jam_length': traci.lanearea.getJamLengthMeters(detector_id),
                        'jam_vehicle_count': traci.lanearea.getJamLengthVehicle(detector_id)
                    }
                    detector_info['lane_area_detectors'][detector_id] = area_data
                    
                except Exception as e:
                    self.logger.warning(f"收集车道区域检测器 {detector_id} 数据时出错: {str(e)}")
            
            return detector_info
            
        except Exception as e:
            self.logger.error(f"收集检测器数据时出错: {str(e)}")
            return {
                'simulation_time': simulation_time,
                'induction_loops': {},
                'lane_area_detectors': {},
                'error': str(e)
            }
    
    def calculate_performance_metrics(self, vehicle_data: Dict[str, Any], 
                                    detector_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算性能指标
        
        Args:
            vehicle_data: 车辆数据
            detector_data: 检测器数据
            
        Returns:
            性能指标字典
        """
        try:
            metrics = {
                'simulation_time': vehicle_data['simulation_time'],
                'total_vehicles': vehicle_data['total_vehicles']
            }
            
            # 计算平均等待时间
            if vehicle_data['vehicles']:
                waiting_times = [v['waiting_time'] for v in vehicle_data['vehicles']]
                metrics['avg_waiting_time'] = sum(waiting_times) / len(waiting_times)
                metrics['max_waiting_time'] = max(waiting_times)
                metrics['min_waiting_time'] = min(waiting_times)
            else:
                metrics['avg_waiting_time'] = 0
                metrics['max_waiting_time'] = 0
                metrics['min_waiting_time'] = 0
            
            # 计算平均速度
            if vehicle_data['vehicles']:
                speeds = [v['speed'] for v in vehicle_data['vehicles']]
                metrics['avg_speed'] = sum(speeds) / len(speeds)
                metrics['max_speed'] = max(speeds)
                metrics['min_speed'] = min(speeds)
            else:
                metrics['avg_speed'] = 0
                metrics['max_speed'] = 0
                metrics['min_speed'] = 0
            
            # 计算总排队长度
            total_jam_length = 0
            total_jam_vehicles = 0
            
            for detector_id, data in detector_data['lane_area_detectors'].items():
                total_jam_length += data.get('jam_length', 0)
                total_jam_vehicles += data.get('jam_vehicle_count', 0)
            
            metrics['total_jam_length'] = total_jam_length
            metrics['total_jam_vehicles'] = total_jam_vehicles
            
            # 计算燃油消耗和排放
            if vehicle_data['vehicles']:
                fuel_consumptions = [v['fuel_consumption'] for v in vehicle_data['vehicles']]
                co2_emissions = [v['co2_emission'] for v in vehicle_data['vehicles']]
                
                metrics['total_fuel_consumption'] = sum(fuel_consumptions)
                metrics['total_co2_emission'] = sum(co2_emissions)
                metrics['avg_fuel_consumption'] = sum(fuel_consumptions) / len(fuel_consumptions)
                metrics['avg_co2_emission'] = sum(co2_emissions) / len(co2_emissions)
            else:
                metrics['total_fuel_consumption'] = 0
                metrics['total_co2_emission'] = 0
                metrics['avg_fuel_consumption'] = 0
                metrics['avg_co2_emission'] = 0
            
            # 计算通行效率
            sim_time_hours = vehicle_data['simulation_time'] / 3600
            if sim_time_hours > 0:
                metrics['throughput'] = vehicle_data['total_vehicles'] / sim_time_hours
            else:
                metrics['throughput'] = 0
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算性能指标时出错: {str(e)}")
            return {
                'simulation_time': vehicle_data.get('simulation_time', 0),
                'error': str(e)
            }
    
    def collect_all_data(self, simulation_time: float, traffic_light_id: str = 'center'):
        """
        收集所有数据
        
        Args:
            simulation_time: 仿真时间
            traffic_light_id: 信号灯ID
        """
        if simulation_time - self.last_collection_time >= self.collection_interval:
            # 收集各类数据
            vehicle_data = self.collect_vehicle_data(simulation_time)
            traffic_light_data = self.collect_traffic_light_data(simulation_time, traffic_light_id)
            detector_data = self.collect_detector_data(simulation_time)
            
            # 计算性能指标
            performance_metrics = self.calculate_performance_metrics(vehicle_data, detector_data)
            
            # 存储数据
            self.vehicle_data.append(vehicle_data)
            self.traffic_light_data.append(traffic_light_data)
            self.detector_data.append(detector_data)
            self.performance_metrics.append(performance_metrics)
            
            self.last_collection_time = simulation_time
    
    def save_data_to_json(self, algorithm_type: str):
        """
        保存数据到JSON文件
        
        Args:
            algorithm_type: 算法类型
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存车辆数据
        vehicle_file = os.path.join(self.output_dir, f"vehicle_data_{algorithm_type}_{timestamp}.json")
        with open(vehicle_file, 'w', encoding='utf-8') as f:
            json.dump(self.vehicle_data, f, indent=2, ensure_ascii=False)
        
        # 保存信号灯数据
        tl_file = os.path.join(self.output_dir, f"traffic_light_data_{algorithm_type}_{timestamp}.json")
        with open(tl_file, 'w', encoding='utf-8') as f:
            json.dump(self.traffic_light_data, f, indent=2, ensure_ascii=False)
        
        # 保存检测器数据
        detector_file = os.path.join(self.output_dir, f"detector_data_{algorithm_type}_{timestamp}.json")
        with open(detector_file, 'w', encoding='utf-8') as f:
            json.dump(self.detector_data, f, indent=2, ensure_ascii=False)
        
        # 保存性能指标
        metrics_file = os.path.join(self.output_dir, f"performance_metrics_{algorithm_type}_{timestamp}.json")
        with open(metrics_file, 'w', encoding='utf-8') as f:
            json.dump(self.performance_metrics, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"数据已保存到 {self.output_dir} 目录")
    
    def save_metrics_to_csv(self, algorithm_type: str):
        """
        保存性能指标到CSV文件
        
        Args:
            algorithm_type: 算法类型
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_file = os.path.join(self.output_dir, f"performance_metrics_{algorithm_type}_{timestamp}.csv")
        
        if self.performance_metrics:
            fieldnames = self.performance_metrics[0].keys()
            
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.performance_metrics)
            
            self.logger.info(f"性能指标CSV文件已保存: {csv_file}")
    
    def reset(self):
        """重置数据采集器"""
        self.vehicle_data = []
        self.traffic_light_data = []
        self.detector_data = []
        self.performance_metrics = []
        self.last_collection_time = 0
        self.logger.info("数据采集器已重置")
    
    def get_summary_statistics(self) -> Dict[str, Any]:
        """
        获取汇总统计信息
        
        Returns:
            汇总统计字典
        """
        if not self.performance_metrics:
            return {}
        
        # 计算各项指标的统计值
        waiting_times = [m['avg_waiting_time'] for m in self.performance_metrics if 'avg_waiting_time' in m]
        speeds = [m['avg_speed'] for m in self.performance_metrics if 'avg_speed' in m]
        jam_lengths = [m['total_jam_length'] for m in self.performance_metrics if 'total_jam_length' in m]
        
        summary = {
            'total_data_points': len(self.performance_metrics),
            'simulation_duration': max([m['simulation_time'] for m in self.performance_metrics]) if self.performance_metrics else 0
        }
        
        if waiting_times:
            summary['avg_waiting_time'] = {
                'mean': sum(waiting_times) / len(waiting_times),
                'max': max(waiting_times),
                'min': min(waiting_times)
            }
        
        if speeds:
            summary['avg_speed'] = {
                'mean': sum(speeds) / len(speeds),
                'max': max(speeds),
                'min': min(speeds)
            }
        
        if jam_lengths:
            summary['jam_length'] = {
                'mean': sum(jam_lengths) / len(jam_lengths),
                'max': max(jam_lengths),
                'min': min(jam_lengths)
            }
        
        return summary
