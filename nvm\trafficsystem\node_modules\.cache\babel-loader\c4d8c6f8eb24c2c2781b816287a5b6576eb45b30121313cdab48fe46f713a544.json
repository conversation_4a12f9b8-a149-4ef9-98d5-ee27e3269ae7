{"ast": null, "code": "import { ref, onMounted, onUnmounted } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport IntelligentTrafficPanel from '@/components/traffic/IntelligentTrafficPanel.vue';\nexport default {\n  name: 'TrafficAnalysisTest',\n  components: {\n    IntelligentTrafficPanel\n  },\n  setup() {\n    const currentVehicleCount = ref(5);\n    const isSimulating = ref(false);\n    const simulationSpeed = ref(2000);\n    const dataPoints = ref(0);\n    const eventLogs = ref([]);\n    const trafficPanel = ref(null);\n    let simulationTimer = null;\n\n    // 添加日志\n    const addLog = (message, type = 'info') => {\n      const log = {\n        time: new Date().toLocaleTimeString(),\n        message,\n        type\n      };\n      eventLogs.value.unshift(log);\n\n      // 限制日志数量\n      if (eventLogs.value.length > 50) {\n        eventLogs.value.pop();\n      }\n    };\n\n    // 生成随机车辆数量\n    const generateRandomVehicleCount = () => {\n      // 模拟真实的交通流量变化\n      const patterns = [\n      // 低峰时段 (0-5辆)\n      () => Math.floor(Math.random() * 6),\n      // 正常时段 (3-12辆)\n      () => Math.floor(Math.random() * 10) + 3,\n      // 高峰时段 (8-25辆)\n      () => Math.floor(Math.random() * 18) + 8,\n      // 拥堵时段 (15-40辆)\n      () => Math.floor(Math.random() * 26) + 15];\n      const pattern = patterns[Math.floor(Math.random() * patterns.length)];\n      return pattern();\n    };\n\n    // 开始模拟\n    const startSimulation = () => {\n      if (isSimulating.value) return;\n      isSimulating.value = true;\n      addLog('开始交通流量模拟', 'success');\n      simulationTimer = setInterval(() => {\n        currentVehicleCount.value = generateRandomVehicleCount();\n        dataPoints.value++;\n        addLog(`更新车辆数量: ${currentVehicleCount.value}`);\n      }, simulationSpeed.value);\n    };\n\n    // 停止模拟\n    const stopSimulation = () => {\n      if (!isSimulating.value) return;\n      isSimulating.value = false;\n      if (simulationTimer) {\n        clearInterval(simulationTimer);\n        simulationTimer = null;\n      }\n      addLog('停止交通流量模拟', 'warning');\n    };\n\n    // 重置数据\n    const resetData = () => {\n      stopSimulation();\n      currentVehicleCount.value = 5;\n      dataPoints.value = 0;\n      if (trafficPanel.value) {\n        trafficPanel.value.clearData();\n      }\n      addLog('重置所有数据', 'info');\n    };\n\n    // 车辆数量变化处理\n    const onVehicleCountChange = value => {\n      addLog(`手动设置车辆数量: ${value}`);\n    };\n\n    // 策略应用处理\n    const handleStrategyApplied = strategyData => {\n      addLog(`应用交通策略: ${strategyData.strategy.primary}`, 'success');\n      ElMessage.success(`策略已应用: ${strategyData.strategy.primary}`);\n    };\n\n    // 数据更新处理\n    const handleDataUpdated = trafficData => {\n      addLog(`数据更新 - 等级: ${trafficData.grade}, 趋势: ${trafficData.trend.description}`);\n    };\n\n    // 清空日志\n    const clearLog = () => {\n      eventLogs.value = [];\n      addLog('日志已清空');\n    };\n\n    // 组件挂载时初始化\n    onMounted(() => {\n      addLog('交通分析测试页面已加载', 'success');\n    });\n\n    // 组件卸载时清理\n    onUnmounted(() => {\n      stopSimulation();\n    });\n    return {\n      currentVehicleCount,\n      isSimulating,\n      simulationSpeed,\n      dataPoints,\n      eventLogs,\n      trafficPanel,\n      startSimulation,\n      stopSimulation,\n      resetData,\n      onVehicleCountChange,\n      handleStrategyApplied,\n      handleDataUpdated,\n      clearLog\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "onUnmounted", "ElMessage", "IntelligentTrafficPanel", "name", "components", "setup", "currentVehicleCount", "isSimulating", "simulationSpeed", "dataPoints", "eventLogs", "trafficPanel", "simulationTimer", "addLog", "message", "type", "log", "time", "Date", "toLocaleTimeString", "value", "unshift", "length", "pop", "generateRandomVehicleCount", "patterns", "Math", "floor", "random", "pattern", "startSimulation", "setInterval", "stopSimulation", "clearInterval", "resetData", "clearData", "onVehicleCountChange", "handleStrategyApplied", "strategyData", "strategy", "primary", "success", "handleDataUpdated", "trafficData", "grade", "trend", "description", "clearLog"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\TrafficAnalysisTest.vue"], "sourcesContent": ["<template>\n  <div class=\"traffic-analysis-test\">\n    <div class=\"test-header\">\n      <h2>智能交通状态面板测试</h2>\n      <div class=\"test-controls\">\n        <el-button @click=\"startSimulation\" :disabled=\"isSimulating\">开始模拟</el-button>\n        <el-button @click=\"stopSimulation\" :disabled=\"!isSimulating\">停止模拟</el-button>\n        <el-button @click=\"resetData\">重置数据</el-button>\n      </div>\n    </div>\n\n    <div class=\"test-content\">\n      <!-- 模拟控制面板 -->\n      <el-card class=\"simulation-control\">\n        <template #header>\n          <span>模拟控制</span>\n        </template>\n        <div class=\"control-group\">\n          <label>当前车辆数量: {{ currentVehicleCount }}</label>\n          <el-slider \n            v-model=\"currentVehicleCount\" \n            :min=\"0\" \n            :max=\"50\" \n            show-input\n            @change=\"onVehicleCountChange\"\n          />\n        </div>\n        <div class=\"control-group\">\n          <label>模拟速度 (ms):</label>\n          <el-input-number \n            v-model=\"simulationSpeed\" \n            :min=\"500\" \n            :max=\"5000\" \n            :step=\"500\"\n          />\n        </div>\n        <div class=\"status-info\">\n          <p>模拟状态: {{ isSimulating ? '运行中' : '已停止' }}</p>\n          <p>数据点数量: {{ dataPoints }}</p>\n        </div>\n      </el-card>\n\n      <!-- 智能交通状态面板 -->\n      <div class=\"panel-container\">\n        <IntelligentTrafficPanel \n          :current-vehicle-count=\"currentVehicleCount\"\n          :auto-update=\"true\"\n          @strategy-applied=\"handleStrategyApplied\"\n          @data-updated=\"handleDataUpdated\"\n          ref=\"trafficPanel\"\n        />\n      </div>\n\n      <!-- 事件日志 -->\n      <el-card class=\"event-log\">\n        <template #header>\n          <div class=\"log-header\">\n            <span>事件日志</span>\n            <el-button size=\"small\" @click=\"clearLog\">清空日志</el-button>\n          </div>\n        </template>\n        <div class=\"log-content\">\n          <div \n            v-for=\"(log, index) in eventLogs\" \n            :key=\"index\"\n            class=\"log-item\"\n            :class=\"log.type\"\n          >\n            <span class=\"log-time\">{{ log.time }}</span>\n            <span class=\"log-message\">{{ log.message }}</span>\n          </div>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport IntelligentTrafficPanel from '@/components/traffic/IntelligentTrafficPanel.vue'\n\nexport default {\n  name: 'TrafficAnalysisTest',\n  components: {\n    IntelligentTrafficPanel\n  },\n  setup() {\n    const currentVehicleCount = ref(5)\n    const isSimulating = ref(false)\n    const simulationSpeed = ref(2000)\n    const dataPoints = ref(0)\n    const eventLogs = ref([])\n    const trafficPanel = ref(null)\n    \n    let simulationTimer = null\n    \n    // 添加日志\n    const addLog = (message, type = 'info') => {\n      const log = {\n        time: new Date().toLocaleTimeString(),\n        message,\n        type\n      }\n      eventLogs.value.unshift(log)\n      \n      // 限制日志数量\n      if (eventLogs.value.length > 50) {\n        eventLogs.value.pop()\n      }\n    }\n    \n    // 生成随机车辆数量\n    const generateRandomVehicleCount = () => {\n      // 模拟真实的交通流量变化\n      const patterns = [\n        // 低峰时段 (0-5辆)\n        () => Math.floor(Math.random() * 6),\n        // 正常时段 (3-12辆)\n        () => Math.floor(Math.random() * 10) + 3,\n        // 高峰时段 (8-25辆)\n        () => Math.floor(Math.random() * 18) + 8,\n        // 拥堵时段 (15-40辆)\n        () => Math.floor(Math.random() * 26) + 15\n      ]\n      \n      const pattern = patterns[Math.floor(Math.random() * patterns.length)]\n      return pattern()\n    }\n    \n    // 开始模拟\n    const startSimulation = () => {\n      if (isSimulating.value) return\n      \n      isSimulating.value = true\n      addLog('开始交通流量模拟', 'success')\n      \n      simulationTimer = setInterval(() => {\n        currentVehicleCount.value = generateRandomVehicleCount()\n        dataPoints.value++\n        addLog(`更新车辆数量: ${currentVehicleCount.value}`)\n      }, simulationSpeed.value)\n    }\n    \n    // 停止模拟\n    const stopSimulation = () => {\n      if (!isSimulating.value) return\n      \n      isSimulating.value = false\n      if (simulationTimer) {\n        clearInterval(simulationTimer)\n        simulationTimer = null\n      }\n      addLog('停止交通流量模拟', 'warning')\n    }\n    \n    // 重置数据\n    const resetData = () => {\n      stopSimulation()\n      currentVehicleCount.value = 5\n      dataPoints.value = 0\n      if (trafficPanel.value) {\n        trafficPanel.value.clearData()\n      }\n      addLog('重置所有数据', 'info')\n    }\n    \n    // 车辆数量变化处理\n    const onVehicleCountChange = (value) => {\n      addLog(`手动设置车辆数量: ${value}`)\n    }\n    \n    // 策略应用处理\n    const handleStrategyApplied = (strategyData) => {\n      addLog(`应用交通策略: ${strategyData.strategy.primary}`, 'success')\n      ElMessage.success(`策略已应用: ${strategyData.strategy.primary}`)\n    }\n    \n    // 数据更新处理\n    const handleDataUpdated = (trafficData) => {\n      addLog(`数据更新 - 等级: ${trafficData.grade}, 趋势: ${trafficData.trend.description}`)\n    }\n    \n    // 清空日志\n    const clearLog = () => {\n      eventLogs.value = []\n      addLog('日志已清空')\n    }\n    \n    // 组件挂载时初始化\n    onMounted(() => {\n      addLog('交通分析测试页面已加载', 'success')\n    })\n    \n    // 组件卸载时清理\n    onUnmounted(() => {\n      stopSimulation()\n    })\n    \n    return {\n      currentVehicleCount,\n      isSimulating,\n      simulationSpeed,\n      dataPoints,\n      eventLogs,\n      trafficPanel,\n      startSimulation,\n      stopSimulation,\n      resetData,\n      onVehicleCountChange,\n      handleStrategyApplied,\n      handleDataUpdated,\n      clearLog\n    }\n  }\n}\n</script>\n\n<style scoped>\n.traffic-analysis-test {\n  padding: 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n.test-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.test-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.test-controls {\n  display: flex;\n  gap: 12px;\n}\n\n.test-content {\n  display: grid;\n  grid-template-columns: 300px 1fr;\n  grid-template-rows: auto 1fr;\n  gap: 20px;\n  height: calc(100vh - 200px);\n}\n\n.simulation-control {\n  grid-row: 1 / 3;\n}\n\n.panel-container {\n  grid-column: 2;\n  grid-row: 1;\n}\n\n.event-log {\n  grid-column: 2;\n  grid-row: 2;\n  max-height: 400px;\n}\n\n.control-group {\n  margin-bottom: 20px;\n}\n\n.control-group label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #333;\n}\n\n.status-info {\n  padding: 16px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  margin-top: 20px;\n}\n\n.status-info p {\n  margin: 4px 0;\n  color: #666;\n}\n\n.log-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.log-content {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.log-item {\n  display: flex;\n  gap: 12px;\n  padding: 8px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.log-item:last-child {\n  border-bottom: none;\n}\n\n.log-time {\n  font-size: 12px;\n  color: #999;\n  min-width: 80px;\n}\n\n.log-message {\n  flex: 1;\n  font-size: 14px;\n}\n\n.log-item.success .log-message {\n  color: #52c41a;\n}\n\n.log-item.warning .log-message {\n  color: #fa8c16;\n}\n\n.log-item.error .log-message {\n  color: #f5222d;\n}\n\n.log-item.info .log-message {\n  color: #333;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .test-content {\n    grid-template-columns: 1fr;\n    grid-template-rows: auto auto auto;\n  }\n  \n  .simulation-control {\n    grid-row: 1;\n    grid-column: 1;\n  }\n  \n  .panel-container {\n    grid-row: 2;\n    grid-column: 1;\n  }\n  \n  .event-log {\n    grid-row: 3;\n    grid-column: 1;\n  }\n}\n\n@media (max-width: 768px) {\n  .traffic-analysis-test {\n    padding: 16px;\n  }\n  \n  .test-header {\n    flex-direction: column;\n    gap: 16px;\n    align-items: flex-start;\n  }\n  \n  .test-controls {\n    width: 100%;\n    justify-content: flex-start;\n  }\n}\n</style>\n"], "mappings": "AA8EA,SAASA,GAAG,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AAChD,SAASC,SAAQ,QAAS,cAAa;AACvC,OAAOC,uBAAsB,MAAO,kDAAiD;AAErF,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,mBAAkB,GAAIR,GAAG,CAAC,CAAC;IACjC,MAAMS,YAAW,GAAIT,GAAG,CAAC,KAAK;IAC9B,MAAMU,eAAc,GAAIV,GAAG,CAAC,IAAI;IAChC,MAAMW,UAAS,GAAIX,GAAG,CAAC,CAAC;IACxB,MAAMY,SAAQ,GAAIZ,GAAG,CAAC,EAAE;IACxB,MAAMa,YAAW,GAAIb,GAAG,CAAC,IAAI;IAE7B,IAAIc,eAAc,GAAI,IAAG;;IAEzB;IACA,MAAMC,MAAK,GAAIA,CAACC,OAAO,EAAEC,IAAG,GAAI,MAAM,KAAK;MACzC,MAAMC,GAAE,GAAI;QACVC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;QACrCL,OAAO;QACPC;MACF;MACAL,SAAS,CAACU,KAAK,CAACC,OAAO,CAACL,GAAG;;MAE3B;MACA,IAAIN,SAAS,CAACU,KAAK,CAACE,MAAK,GAAI,EAAE,EAAE;QAC/BZ,SAAS,CAACU,KAAK,CAACG,GAAG,CAAC;MACtB;IACF;;IAEA;IACA,MAAMC,0BAAyB,GAAIA,CAAA,KAAM;MACvC;MACA,MAAMC,QAAO,GAAI;MACf;MACA,MAAMC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAI,CAAC,CAAC;MACnC;MACA,MAAMF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;MACxC;MACA,MAAMF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;MACxC;MACA,MAAMF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAI,EAAE,IAAI,EAAC,CAC1C;MAEA,MAAMC,OAAM,GAAIJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIH,QAAQ,CAACH,MAAM,CAAC;MACpE,OAAOO,OAAO,CAAC;IACjB;;IAEA;IACA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5B,IAAIvB,YAAY,CAACa,KAAK,EAAE;MAExBb,YAAY,CAACa,KAAI,GAAI,IAAG;MACxBP,MAAM,CAAC,UAAU,EAAE,SAAS;MAE5BD,eAAc,GAAImB,WAAW,CAAC,MAAM;QAClCzB,mBAAmB,CAACc,KAAI,GAAII,0BAA0B,CAAC;QACvDf,UAAU,CAACW,KAAK,EAAC;QACjBP,MAAM,CAAC,WAAWP,mBAAmB,CAACc,KAAK,EAAE;MAC/C,CAAC,EAAEZ,eAAe,CAACY,KAAK;IAC1B;;IAEA;IACA,MAAMY,cAAa,GAAIA,CAAA,KAAM;MAC3B,IAAI,CAACzB,YAAY,CAACa,KAAK,EAAE;MAEzBb,YAAY,CAACa,KAAI,GAAI,KAAI;MACzB,IAAIR,eAAe,EAAE;QACnBqB,aAAa,CAACrB,eAAe;QAC7BA,eAAc,GAAI,IAAG;MACvB;MACAC,MAAM,CAAC,UAAU,EAAE,SAAS;IAC9B;;IAEA;IACA,MAAMqB,SAAQ,GAAIA,CAAA,KAAM;MACtBF,cAAc,CAAC;MACf1B,mBAAmB,CAACc,KAAI,GAAI;MAC5BX,UAAU,CAACW,KAAI,GAAI;MACnB,IAAIT,YAAY,CAACS,KAAK,EAAE;QACtBT,YAAY,CAACS,KAAK,CAACe,SAAS,CAAC;MAC/B;MACAtB,MAAM,CAAC,QAAQ,EAAE,MAAM;IACzB;;IAEA;IACA,MAAMuB,oBAAmB,GAAKhB,KAAK,IAAK;MACtCP,MAAM,CAAC,aAAaO,KAAK,EAAE;IAC7B;;IAEA;IACA,MAAMiB,qBAAoB,GAAKC,YAAY,IAAK;MAC9CzB,MAAM,CAAC,WAAWyB,YAAY,CAACC,QAAQ,CAACC,OAAO,EAAE,EAAE,SAAS;MAC5DvC,SAAS,CAACwC,OAAO,CAAC,UAAUH,YAAY,CAACC,QAAQ,CAACC,OAAO,EAAE;IAC7D;;IAEA;IACA,MAAME,iBAAgB,GAAKC,WAAW,IAAK;MACzC9B,MAAM,CAAC,cAAc8B,WAAW,CAACC,KAAK,SAASD,WAAW,CAACE,KAAK,CAACC,WAAW,EAAE;IAChF;;IAEA;IACA,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBrC,SAAS,CAACU,KAAI,GAAI,EAAC;MACnBP,MAAM,CAAC,OAAO;IAChB;;IAEA;IACAd,SAAS,CAAC,MAAM;MACdc,MAAM,CAAC,aAAa,EAAE,SAAS;IACjC,CAAC;;IAED;IACAb,WAAW,CAAC,MAAM;MAChBgC,cAAc,CAAC;IACjB,CAAC;IAED,OAAO;MACL1B,mBAAmB;MACnBC,YAAY;MACZC,eAAe;MACfC,UAAU;MACVC,SAAS;MACTC,YAAY;MACZmB,eAAe;MACfE,cAAc;MACdE,SAAS;MACTE,oBAAoB;MACpBC,qBAAqB;MACrBK,iBAAiB;MACjBK;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}