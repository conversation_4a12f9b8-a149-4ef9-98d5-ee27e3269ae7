{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, withModifiers as _withModifiers, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"navbar navbar-expand-lg\"\n};\nconst _hoisted_2 = {\n  class: \"container\"\n};\nconst _hoisted_3 = {\n  class: \"collapse navbar-collapse\",\n  id: \"navbarNav\"\n};\nconst _hoisted_4 = {\n  class: \"navbar-nav me-auto mb-2 mb-lg-0\"\n};\nconst _hoisted_5 = {\n  class: \"nav-item dropdown\"\n};\nconst _hoisted_6 = {\n  class: \"nav-item dropdown\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"nav-item\"\n};\nconst _hoisted_8 = {\n  class: \"nav-item\"\n};\nconst _hoisted_9 = {\n  key: 1,\n  class: \"nav-item\"\n};\nconst _hoisted_10 = {\n  class: \"d-flex align-items-center\"\n};\nconst _hoisted_11 = {\n  key: 0,\n  class: \"d-flex\"\n};\nconst _hoisted_12 = {\n  class: \"navbar-text me-3\"\n};\nconst _hoisted_13 = {\n  key: 1\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"nav\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_router_link, {\n    class: \"navbar-brand\",\n    to: \"/\"\n  }, {\n    default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"交通分析系统\")])),\n    _: 1 /* STABLE */\n  }), _cache[17] || (_cache[17] = _createElementVNode(\"button\", {\n    class: \"navbar-toggler\",\n    type: \"button\",\n    \"data-bs-toggle\": \"collapse\",\n    \"data-bs-target\": \"#navbarNav\",\n    \"aria-controls\": \"navbarNav\",\n    \"aria-expanded\": \"false\",\n    \"aria-label\": \"Toggle navigation\"\n  }, [_createElementVNode(\"span\", {\n    class: \"navbar-toggler-icon\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"ul\", _hoisted_4, [_createElementVNode(\"li\", _hoisted_5, [_createElementVNode(\"a\", {\n    class: \"nav-link dropdown-toggle\",\n    href: \"#\",\n    id: \"analysisDropdown\",\n    role: \"button\",\n    onClick: _cache[0] || (_cache[0] = _withModifiers($event => $setup.toggleDropdown('analysis'), [\"prevent\"]))\n  }, \" 分析功能 \"), _createElementVNode(\"ul\", {\n    class: _normalizeClass([\"dropdown-menu\", {\n      show: $setup.activeDropdown === 'analysis'\n    }])\n  }, [_createElementVNode(\"li\", null, [_createVNode(_component_router_link, {\n    class: \"dropdown-item\",\n    to: \"/upload\",\n    onClick: $setup.closeDropdowns\n  }, {\n    default: _withCtx(() => _cache[4] || (_cache[4] = [_createElementVNode(\"i\", {\n      class: \"bi bi-image\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\" 图像分析 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])]), _createElementVNode(\"li\", null, [_createVNode(_component_router_link, {\n    class: \"dropdown-item\",\n    to: \"/video-upload\",\n    onClick: $setup.closeDropdowns\n  }, {\n    default: _withCtx(() => _cache[5] || (_cache[5] = [_createElementVNode(\"i\", {\n      class: \"bi bi-camera-video\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\" 视频分析 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])]), _cache[7] || (_cache[7] = _createElementVNode(\"li\", null, [_createElementVNode(\"hr\", {\n    class: \"dropdown-divider\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"li\", null, [_createVNode(_component_router_link, {\n    class: \"dropdown-item\",\n    to: \"/four-way-console?mode=new\",\n    onClick: $setup.closeDropdowns\n  }, {\n    default: _withCtx(() => _cache[6] || (_cache[6] = [_createElementVNode(\"i\", {\n      class: \"bi bi-speedometer2\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\" 四方向分析控制台 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])], 2 /* CLASS */)]), _createElementVNode(\"li\", _hoisted_6, [_createElementVNode(\"a\", {\n    class: \"nav-link dropdown-toggle\",\n    href: \"#\",\n    id: \"historyDropdown\",\n    role: \"button\",\n    onClick: _cache[1] || (_cache[1] = _withModifiers($event => $setup.toggleDropdown('history'), [\"prevent\"]))\n  }, \" 历史记录 \"), _createElementVNode(\"ul\", {\n    class: _normalizeClass([\"dropdown-menu\", {\n      show: $setup.activeDropdown === 'history'\n    }])\n  }, [_createElementVNode(\"li\", null, [_createVNode(_component_router_link, {\n    class: \"dropdown-item\",\n    to: \"/history\",\n    onClick: $setup.closeDropdowns\n  }, {\n    default: _withCtx(() => _cache[8] || (_cache[8] = [_createElementVNode(\"i\", {\n      class: \"bi bi-image\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\" 图像分析历史 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])]), _createElementVNode(\"li\", null, [_createVNode(_component_router_link, {\n    class: \"dropdown-item\",\n    to: \"/video-history\",\n    onClick: $setup.closeDropdowns\n  }, {\n    default: _withCtx(() => _cache[9] || (_cache[9] = [_createElementVNode(\"i\", {\n      class: \"bi bi-camera-video\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\" 视频分析历史 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])]), _cache[11] || (_cache[11] = _createElementVNode(\"li\", null, [_createElementVNode(\"hr\", {\n    class: \"dropdown-divider\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"li\", null, [_createVNode(_component_router_link, {\n    class: \"dropdown-item\",\n    to: \"/four-way-history\",\n    onClick: $setup.closeDropdowns\n  }, {\n    default: _withCtx(() => _cache[10] || (_cache[10] = [_createElementVNode(\"i\", {\n      class: \"bi bi-intersection\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\" 四方向分析历史 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])], 2 /* CLASS */)]), $setup.user ? (_openBlock(), _createElementBlock(\"li\", _hoisted_7, [_createVNode(_component_router_link, {\n    class: _normalizeClass([\"nav-link\", {\n      active: _ctx.$route.path === '/user/profile'\n    }]),\n    to: \"/user/profile\",\n    onClick: $setup.closeDropdowns\n  }, {\n    default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"个人信息\")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\", \"onClick\"])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"li\", _hoisted_8, [_createVNode(_component_router_link, {\n    class: _normalizeClass([\"nav-link\", {\n      active: _ctx.$route.path === '/settings'\n    }]),\n    to: \"/settings\",\n    onClick: $setup.closeDropdowns\n  }, {\n    default: _withCtx(() => _cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n      class: \"bi bi-gear\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\" 系统设置 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\", \"onClick\"])]), $setup.isAdmin ? (_openBlock(), _createElementBlock(\"li\", _hoisted_9, [_createVNode(_component_router_link, {\n    class: _normalizeClass([\"nav-link\", {\n      active: _ctx.$route.path === '/admin/users'\n    }]),\n    to: \"/admin/users\",\n    onClick: $setup.closeDropdowns\n  }, {\n    default: _withCtx(() => _cache[14] || (_cache[14] = [_createElementVNode(\"i\", {\n      class: \"bi bi-people\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\" 用户管理 \"), _createElementVNode(\"span\", {\n      class: \"admin-badge\"\n    }, \"管理员\", -1 /* HOISTED */)])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\", \"onClick\"])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_10, [$setup.user ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createElementVNode(\"span\", _hoisted_12, \" 欢迎, \" + _toDisplayString($setup.user.username), 1 /* TEXT */), _createElementVNode(\"button\", {\n    class: \"logout-btn\",\n    type: \"button\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $setup.logout && $setup.logout(...args))\n  }, \"退出\")])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createVNode(_component_router_link, {\n    class: \"auth-btn login-btn\",\n    to: \"/login\"\n  }, {\n    default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"登录\")])),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_router_link, {\n    class: \"auth-btn register-btn\",\n    to: \"/register\"\n  }, {\n    default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\"注册\")])),\n    _: 1 /* STABLE */\n  })]))])])])]);\n}", "map": {"version": 3, "names": ["class", "id", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_router_link", "to", "default", "_withCtx", "_cache", "_createTextVNode", "_", "type", "_hoisted_3", "_hoisted_4", "_hoisted_5", "href", "role", "onClick", "_withModifiers", "$event", "$setup", "toggleDropdown", "_normalizeClass", "show", "activeDropdown", "closeDropdowns", "_hoisted_6", "user", "_hoisted_7", "active", "_ctx", "$route", "path", "_createCommentVNode", "_hoisted_8", "isAdmin", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_toDisplayString", "username", "args", "logout", "_hoisted_13"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\common\\Navbar.vue"], "sourcesContent": ["<template>\n  <nav class=\"navbar navbar-expand-lg\">\n    <div class=\"container\">\n      <router-link class=\"navbar-brand\" to=\"/\">交通分析系统</router-link>\n      <button class=\"navbar-toggler\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#navbarNav\" aria-controls=\"navbarNav\" aria-expanded=\"false\" aria-label=\"Toggle navigation\">\n        <span class=\"navbar-toggler-icon\"></span>\n      </button>\n      <div class=\"collapse navbar-collapse\" id=\"navbarNav\">\n        <ul class=\"navbar-nav me-auto mb-2 mb-lg-0\">\n          <li class=\"nav-item dropdown\">\n            <a class=\"nav-link dropdown-toggle\" href=\"#\" id=\"analysisDropdown\" role=\"button\" @click.prevent=\"toggleDropdown('analysis')\">\n              分析功能\n            </a>\n            <ul class=\"dropdown-menu\" :class=\"{ show: activeDropdown === 'analysis' }\">\n              <li>\n                <router-link class=\"dropdown-item\" to=\"/upload\" @click=\"closeDropdowns\">\n                  <i class=\"bi bi-image\"></i> 图像分析\n                </router-link>\n              </li>\n              <li>\n                <router-link class=\"dropdown-item\" to=\"/video-upload\" @click=\"closeDropdowns\">\n                  <i class=\"bi bi-camera-video\"></i> 视频分析\n                </router-link>\n              </li>\n\n              <li><hr class=\"dropdown-divider\"></li>\n              <li>\n                <router-link class=\"dropdown-item\" to=\"/four-way-console?mode=new\" @click=\"closeDropdowns\">\n                  <i class=\"bi bi-speedometer2\"></i> 四方向分析控制台\n                </router-link>\n              </li>\n            </ul>\n          </li>\n          <li class=\"nav-item dropdown\">\n            <a class=\"nav-link dropdown-toggle\" href=\"#\" id=\"historyDropdown\" role=\"button\" @click.prevent=\"toggleDropdown('history')\">\n              历史记录\n            </a>\n            <ul class=\"dropdown-menu\" :class=\"{ show: activeDropdown === 'history' }\">\n              <li>\n                <router-link class=\"dropdown-item\" to=\"/history\" @click=\"closeDropdowns\">\n                  <i class=\"bi bi-image\"></i> 图像分析历史\n                </router-link>\n              </li>\n              <li>\n                <router-link class=\"dropdown-item\" to=\"/video-history\" @click=\"closeDropdowns\">\n                  <i class=\"bi bi-camera-video\"></i> 视频分析历史\n                </router-link>\n              </li>\n              <li><hr class=\"dropdown-divider\"></li>\n              <li>\n                <router-link class=\"dropdown-item\" to=\"/four-way-history\" @click=\"closeDropdowns\">\n                  <i class=\"bi bi-intersection\"></i> 四方向分析历史\n                </router-link>\n              </li>\n            </ul>\n          </li>\n          <li class=\"nav-item\" v-if=\"user\">\n            <router-link class=\"nav-link\" :class=\"{ active: $route.path === '/user/profile' }\" to=\"/user/profile\" @click=\"closeDropdowns\">个人信息</router-link>\n          </li>\n          <li class=\"nav-item\">\n            <router-link class=\"nav-link\" :class=\"{ active: $route.path === '/settings' }\" to=\"/settings\" @click=\"closeDropdowns\">\n              <i class=\"bi bi-gear\"></i> 系统设置\n            </router-link>\n          </li>\n          <li class=\"nav-item\" v-if=\"isAdmin\">\n            <router-link class=\"nav-link\" :class=\"{ active: $route.path === '/admin/users' }\" to=\"/admin/users\" @click=\"closeDropdowns\">\n              <i class=\"bi bi-people\"></i> 用户管理\n              <span class=\"admin-badge\">管理员</span>\n            </router-link>\n          </li>\n        </ul>\n        <div class=\"d-flex align-items-center\">\n          <div class=\"d-flex\" v-if=\"user\">\n            <span class=\"navbar-text me-3\">\n              欢迎, {{ user.username }}\n            </span>\n            <button class=\"logout-btn\" type=\"button\" @click=\"logout\">退出</button>\n          </div>\n          <div v-else>\n            <router-link class=\"auth-btn login-btn\" to=\"/login\">登录</router-link>\n            <router-link class=\"auth-btn register-btn\" to=\"/register\">注册</router-link>\n          </div>\n        </div>\n      </div>\n    </div>\n  </nav>\n</template>\n\n<script>\nimport { ref, onMounted, computed, watch, onUnmounted } from 'vue';\nimport { useRouter } from 'vue-router';\n\nexport default {\n  name: 'NavbarComponent',\n  setup() {\n    const router = useRouter();\n    const user = ref(null);\n    const activeDropdown = ref(null);\n    \n    // 计算属性：检查用户是否为管理员\n    const isAdmin = computed(() => {\n      if (!user.value) return false;\n      \n      // 兼容不同格式的管理员角色名称（大小写不敏感比较）\n      const role = user.value.role?.toLowerCase() || '';\n      return role === 'admin' || role === 'administrator';\n    });\n    \n    // 切换下拉菜单显示状态\n    const toggleDropdown = (dropdownId) => {\n      console.log(`切换下拉菜单: ${dropdownId}, 当前状态: ${activeDropdown.value === dropdownId ? '开启' : '关闭'}`);\n      \n      // 如果点击当前已打开的下拉菜单，则关闭它\n      if (activeDropdown.value === dropdownId) {\n        activeDropdown.value = null;\n        console.log(`关闭下拉菜单: ${dropdownId}`);\n      } else {\n        // 否则打开新的下拉菜单，并关闭其他已打开的菜单\n        activeDropdown.value = dropdownId;\n        console.log(`打开下拉菜单: ${dropdownId}`);\n      }\n    };\n    \n    // 关闭所有下拉菜单\n    const closeDropdowns = () => {\n      if (activeDropdown.value) {\n        console.log(`关闭活动下拉菜单: ${activeDropdown.value}`);\n        activeDropdown.value = null;\n      }\n    };\n    \n    // 处理页面点击事件，点击菜单外部时关闭下拉菜单\n    const handleOutsideClick = (event) => {\n      // 如果没有活动的下拉菜单，无需处理\n      if (!activeDropdown.value) return;\n      \n      // 检查点击是否发生在下拉菜单或下拉菜单触发器上\n      const isClickOnDropdown = event.target.closest('.dropdown-menu');\n      const isClickOnDropdownToggle = event.target.closest('.dropdown-toggle');\n      \n      // 如果点击不是发生在下拉菜单或其触发器上，则关闭所有下拉菜单\n      if (!isClickOnDropdown && !isClickOnDropdownToggle) {\n        closeDropdowns();\n      }\n    };\n    \n    onMounted(() => {\n      // 读取用户信息\n      const userInfo = localStorage.getItem('user');\n      if (userInfo) {\n        user.value = JSON.parse(userInfo);\n      }\n      \n      // 添加点击事件监听，用于处理点击菜单外部区域\n      document.addEventListener('click', handleOutsideClick);\n    });\n    \n    // 组件卸载时清理事件\n    onUnmounted(() => {\n      document.removeEventListener('click', handleOutsideClick);\n    });\n    \n    // 退出登录\n    const logout = () => {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n      user.value = null;\n      activeDropdown.value = null; // 关闭任何打开的下拉菜单\n      router.push('/login');\n    };\n    \n    return {\n      user,\n      logout,\n      isAdmin,\n      activeDropdown,\n      toggleDropdown,\n      closeDropdowns\n    };\n  }\n};\n</script>\n\n<style scoped>\n.navbar {\n  background-color: #111827;\n  padding: 1rem 0;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.navbar-brand {\n  font-weight: 800;\n  font-size: 1.5rem;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6);\n  -webkit-background-clip: text;\n  background-clip: text;\n  color: transparent;\n  text-decoration: none;\n  padding: 0.5rem 0;\n  transition: none;\n}\n\n.navbar-brand:hover {\n  transform: none;\n  opacity: 1;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6);\n  -webkit-background-clip: text;\n  background-clip: text;\n  color: transparent;\n}\n\n.navbar-nav {\n  margin-left: 2rem;\n}\n\n.nav-item {\n  margin: 0 0.5rem;\n  position: relative;\n}\n\n.nav-link {\n  color: #d1d5db;\n  font-weight: 500;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  transition: none;\n  text-decoration: none;\n}\n\n.nav-link:hover,\n.nav-link.active,\n.nav-link:focus,\n.nav-link:active,\n.nav-link:visited {\n  color: #d1d5db !important;\n  background-color: transparent !important;\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.nav-link i {\n  margin-right: 0.5rem;\n  color: #3b82f6;\n}\n\n.dropdown-toggle {\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n}\n\n.dropdown-toggle::after {\n  margin-left: 0.5rem;\n  border-top: 0.3em solid #3b82f6;\n}\n\n.dropdown-menu {\n  display: none;\n  position: absolute;\n  background-color: #1a2032;\n  border: 1px solid rgba(255, 255, 255, 0.08);\n  border-radius: 0.75rem;\n  min-width: 10rem;\n  padding: 0.5rem 0;\n  margin-top: 0.5rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  z-index: 1000;\n  overflow: hidden;\n  animation: dropdown-appear 0.2s ease;\n}\n\n@keyframes dropdown-appear {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n.dropdown-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 0.75rem 1.25rem;\n  color: #d1d5db;\n  text-decoration: none;\n  transition: none;\n  height: 36px;\n  line-height: normal;\n}\n\n.dropdown-item:hover,\n.dropdown-item:focus,\n.dropdown-item:active,\n.dropdown-item:visited {\n  background-color: transparent !important;\n  color: #d1d5db !important;\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.dropdown-item i {\n  color: #3b82f6;\n  font-size: 1rem;\n}\n\n.navbar-text {\n  color: #d1d5db;\n  padding: 0.5rem 1rem;\n}\n\n.admin-badge {\n  display: inline-block;\n  background: linear-gradient(135deg, #f59e0b, #d97706);\n  color: #111827;\n  font-size: 0.7rem;\n  font-weight: 700;\n  padding: 0.2rem 0.5rem;\n  border-radius: 1rem;\n  margin-left: 0.5rem;\n  text-transform: uppercase;\n}\n\n.auth-btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.5rem 1.25rem;\n  border-radius: 0.75rem;\n  font-weight: 600;\n  transition: none;\n  text-decoration: none;\n  margin-left: 0.5rem;\n  font-size: 0.9rem;\n}\n\n.login-btn {\n  background-color: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  color: #d1d5db;\n}\n\n.login-btn:hover,\n.login-btn:focus,\n.login-btn:active,\n.login-btn:visited {\n  background-color: transparent !important;\n  color: #d1d5db !important;\n  transform: none !important;\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.register-btn {\n  background: linear-gradient(135deg, #3b82f6, #2563eb);\n  color: white;\n  border: none;\n}\n\n.register-btn:hover,\n.register-btn:focus,\n.register-btn:active,\n.register-btn:visited {\n  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;\n  color: white !important;\n  transform: none !important;\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.logout-btn {\n  background: rgba(255, 255, 255, 0.05);\n  color: #d1d5db;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 0.5rem 1.25rem;\n  border-radius: 0.75rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: none;\n}\n\n.logout-btn:hover,\n.logout-btn:focus,\n.logout-btn:active,\n.logout-btn:visited {\n  background: rgba(255, 255, 255, 0.05) !important;\n  color: #d1d5db !important;\n  border-color: rgba(255, 255, 255, 0.1) !important;\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.navbar-toggler {\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  background-color: rgba(255, 255, 255, 0.03);\n}\n\n.navbar-toggler-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\");\n}\n\n/* 全局防止导航栏元素点击后变色 */\n.navbar a,\n.navbar button,\n.navbar .nav-link,\n.navbar .dropdown-item,\n.navbar .auth-btn {\n  -webkit-tap-highlight-color: transparent !important;\n  -webkit-touch-callout: none !important;\n  -webkit-user-select: none !important;\n  -moz-user-select: none !important;\n  -ms-user-select: none !important;\n  user-select: none !important;\n}\n\n.navbar a:focus,\n.navbar button:focus,\n.navbar .nav-link:focus,\n.navbar .dropdown-item:focus,\n.navbar .auth-btn:focus {\n  outline: none !important;\n  box-shadow: none !important;\n}\n\n.navbar a:active,\n.navbar button:active,\n.navbar .nav-link:active,\n.navbar .dropdown-item:active,\n.navbar .auth-btn:active {\n  outline: none !important;\n  box-shadow: none !important;\n  transform: none !important;\n}\n\n@media (max-width: 992px) {\n  .navbar-nav {\n    margin-left: 0;\n    margin-top: 1rem;\n  }\n  \n  .nav-item {\n    margin: 0;\n  }\n  \n  .dropdown-menu {\n    position: static;\n    background-color: rgba(26, 32, 50, 0.5);\n    border: none;\n    box-shadow: none;\n    width: 100%;\n    margin-top: 0.25rem;\n    margin-bottom: 0.25rem;\n    padding-left: 1.5rem;\n  }\n  \n  .auth-btn {\n    margin: 0.5rem 0;\n    width: 100%;\n    justify-content: center;\n  }\n}\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAW;;EAKfA,KAAK,EAAC,0BAA0B;EAACC,EAAE,EAAC;;;EACnCD,KAAK,EAAC;AAAiC;;EACrCA,KAAK,EAAC;AAAmB;;EAwBzBA,KAAK,EAAC;AAAmB;;EAjCvCE,GAAA;EAwDcF,KAAK,EAAC;;;EAGNA,KAAK,EAAC;AAAU;;EA3D9BE,GAAA;EAgEcF,KAAK,EAAC;;;EAOPA,KAAK,EAAC;AAA2B;;EAvE9CE,GAAA;EAwEeF,KAAK,EAAC;;;EACHA,KAAK,EAAC;AAAkB;;EAzE1CE,GAAA;AAAA;;;uBACEC,mBAAA,CAoFM,OApFNC,UAoFM,GAnFJC,mBAAA,CAkFM,OAlFNC,UAkFM,GAjFJC,YAAA,CAA6DC,sBAAA;IAAhDR,KAAK,EAAC,cAAc;IAACS,EAAE,EAAC;;IAH3CC,OAAA,EAAAC,QAAA,CAG+C,MAAMC,MAAA,QAAAA,MAAA,OAHrDC,gBAAA,CAG+C,QAAM,E;IAHrDC,CAAA;kCAIMT,mBAAA,CAES;IAFDL,KAAK,EAAC,gBAAgB;IAACe,IAAI,EAAC,QAAQ;IAAC,gBAAc,EAAC,UAAU;IAAC,gBAAc,EAAC,YAAY;IAAC,eAAa,EAAC,WAAW;IAAC,eAAa,EAAC,OAAO;IAAC,YAAU,EAAC;MAC5JV,mBAAA,CAAyC;IAAnCL,KAAK,EAAC;EAAqB,G,sBAEnCK,mBAAA,CA4EM,OA5ENW,UA4EM,GA3EJX,mBAAA,CA8DK,MA9DLY,UA8DK,GA7DHZ,mBAAA,CAuBK,MAvBLa,UAuBK,GAtBHb,mBAAA,CAEI;IAFDL,KAAK,EAAC,0BAA0B;IAACmB,IAAI,EAAC,GAAG;IAAClB,EAAE,EAAC,kBAAkB;IAACmB,IAAI,EAAC,QAAQ;IAAEC,OAAK,EAAAT,MAAA,QAAAA,MAAA,MAVnGU,cAAA,CAAAC,MAAA,IAU6GC,MAAA,CAAAC,cAAc;KAAc,QAE7H,GACApB,mBAAA,CAkBK;IAlBDL,KAAK,EAbrB0B,eAAA,EAasB,eAAe;MAAAC,IAAA,EAAiBH,MAAA,CAAAI,cAAc;IAAA;MACtDvB,mBAAA,CAIK,aAHHE,YAAA,CAEcC,sBAAA;IAFDR,KAAK,EAAC,eAAe;IAACS,EAAE,EAAC,SAAS;IAAEY,OAAK,EAAEG,MAAA,CAAAK;;IAfxEnB,OAAA,EAAAC,QAAA,CAgBkB,MAA2BC,MAAA,QAAAA,MAAA,OAA3BP,mBAAA,CAA2B;MAAxBL,KAAK,EAAC;IAAa,4BAhBxCa,gBAAA,CAgB6C,QAC7B,E;IAjBhBC,CAAA;oCAmBcT,mBAAA,CAIK,aAHHE,YAAA,CAEcC,sBAAA;IAFDR,KAAK,EAAC,eAAe;IAACS,EAAE,EAAC,eAAe;IAAEY,OAAK,EAAEG,MAAA,CAAAK;;IApB9EnB,OAAA,EAAAC,QAAA,CAqBkB,MAAkCC,MAAA,QAAAA,MAAA,OAAlCP,mBAAA,CAAkC;MAA/BL,KAAK,EAAC;IAAoB,4BArB/Ca,gBAAA,CAqBoD,QACpC,E;IAtBhBC,CAAA;8DAyBcT,mBAAA,CAAsC,aAAlCA,mBAAA,CAA6B;IAAzBL,KAAK,EAAC;EAAkB,G,sBAChCK,mBAAA,CAIK,aAHHE,YAAA,CAEcC,sBAAA;IAFDR,KAAK,EAAC,eAAe;IAACS,EAAE,EAAC,4BAA4B;IAAEY,OAAK,EAAEG,MAAA,CAAAK;;IA3B3FnB,OAAA,EAAAC,QAAA,CA4BkB,MAAkCC,MAAA,QAAAA,MAAA,OAAlCP,mBAAA,CAAkC;MAA/BL,KAAK,EAAC;IAAoB,4BA5B/Ca,gBAAA,CA4BoD,YACpC,E;IA7BhBC,CAAA;uDAiCUT,mBAAA,CAsBK,MAtBLyB,UAsBK,GArBHzB,mBAAA,CAEI;IAFDL,KAAK,EAAC,0BAA0B;IAACmB,IAAI,EAAC,GAAG;IAAClB,EAAE,EAAC,iBAAiB;IAACmB,IAAI,EAAC,QAAQ;IAAEC,OAAK,EAAAT,MAAA,QAAAA,MAAA,MAlClGU,cAAA,CAAAC,MAAA,IAkC4GC,MAAA,CAAAC,cAAc;KAAa,QAE3H,GACApB,mBAAA,CAiBK;IAjBDL,KAAK,EArCrB0B,eAAA,EAqCsB,eAAe;MAAAC,IAAA,EAAiBH,MAAA,CAAAI,cAAc;IAAA;MACtDvB,mBAAA,CAIK,aAHHE,YAAA,CAEcC,sBAAA;IAFDR,KAAK,EAAC,eAAe;IAACS,EAAE,EAAC,UAAU;IAAEY,OAAK,EAAEG,MAAA,CAAAK;;IAvCzEnB,OAAA,EAAAC,QAAA,CAwCkB,MAA2BC,MAAA,QAAAA,MAAA,OAA3BP,mBAAA,CAA2B;MAAxBL,KAAK,EAAC;IAAa,4BAxCxCa,gBAAA,CAwC6C,UAC7B,E;IAzChBC,CAAA;oCA2CcT,mBAAA,CAIK,aAHHE,YAAA,CAEcC,sBAAA;IAFDR,KAAK,EAAC,eAAe;IAACS,EAAE,EAAC,gBAAgB;IAAEY,OAAK,EAAEG,MAAA,CAAAK;;IA5C/EnB,OAAA,EAAAC,QAAA,CA6CkB,MAAkCC,MAAA,QAAAA,MAAA,OAAlCP,mBAAA,CAAkC;MAA/BL,KAAK,EAAC;IAAoB,4BA7C/Ca,gBAAA,CA6CoD,UACpC,E;IA9ChBC,CAAA;gEAgDcT,mBAAA,CAAsC,aAAlCA,mBAAA,CAA6B;IAAzBL,KAAK,EAAC;EAAkB,G,sBAChCK,mBAAA,CAIK,aAHHE,YAAA,CAEcC,sBAAA;IAFDR,KAAK,EAAC,eAAe;IAACS,EAAE,EAAC,mBAAmB;IAAEY,OAAK,EAAEG,MAAA,CAAAK;;IAlDlFnB,OAAA,EAAAC,QAAA,CAmDkB,MAAkCC,MAAA,SAAAA,MAAA,QAAlCP,mBAAA,CAAkC;MAA/BL,KAAK,EAAC;IAAoB,4BAnD/Ca,gBAAA,CAmDoD,WACpC,E;IApDhBC,CAAA;uDAwDqCU,MAAA,CAAAO,IAAI,I,cAA/B5B,mBAAA,CAEK,MAFL6B,UAEK,GADHzB,YAAA,CAAgJC,sBAAA;IAAnIR,KAAK,EAzD9B0B,eAAA,EAyD+B,UAAU;MAAAO,MAAA,EAAmBC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAAwB3B,EAAE,EAAC,eAAe;IAAEY,OAAK,EAAEG,MAAA,CAAAK;;IAzD1HnB,OAAA,EAAAC,QAAA,CAyD0I,MAAIC,MAAA,SAAAA,MAAA,QAzD9IC,gBAAA,CAyD0I,MAAI,E;IAzD9IC,CAAA;+CAAAuB,mBAAA,gBA2DUhC,mBAAA,CAIK,MAJLiC,UAIK,GAHH/B,YAAA,CAEcC,sBAAA;IAFDR,KAAK,EA5D9B0B,eAAA,EA4D+B,UAAU;MAAAO,MAAA,EAAmBC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAAoB3B,EAAE,EAAC,WAAW;IAAEY,OAAK,EAAEG,MAAA,CAAAK;;IA5DlHnB,OAAA,EAAAC,QAAA,CA6Dc,MAA0BC,MAAA,SAAAA,MAAA,QAA1BP,mBAAA,CAA0B;MAAvBL,KAAK,EAAC;IAAY,4BA7DnCa,gBAAA,CA6DwC,QAC5B,E;IA9DZC,CAAA;6CAgEqCU,MAAA,CAAAe,OAAO,I,cAAlCpC,mBAAA,CAKK,MALLqC,UAKK,GAJHjC,YAAA,CAGcC,sBAAA;IAHDR,KAAK,EAjE9B0B,eAAA,EAiE+B,UAAU;MAAAO,MAAA,EAAmBC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAAuB3B,EAAE,EAAC,cAAc;IAAEY,OAAK,EAAEG,MAAA,CAAAK;;IAjExHnB,OAAA,EAAAC,QAAA,CAkEc,MAA4BC,MAAA,SAAAA,MAAA,QAA5BP,mBAAA,CAA4B;MAAzBL,KAAK,EAAC;IAAc,4BAlErCa,gBAAA,CAkE0C,QAC5B,GAAAR,mBAAA,CAAoC;MAA9BL,KAAK,EAAC;IAAa,GAAC,KAAG,oB;IAnE3Cc,CAAA;+CAAAuB,mBAAA,e,GAuEQhC,mBAAA,CAWM,OAXNoC,WAWM,GAVsBjB,MAAA,CAAAO,IAAI,I,cAA9B5B,mBAAA,CAKM,OALNuC,WAKM,GAJJrC,mBAAA,CAEO,QAFPsC,WAEO,EAFwB,OACzB,GAAAC,gBAAA,CAAGpB,MAAA,CAAAO,IAAI,CAACc,QAAQ,kBAEtBxC,mBAAA,CAAoE;IAA5DL,KAAK,EAAC,YAAY;IAACe,IAAI,EAAC,QAAQ;IAAEM,OAAK,EAAAT,MAAA,QAAAA,MAAA,UAAAkC,IAAA,KAAEtB,MAAA,CAAAuB,MAAA,IAAAvB,MAAA,CAAAuB,MAAA,IAAAD,IAAA,CAAM;KAAE,IAAE,E,oBAE7D3C,mBAAA,CAGM,OAjFhB6C,WAAA,GA+EYzC,YAAA,CAAoEC,sBAAA;IAAvDR,KAAK,EAAC,oBAAoB;IAACS,EAAE,EAAC;;IA/EvDC,OAAA,EAAAC,QAAA,CA+EgE,MAAEC,MAAA,SAAAA,MAAA,QA/ElEC,gBAAA,CA+EgE,IAAE,E;IA/ElEC,CAAA;MAgFYP,YAAA,CAA0EC,sBAAA;IAA7DR,KAAK,EAAC,uBAAuB;IAACS,EAAE,EAAC;;IAhF1DC,OAAA,EAAAC,QAAA,CAgFsE,MAAEC,MAAA,SAAAA,MAAA,QAhFxEC,gBAAA,CAgFsE,IAAE,E;IAhFxEC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}