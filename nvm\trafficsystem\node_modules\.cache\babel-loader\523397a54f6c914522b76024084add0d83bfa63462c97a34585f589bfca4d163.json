{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { computed, ref } from 'vue';\nimport { TrendCharts, Warning, Setting, InfoFilled, SuccessFilled, WarnTriangleFilled, Clock } from '@element-plus/icons-vue';\nexport default {\n  name: 'IntelligentTrafficStatusPanel',\n  components: {\n    TrendCharts,\n    Warning,\n    Setting,\n    InfoFilled,\n    SuccessFilled,\n    WarnTriangleFilled,\n    Clock\n  },\n  props: {\n    directionStats: {\n      type: Object,\n      required: true\n    },\n    lastUpdateTime: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['apply-suggestion'],\n  setup(props, {\n    emit\n  }) {\n    const directions = [{\n      key: 'north',\n      name: '北向'\n    }, {\n      key: 'east',\n      name: '东向'\n    }, {\n      key: 'south',\n      name: '南向'\n    }, {\n      key: 'west',\n      name: '西向'\n    }];\n\n    // 获取当前帧车辆数\n    const getCurrentVehicleCount = direction => {\n      return props.directionStats[direction]?.vehicleCount || 0;\n    };\n\n    // 获取移动平均\n    const getMovingAverage = direction => {\n      return props.directionStats[direction]?.movingAverage || 0;\n    };\n\n    // 获取方向进度\n    const getDirectionProgress = direction => {\n      const stats = props.directionStats[direction];\n      if (!stats || !stats.totalFrames) return 0;\n      return Math.round((stats.currentFrame || 0) / stats.totalFrames * 100);\n    };\n\n    // 获取当前帧数\n    const getCurrentFrame = direction => {\n      return props.directionStats[direction]?.currentFrame || 0;\n    };\n\n    // 获取总帧数\n    const getTotalFrames = direction => {\n      return props.directionStats[direction]?.totalFrames || 0;\n    };\n\n    // 获取方向状态类型\n    const getDirectionStatusType = direction => {\n      const status = props.directionStats[direction]?.status || 'waiting';\n      const typeMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'error': 'danger'\n      };\n      return typeMap[status] || 'info';\n    };\n\n    // 获取方向状态文本\n    const getDirectionStatusText = direction => {\n      const status = props.directionStats[direction]?.status || 'waiting';\n      const textMap = {\n        'waiting': '等待中',\n        'processing': '检测中',\n        'completed': '已完成',\n        'error': '检测失败'\n      };\n      return textMap[status] || '未知';\n    };\n\n    // 获取进度状态\n    const getProgressStatus = direction => {\n      const progress = getDirectionProgress(direction);\n      if (progress === 100) return 'success';\n      if (progress > 0) return undefined;\n      return 'exception';\n    };\n\n    // 获取方向卡片样式类\n    const getDirectionCardClass = direction => {\n      const status = props.directionStats[direction]?.status || 'waiting';\n      return `status-${status}`;\n    };\n\n    // 拥堵等级计算\n    const getCongestionLevel = () => {\n      const totalVehicles = directions.reduce((sum, dir) => sum + getCurrentVehicleCount(dir.key), 0);\n      const avgMoving = directions.reduce((sum, dir) => sum + getMovingAverage(dir.key), 0) / 4;\n      if (avgMoving >= 8 || totalVehicles >= 20) return '严重拥堵';\n      if (avgMoving >= 5 || totalVehicles >= 12) return '中度拥堵';\n      if (avgMoving >= 3 || totalVehicles >= 6) return '轻度拥堵';\n      return '畅通';\n    };\n    const getCongestionLevelClass = () => {\n      const level = getCongestionLevel();\n      const classMap = {\n        '严重拥堵': 'severe',\n        '中度拥堵': 'moderate',\n        '轻度拥堵': 'light',\n        '畅通': 'smooth'\n      };\n      return classMap[level] || 'smooth';\n    };\n    const getCongestionDescription = () => {\n      const level = getCongestionLevel();\n      const descMap = {\n        '严重拥堵': '交通严重拥堵，建议立即采取疏导措施',\n        '中度拥堵': '交通较为拥堵，需要关注流量变化',\n        '轻度拥堵': '交通略有拥堵，保持正常监控',\n        '畅通': '交通状况良好，运行顺畅'\n      };\n      return descMap[level] || '状态未知';\n    };\n    const getCongestionIndex = () => {\n      const totalVehicles = directions.reduce((sum, dir) => sum + getCurrentVehicleCount(dir.key), 0);\n      const avgMoving = directions.reduce((sum, dir) => sum + getMovingAverage(dir.key), 0) / 4;\n\n      // 综合计算拥堵指数 (0-100)\n      const vehicleIndex = Math.min(totalVehicles * 2.5, 50);\n      const movingIndex = Math.min(avgMoving * 6, 50);\n      return Math.round(vehicleIndex + movingIndex);\n    };\n    const getCongestionTrend = () => {\n      const index = getCongestionIndex();\n      if (index >= 80) return '↗️ 恶化';\n      if (index >= 60) return '→ 稳定';\n      if (index >= 40) return '↘️ 改善';\n      return '✅ 良好';\n    };\n\n    // 交通流平衡度计算\n    const getTrafficFlowBalance = () => {\n      const counts = directions.map(dir => getMovingAverage(dir.key));\n      const max = Math.max(...counts);\n      const min = Math.min(...counts);\n      if (max === 0) return 100;\n      const balance = (max - min) / max * 100;\n      return Math.round(100 - balance);\n    };\n    const getBalanceStatus = () => {\n      const balance = getTrafficFlowBalance();\n      if (balance >= 80) return '非常均衡';\n      if (balance >= 60) return '较为均衡';\n      if (balance >= 40) return '不够均衡';\n      return '严重不均衡';\n    };\n    const getBalanceColor = () => {\n      const balance = getTrafficFlowBalance();\n      if (balance >= 80) return '#10b981';\n      if (balance >= 60) return '#f59e0b';\n      if (balance >= 40) return '#f97316';\n      return '#ef4444';\n    };\n\n    // 管理建议生成\n    const getManagementSuggestions = () => {\n      const suggestions = [];\n      const congestionLevel = getCongestionLevel();\n      const balance = getTrafficFlowBalance();\n      const totalVehicles = directions.reduce((sum, dir) => sum + getCurrentVehicleCount(dir.key), 0);\n\n      // 基于拥堵等级的建议\n      if (congestionLevel === '严重拥堵') {\n        suggestions.push({\n          id: 'severe-congestion',\n          title: '紧急疏导措施',\n          content: '检测到严重拥堵，建议立即启动应急预案，增加信号灯绿灯时长，派遣交警现场疏导。',\n          priority: 'high',\n          tagType: 'danger',\n          icon: WarnTriangleFilled,\n          iconClass: 'danger-icon',\n          action: '启动应急预案',\n          actionType: 'danger'\n        });\n      } else if (congestionLevel === '中度拥堵') {\n        suggestions.push({\n          id: 'moderate-congestion',\n          title: '优化信号配时',\n          content: '交通流量较大，建议调整信号灯配时方案，延长主要方向绿灯时间。',\n          priority: 'medium',\n          tagType: 'warning',\n          icon: Setting,\n          iconClass: 'warning-icon',\n          action: '调整配时',\n          actionType: 'warning'\n        });\n      }\n\n      // 基于流量平衡的建议\n      if (balance < 60) {\n        suggestions.push({\n          id: 'flow-imbalance',\n          title: '流量均衡优化',\n          content: '各方向流量不均衡，建议动态调整信号配时，优化交通流分配。',\n          priority: 'medium',\n          tagType: 'warning',\n          icon: TrendCharts,\n          iconClass: 'warning-icon',\n          action: '均衡流量',\n          actionType: 'primary'\n        });\n      }\n\n      // 基于总体流量的建议\n      if (totalVehicles > 15) {\n        suggestions.push({\n          id: 'high-volume',\n          title: '高峰期管理',\n          content: '当前处于交通高峰期，建议启动高峰期管理模式，加强现场监控。',\n          priority: 'medium',\n          tagType: 'info',\n          icon: Clock,\n          iconClass: 'info-icon',\n          action: '启动高峰模式',\n          actionType: 'primary'\n        });\n      }\n\n      // 如果没有问题，给出正面建议\n      if (suggestions.length === 0) {\n        suggestions.push({\n          id: 'normal-operation',\n          title: '运行状态良好',\n          content: '当前交通状况良好，建议保持现有信号配时方案，继续监控。',\n          priority: 'low',\n          tagType: 'success',\n          icon: SuccessFilled,\n          iconClass: 'success-icon',\n          action: '保持监控',\n          actionType: 'success'\n        });\n      }\n      return suggestions;\n    };\n\n    // 应用建议\n    const applySuggestion = suggestion => {\n      emit('apply-suggestion', suggestion);\n    };\n    return {\n      directions,\n      getCurrentVehicleCount,\n      getMovingAverage,\n      getDirectionProgress,\n      getCurrentFrame,\n      getTotalFrames,\n      getDirectionStatusType,\n      getDirectionStatusText,\n      getProgressStatus,\n      getDirectionCardClass,\n      getCongestionLevel,\n      getCongestionLevelClass,\n      getCongestionDescription,\n      getCongestionIndex,\n      getCongestionTrend,\n      getTrafficFlowBalance,\n      getBalanceStatus,\n      getBalanceColor,\n      getManagementSuggestions,\n      applySuggestion\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "ref", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Warning", "Setting", "InfoFilled", "SuccessFilled", "WarnTriangleFilled", "Clock", "name", "components", "props", "directionStats", "type", "Object", "required", "lastUpdateTime", "String", "default", "emits", "setup", "emit", "directions", "key", "getCurrentVehicleCount", "direction", "vehicleCount", "getMovingAverage", "movingAverage", "getDirectionProgress", "stats", "totalFrames", "Math", "round", "currentFrame", "getCurrentFrame", "getTotalFrames", "getDirectionStatusType", "status", "typeMap", "getDirectionStatusText", "textMap", "getProgressStatus", "progress", "undefined", "getDirectionCardClass", "getCongestionLevel", "totalVehicles", "reduce", "sum", "dir", "avgMoving", "getCongestionLevelClass", "level", "classMap", "getCongestionDescription", "descMap", "getCongestionIndex", "vehicleIndex", "min", "movingIndex", "getCongestionTrend", "index", "getTrafficFlowBalance", "counts", "map", "max", "balance", "getBalanceStatus", "getBalanceColor", "getManagementSuggestions", "suggestions", "congestionLevel", "push", "id", "title", "content", "priority", "tagType", "icon", "iconClass", "action", "actionType", "length", "applySuggestion", "suggestion"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\analysis\\IntelligentTrafficStatusPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"intelligent-traffic-status-panel\">\n    <!-- 面板标题 -->\n    <div class=\"panel-header\">\n      <h3 class=\"panel-title\">\n        <el-icon><TrendCharts /></el-icon>\n        智能交通状态面板\n      </h3>\n      <div class=\"update-info\">\n        <el-tag v-if=\"lastUpdateTime\" size=\"small\" type=\"info\">\n          {{ lastUpdateTime }}\n        </el-tag>\n      </div>\n    </div>\n\n    <!-- 实时车辆计数区域 -->\n    <div class=\"realtime-vehicle-section\">\n      <h4 class=\"section-title\">实时车辆检测</h4>\n      <div class=\"vehicle-grid\">\n        <div \n          v-for=\"direction in directions\" \n          :key=\"direction.key\"\n          class=\"vehicle-card\"\n          :class=\"getDirectionCardClass(direction.key)\"\n        >\n          <div class=\"direction-header\">\n            <span class=\"direction-name\">{{ direction.name }}</span>\n            <el-tag \n              :type=\"getDirectionStatusType(direction.key)\" \n              size=\"small\"\n            >\n              {{ getDirectionStatusText(direction.key) }}\n            </el-tag>\n          </div>\n          \n          <div class=\"vehicle-metrics\">\n            <!-- 当前帧车辆数 -->\n            <div class=\"metric-item primary\">\n              <div class=\"metric-value\">{{ getCurrentVehicleCount(direction.key) }}</div>\n              <div class=\"metric-label\">当前帧车辆</div>\n            </div>\n            \n            <!-- 移动平均 -->\n            <div class=\"metric-item secondary\">\n              <div class=\"metric-value\">{{ getMovingAverage(direction.key) }}</div>\n              <div class=\"metric-label\">移动平均</div>\n            </div>\n            \n            <!-- 进度显示 -->\n            <div class=\"metric-item progress\">\n              <el-progress \n                :percentage=\"getDirectionProgress(direction.key)\"\n                :stroke-width=\"6\"\n                :show-text=\"false\"\n                :status=\"getProgressStatus(direction.key)\"\n              />\n              <div class=\"metric-label\">\n                {{ getDirectionProgress(direction.key) }}% \n                ({{ getCurrentFrame(direction.key) }}/{{ getTotalFrames(direction.key) }})\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 拥堵等级分析 -->\n    <div class=\"congestion-analysis-section\">\n      <h4 class=\"section-title\">拥堵等级分析</h4>\n      <div class=\"congestion-grid\">\n        <div class=\"congestion-overview\">\n          <div class=\"congestion-level\" :class=\"getCongestionLevelClass()\">\n            <div class=\"level-indicator\">\n              <el-icon><Warning /></el-icon>\n              {{ getCongestionLevel() }}\n            </div>\n            <div class=\"level-description\">{{ getCongestionDescription() }}</div>\n          </div>\n          \n          <div class=\"congestion-metrics\">\n            <div class=\"metric\">\n              <span class=\"metric-label\">拥堵指数</span>\n              <span class=\"metric-value\">{{ getCongestionIndex() }}</span>\n            </div>\n            <div class=\"metric\">\n              <span class=\"metric-label\">变化趋势</span>\n              <span class=\"metric-value trend\">{{ getCongestionTrend() }}</span>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"flow-balance\">\n          <div class=\"balance-indicator\">\n            <div class=\"balance-title\">交通流平衡度</div>\n            <div class=\"balance-value\">{{ getTrafficFlowBalance() }}%</div>\n            <div class=\"balance-status\">{{ getBalanceStatus() }}</div>\n          </div>\n          \n          <el-progress \n            type=\"circle\" \n            :percentage=\"getTrafficFlowBalance()\"\n            :width=\"80\"\n            :stroke-width=\"8\"\n            :color=\"getBalanceColor()\"\n          />\n        </div>\n      </div>\n    </div>\n\n    <!-- 交通管理建议 -->\n    <div class=\"management-suggestions-section\">\n      <h4 class=\"section-title\">智能管理建议</h4>\n      <div class=\"suggestions-container\">\n        <div \n          v-for=\"suggestion in getManagementSuggestions()\" \n          :key=\"suggestion.id\"\n          class=\"suggestion-card\"\n          :class=\"suggestion.priority\"\n        >\n          <div class=\"suggestion-header\">\n            <el-icon :class=\"suggestion.iconClass\">\n              <component :is=\"suggestion.icon\" />\n            </el-icon>\n            <span class=\"suggestion-title\">{{ suggestion.title }}</span>\n            <el-tag :type=\"suggestion.tagType\" size=\"small\">\n              {{ suggestion.priority }}\n            </el-tag>\n          </div>\n          <div class=\"suggestion-content\">{{ suggestion.content }}</div>\n          <div class=\"suggestion-action\" v-if=\"suggestion.action\">\n            <el-button \n              :type=\"suggestion.actionType\" \n              size=\"small\"\n              @click=\"applySuggestion(suggestion)\"\n            >\n              {{ suggestion.action }}\n            </el-button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed, ref } from 'vue'\nimport { \n  TrendCharts, Warning, Setting, InfoFilled, \n  SuccessFilled, WarnTriangleFilled, Clock \n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'IntelligentTrafficStatusPanel',\n  components: {\n    TrendCharts, Warning, Setting, InfoFilled,\n    SuccessFilled, WarnTriangleFilled, Clock\n  },\n  props: {\n    directionStats: {\n      type: Object,\n      required: true\n    },\n    lastUpdateTime: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['apply-suggestion'],\n  setup(props, { emit }) {\n    const directions = [\n      { key: 'north', name: '北向' },\n      { key: 'east', name: '东向' },\n      { key: 'south', name: '南向' },\n      { key: 'west', name: '西向' }\n    ]\n\n    // 获取当前帧车辆数\n    const getCurrentVehicleCount = (direction) => {\n      return props.directionStats[direction]?.vehicleCount || 0\n    }\n\n    // 获取移动平均\n    const getMovingAverage = (direction) => {\n      return props.directionStats[direction]?.movingAverage || 0\n    }\n\n    // 获取方向进度\n    const getDirectionProgress = (direction) => {\n      const stats = props.directionStats[direction]\n      if (!stats || !stats.totalFrames) return 0\n      return Math.round((stats.currentFrame || 0) / stats.totalFrames * 100)\n    }\n\n    // 获取当前帧数\n    const getCurrentFrame = (direction) => {\n      return props.directionStats[direction]?.currentFrame || 0\n    }\n\n    // 获取总帧数\n    const getTotalFrames = (direction) => {\n      return props.directionStats[direction]?.totalFrames || 0\n    }\n\n    // 获取方向状态类型\n    const getDirectionStatusType = (direction) => {\n      const status = props.directionStats[direction]?.status || 'waiting'\n      const typeMap = {\n        'waiting': 'info',\n        'processing': 'warning', \n        'completed': 'success',\n        'error': 'danger'\n      }\n      return typeMap[status] || 'info'\n    }\n\n    // 获取方向状态文本\n    const getDirectionStatusText = (direction) => {\n      const status = props.directionStats[direction]?.status || 'waiting'\n      const textMap = {\n        'waiting': '等待中',\n        'processing': '检测中',\n        'completed': '已完成', \n        'error': '检测失败'\n      }\n      return textMap[status] || '未知'\n    }\n\n    // 获取进度状态\n    const getProgressStatus = (direction) => {\n      const progress = getDirectionProgress(direction)\n      if (progress === 100) return 'success'\n      if (progress > 0) return undefined\n      return 'exception'\n    }\n\n    // 获取方向卡片样式类\n    const getDirectionCardClass = (direction) => {\n      const status = props.directionStats[direction]?.status || 'waiting'\n      return `status-${status}`\n    }\n\n    // 拥堵等级计算\n    const getCongestionLevel = () => {\n      const totalVehicles = directions.reduce((sum, dir) =>\n        sum + getCurrentVehicleCount(dir.key), 0)\n      const avgMoving = directions.reduce((sum, dir) =>\n        sum + getMovingAverage(dir.key), 0) / 4\n\n      if (avgMoving >= 8 || totalVehicles >= 20) return '严重拥堵'\n      if (avgMoving >= 5 || totalVehicles >= 12) return '中度拥堵'\n      if (avgMoving >= 3 || totalVehicles >= 6) return '轻度拥堵'\n      return '畅通'\n    }\n\n    const getCongestionLevelClass = () => {\n      const level = getCongestionLevel()\n      const classMap = {\n        '严重拥堵': 'severe',\n        '中度拥堵': 'moderate',\n        '轻度拥堵': 'light',\n        '畅通': 'smooth'\n      }\n      return classMap[level] || 'smooth'\n    }\n\n    const getCongestionDescription = () => {\n      const level = getCongestionLevel()\n      const descMap = {\n        '严重拥堵': '交通严重拥堵，建议立即采取疏导措施',\n        '中度拥堵': '交通较为拥堵，需要关注流量变化',\n        '轻度拥堵': '交通略有拥堵，保持正常监控',\n        '畅通': '交通状况良好，运行顺畅'\n      }\n      return descMap[level] || '状态未知'\n    }\n\n    const getCongestionIndex = () => {\n      const totalVehicles = directions.reduce((sum, dir) =>\n        sum + getCurrentVehicleCount(dir.key), 0)\n      const avgMoving = directions.reduce((sum, dir) =>\n        sum + getMovingAverage(dir.key), 0) / 4\n\n      // 综合计算拥堵指数 (0-100)\n      const vehicleIndex = Math.min(totalVehicles * 2.5, 50)\n      const movingIndex = Math.min(avgMoving * 6, 50)\n      return Math.round(vehicleIndex + movingIndex)\n    }\n\n    const getCongestionTrend = () => {\n      const index = getCongestionIndex()\n      if (index >= 80) return '↗️ 恶化'\n      if (index >= 60) return '→ 稳定'\n      if (index >= 40) return '↘️ 改善'\n      return '✅ 良好'\n    }\n\n    // 交通流平衡度计算\n    const getTrafficFlowBalance = () => {\n      const counts = directions.map(dir => getMovingAverage(dir.key))\n      const max = Math.max(...counts)\n      const min = Math.min(...counts)\n\n      if (max === 0) return 100\n      const balance = ((max - min) / max) * 100\n      return Math.round(100 - balance)\n    }\n\n    const getBalanceStatus = () => {\n      const balance = getTrafficFlowBalance()\n      if (balance >= 80) return '非常均衡'\n      if (balance >= 60) return '较为均衡'\n      if (balance >= 40) return '不够均衡'\n      return '严重不均衡'\n    }\n\n    const getBalanceColor = () => {\n      const balance = getTrafficFlowBalance()\n      if (balance >= 80) return '#10b981'\n      if (balance >= 60) return '#f59e0b'\n      if (balance >= 40) return '#f97316'\n      return '#ef4444'\n    }\n\n    // 管理建议生成\n    const getManagementSuggestions = () => {\n      const suggestions = []\n      const congestionLevel = getCongestionLevel()\n      const balance = getTrafficFlowBalance()\n      const totalVehicles = directions.reduce((sum, dir) =>\n        sum + getCurrentVehicleCount(dir.key), 0)\n\n      // 基于拥堵等级的建议\n      if (congestionLevel === '严重拥堵') {\n        suggestions.push({\n          id: 'severe-congestion',\n          title: '紧急疏导措施',\n          content: '检测到严重拥堵，建议立即启动应急预案，增加信号灯绿灯时长，派遣交警现场疏导。',\n          priority: 'high',\n          tagType: 'danger',\n          icon: WarnTriangleFilled,\n          iconClass: 'danger-icon',\n          action: '启动应急预案',\n          actionType: 'danger'\n        })\n      } else if (congestionLevel === '中度拥堵') {\n        suggestions.push({\n          id: 'moderate-congestion',\n          title: '优化信号配时',\n          content: '交通流量较大，建议调整信号灯配时方案，延长主要方向绿灯时间。',\n          priority: 'medium',\n          tagType: 'warning',\n          icon: Setting,\n          iconClass: 'warning-icon',\n          action: '调整配时',\n          actionType: 'warning'\n        })\n      }\n\n      // 基于流量平衡的建议\n      if (balance < 60) {\n        suggestions.push({\n          id: 'flow-imbalance',\n          title: '流量均衡优化',\n          content: '各方向流量不均衡，建议动态调整信号配时，优化交通流分配。',\n          priority: 'medium',\n          tagType: 'warning',\n          icon: TrendCharts,\n          iconClass: 'warning-icon',\n          action: '均衡流量',\n          actionType: 'primary'\n        })\n      }\n\n      // 基于总体流量的建议\n      if (totalVehicles > 15) {\n        suggestions.push({\n          id: 'high-volume',\n          title: '高峰期管理',\n          content: '当前处于交通高峰期，建议启动高峰期管理模式，加强现场监控。',\n          priority: 'medium',\n          tagType: 'info',\n          icon: Clock,\n          iconClass: 'info-icon',\n          action: '启动高峰模式',\n          actionType: 'primary'\n        })\n      }\n\n      // 如果没有问题，给出正面建议\n      if (suggestions.length === 0) {\n        suggestions.push({\n          id: 'normal-operation',\n          title: '运行状态良好',\n          content: '当前交通状况良好，建议保持现有信号配时方案，继续监控。',\n          priority: 'low',\n          tagType: 'success',\n          icon: SuccessFilled,\n          iconClass: 'success-icon',\n          action: '保持监控',\n          actionType: 'success'\n        })\n      }\n\n      return suggestions\n    }\n\n    // 应用建议\n    const applySuggestion = (suggestion) => {\n      emit('apply-suggestion', suggestion)\n    }\n\n    return {\n      directions,\n      getCurrentVehicleCount,\n      getMovingAverage,\n      getDirectionProgress,\n      getCurrentFrame,\n      getTotalFrames,\n      getDirectionStatusType,\n      getDirectionStatusText,\n      getProgressStatus,\n      getDirectionCardClass,\n      getCongestionLevel,\n      getCongestionLevelClass,\n      getCongestionDescription,\n      getCongestionIndex,\n      getCongestionTrend,\n      getTrafficFlowBalance,\n      getBalanceStatus,\n      getBalanceColor,\n      getManagementSuggestions,\n      applySuggestion\n    }\n  }\n}\n</script>\n\n<style scoped>\n.intelligent-traffic-status-panel {\n  background: #ffffff;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 2px solid #f0f2f5;\n}\n\n.panel-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.section-title {\n  margin: 0 0 16px 0;\n  font-size: 16px;\n  font-weight: 500;\n  color: #374151;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.realtime-vehicle-section {\n  margin-bottom: 32px;\n}\n\n.vehicle-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 16px;\n}\n\n@media (max-width: 768px) {\n  .vehicle-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .congestion-grid {\n    grid-template-columns: 1fr !important;\n  }\n}\n\n.vehicle-card {\n  background: #f9fafb;\n  border-radius: 8px;\n  padding: 16px;\n  border: 2px solid #e5e7eb;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  transform: translateY(0);\n}\n\n.vehicle-card:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);\n  transform: translateY(-2px);\n}\n\n.vehicle-card.status-processing {\n  border-color: #f59e0b;\n  background: #fffbeb;\n}\n\n.vehicle-card.status-completed {\n  border-color: #10b981;\n  background: #ecfdf5;\n}\n\n.vehicle-card.status-error {\n  border-color: #ef4444;\n  background: #fef2f2;\n}\n\n.direction-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.direction-name {\n  font-weight: 500;\n  color: #374151;\n}\n\n.vehicle-metrics {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.metric-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.metric-item.primary .metric-value {\n  font-size: 24px;\n  font-weight: 700;\n  color: #1f2937;\n}\n\n.metric-item.secondary .metric-value {\n  font-size: 16px;\n  font-weight: 500;\n  color: #6b7280;\n}\n\n.metric-label {\n  font-size: 12px;\n  color: #9ca3af;\n}\n\n.metric-item.progress {\n  flex-direction: column;\n  align-items: stretch;\n  gap: 4px;\n}\n\n.congestion-analysis-section {\n  margin-bottom: 32px;\n}\n\n.congestion-grid {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 24px;\n}\n\n.congestion-overview {\n  background: #f9fafb;\n  border-radius: 8px;\n  padding: 20px;\n}\n\n.congestion-level {\n  text-align: center;\n  margin-bottom: 16px;\n}\n\n.congestion-level.severe .level-indicator {\n  color: #ef4444;\n}\n\n.congestion-level.moderate .level-indicator {\n  color: #f59e0b;\n}\n\n.congestion-level.light .level-indicator {\n  color: #f97316;\n}\n\n.congestion-level.smooth .level-indicator {\n  color: #10b981;\n}\n\n.level-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  font-size: 20px;\n  font-weight: 600;\n  margin-bottom: 8px;\n}\n\n.level-description {\n  font-size: 14px;\n  color: #6b7280;\n}\n\n.congestion-metrics {\n  display: flex;\n  justify-content: space-around;\n}\n\n.metric {\n  text-align: center;\n}\n\n.metric .metric-label {\n  display: block;\n  font-size: 12px;\n  color: #9ca3af;\n  margin-bottom: 4px;\n}\n\n.metric .metric-value {\n  font-size: 16px;\n  font-weight: 500;\n  color: #374151;\n}\n\n.flow-balance {\n  background: #f9fafb;\n  border-radius: 8px;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16px;\n}\n\n.balance-indicator {\n  text-align: center;\n}\n\n.balance-title {\n  font-size: 14px;\n  color: #6b7280;\n  margin-bottom: 8px;\n}\n\n.balance-value {\n  font-size: 24px;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 4px;\n}\n\n.balance-status {\n  font-size: 12px;\n  color: #9ca3af;\n}\n\n.management-suggestions-section {\n  margin-bottom: 16px;\n}\n\n.suggestions-container {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.suggestion-card {\n  background: #f9fafb;\n  border-radius: 8px;\n  padding: 16px;\n  border-left: 4px solid #e5e7eb;\n}\n\n.suggestion-card.high {\n  border-left-color: #ef4444;\n  background: #fef2f2;\n}\n\n.suggestion-card.medium {\n  border-left-color: #f59e0b;\n  background: #fffbeb;\n}\n\n.suggestion-card.low {\n  border-left-color: #10b981;\n  background: #ecfdf5;\n}\n\n.suggestion-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n}\n\n.suggestion-title {\n  flex: 1;\n  font-weight: 500;\n  color: #374151;\n}\n\n.suggestion-content {\n  font-size: 14px;\n  color: #6b7280;\n  margin-bottom: 12px;\n  line-height: 1.5;\n}\n\n.suggestion-action {\n  text-align: right;\n}\n</style>\n"], "mappings": ";AAiJA,SAASA,QAAQ,EAAEC,GAAE,QAAS,KAAI;AAClC,SACEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EACzCC,aAAa,EAAEC,kBAAkB,EAAEC,KAAI,QAClC,yBAAwB;AAE/B,eAAe;EACbC,IAAI,EAAE,+BAA+B;EACrCC,UAAU,EAAE;IACVR,WAAW;IAAEC,OAAO;IAAEC,OAAO;IAAEC,UAAU;IACzCC,aAAa;IAAEC,kBAAkB;IAAEC;EACrC,CAAC;EACDG,KAAK,EAAE;IACLC,cAAc,EAAE;MACdC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,cAAc,EAAE;MACdH,IAAI,EAAEI,MAAM;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,kBAAkB,CAAC;EAC3BC,KAAKA,CAACT,KAAK,EAAE;IAAEU;EAAK,CAAC,EAAE;IACrB,MAAMC,UAAS,GAAI,CACjB;MAAEC,GAAG,EAAE,OAAO;MAAEd,IAAI,EAAE;IAAK,CAAC,EAC5B;MAAEc,GAAG,EAAE,MAAM;MAAEd,IAAI,EAAE;IAAK,CAAC,EAC3B;MAAEc,GAAG,EAAE,OAAO;MAAEd,IAAI,EAAE;IAAK,CAAC,EAC5B;MAAEc,GAAG,EAAE,MAAM;MAAEd,IAAI,EAAE;IAAK,EAC5B;;IAEA;IACA,MAAMe,sBAAqB,GAAKC,SAAS,IAAK;MAC5C,OAAOd,KAAK,CAACC,cAAc,CAACa,SAAS,CAAC,EAAEC,YAAW,IAAK;IAC1D;;IAEA;IACA,MAAMC,gBAAe,GAAKF,SAAS,IAAK;MACtC,OAAOd,KAAK,CAACC,cAAc,CAACa,SAAS,CAAC,EAAEG,aAAY,IAAK;IAC3D;;IAEA;IACA,MAAMC,oBAAmB,GAAKJ,SAAS,IAAK;MAC1C,MAAMK,KAAI,GAAInB,KAAK,CAACC,cAAc,CAACa,SAAS;MAC5C,IAAI,CAACK,KAAI,IAAK,CAACA,KAAK,CAACC,WAAW,EAAE,OAAO;MACzC,OAAOC,IAAI,CAACC,KAAK,CAAC,CAACH,KAAK,CAACI,YAAW,IAAK,CAAC,IAAIJ,KAAK,CAACC,WAAU,GAAI,GAAG;IACvE;;IAEA;IACA,MAAMI,eAAc,GAAKV,SAAS,IAAK;MACrC,OAAOd,KAAK,CAACC,cAAc,CAACa,SAAS,CAAC,EAAES,YAAW,IAAK;IAC1D;;IAEA;IACA,MAAME,cAAa,GAAKX,SAAS,IAAK;MACpC,OAAOd,KAAK,CAACC,cAAc,CAACa,SAAS,CAAC,EAAEM,WAAU,IAAK;IACzD;;IAEA;IACA,MAAMM,sBAAqB,GAAKZ,SAAS,IAAK;MAC5C,MAAMa,MAAK,GAAI3B,KAAK,CAACC,cAAc,CAACa,SAAS,CAAC,EAAEa,MAAK,IAAK,SAAQ;MAClE,MAAMC,OAAM,GAAI;QACd,SAAS,EAAE,MAAM;QACjB,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,SAAS;QACtB,OAAO,EAAE;MACX;MACA,OAAOA,OAAO,CAACD,MAAM,KAAK,MAAK;IACjC;;IAEA;IACA,MAAME,sBAAqB,GAAKf,SAAS,IAAK;MAC5C,MAAMa,MAAK,GAAI3B,KAAK,CAACC,cAAc,CAACa,SAAS,CAAC,EAAEa,MAAK,IAAK,SAAQ;MAClE,MAAMG,OAAM,GAAI;QACd,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,KAAK;QAClB,OAAO,EAAE;MACX;MACA,OAAOA,OAAO,CAACH,MAAM,KAAK,IAAG;IAC/B;;IAEA;IACA,MAAMI,iBAAgB,GAAKjB,SAAS,IAAK;MACvC,MAAMkB,QAAO,GAAId,oBAAoB,CAACJ,SAAS;MAC/C,IAAIkB,QAAO,KAAM,GAAG,EAAE,OAAO,SAAQ;MACrC,IAAIA,QAAO,GAAI,CAAC,EAAE,OAAOC,SAAQ;MACjC,OAAO,WAAU;IACnB;;IAEA;IACA,MAAMC,qBAAoB,GAAKpB,SAAS,IAAK;MAC3C,MAAMa,MAAK,GAAI3B,KAAK,CAACC,cAAc,CAACa,SAAS,CAAC,EAAEa,MAAK,IAAK,SAAQ;MAClE,OAAO,UAAUA,MAAM,EAAC;IAC1B;;IAEA;IACA,MAAMQ,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,aAAY,GAAIzB,UAAU,CAAC0B,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAC/CD,GAAE,GAAIzB,sBAAsB,CAAC0B,GAAG,CAAC3B,GAAG,CAAC,EAAE,CAAC;MAC1C,MAAM4B,SAAQ,GAAI7B,UAAU,CAAC0B,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAC3CD,GAAE,GAAItB,gBAAgB,CAACuB,GAAG,CAAC3B,GAAG,CAAC,EAAE,CAAC,IAAI;MAExC,IAAI4B,SAAQ,IAAK,KAAKJ,aAAY,IAAK,EAAE,EAAE,OAAO,MAAK;MACvD,IAAII,SAAQ,IAAK,KAAKJ,aAAY,IAAK,EAAE,EAAE,OAAO,MAAK;MACvD,IAAII,SAAQ,IAAK,KAAKJ,aAAY,IAAK,CAAC,EAAE,OAAO,MAAK;MACtD,OAAO,IAAG;IACZ;IAEA,MAAMK,uBAAsB,GAAIA,CAAA,KAAM;MACpC,MAAMC,KAAI,GAAIP,kBAAkB,CAAC;MACjC,MAAMQ,QAAO,GAAI;QACf,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,UAAU;QAClB,MAAM,EAAE,OAAO;QACf,IAAI,EAAE;MACR;MACA,OAAOA,QAAQ,CAACD,KAAK,KAAK,QAAO;IACnC;IAEA,MAAME,wBAAuB,GAAIA,CAAA,KAAM;MACrC,MAAMF,KAAI,GAAIP,kBAAkB,CAAC;MACjC,MAAMU,OAAM,GAAI;QACd,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,eAAe;QACvB,IAAI,EAAE;MACR;MACA,OAAOA,OAAO,CAACH,KAAK,KAAK,MAAK;IAChC;IAEA,MAAMI,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMV,aAAY,GAAIzB,UAAU,CAAC0B,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAC/CD,GAAE,GAAIzB,sBAAsB,CAAC0B,GAAG,CAAC3B,GAAG,CAAC,EAAE,CAAC;MAC1C,MAAM4B,SAAQ,GAAI7B,UAAU,CAAC0B,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAC3CD,GAAE,GAAItB,gBAAgB,CAACuB,GAAG,CAAC3B,GAAG,CAAC,EAAE,CAAC,IAAI;;MAExC;MACA,MAAMmC,YAAW,GAAI1B,IAAI,CAAC2B,GAAG,CAACZ,aAAY,GAAI,GAAG,EAAE,EAAE;MACrD,MAAMa,WAAU,GAAI5B,IAAI,CAAC2B,GAAG,CAACR,SAAQ,GAAI,CAAC,EAAE,EAAE;MAC9C,OAAOnB,IAAI,CAACC,KAAK,CAACyB,YAAW,GAAIE,WAAW;IAC9C;IAEA,MAAMC,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,KAAI,GAAIL,kBAAkB,CAAC;MACjC,IAAIK,KAAI,IAAK,EAAE,EAAE,OAAO,OAAM;MAC9B,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,MAAK;MAC7B,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,OAAM;MAC9B,OAAO,MAAK;IACd;;IAEA;IACA,MAAMC,qBAAoB,GAAIA,CAAA,KAAM;MAClC,MAAMC,MAAK,GAAI1C,UAAU,CAAC2C,GAAG,CAACf,GAAE,IAAKvB,gBAAgB,CAACuB,GAAG,CAAC3B,GAAG,CAAC;MAC9D,MAAM2C,GAAE,GAAIlC,IAAI,CAACkC,GAAG,CAAC,GAAGF,MAAM;MAC9B,MAAML,GAAE,GAAI3B,IAAI,CAAC2B,GAAG,CAAC,GAAGK,MAAM;MAE9B,IAAIE,GAAE,KAAM,CAAC,EAAE,OAAO,GAAE;MACxB,MAAMC,OAAM,GAAK,CAACD,GAAE,GAAIP,GAAG,IAAIO,GAAG,GAAI,GAAE;MACxC,OAAOlC,IAAI,CAACC,KAAK,CAAC,GAAE,GAAIkC,OAAO;IACjC;IAEA,MAAMC,gBAAe,GAAIA,CAAA,KAAM;MAC7B,MAAMD,OAAM,GAAIJ,qBAAqB,CAAC;MACtC,IAAII,OAAM,IAAK,EAAE,EAAE,OAAO,MAAK;MAC/B,IAAIA,OAAM,IAAK,EAAE,EAAE,OAAO,MAAK;MAC/B,IAAIA,OAAM,IAAK,EAAE,EAAE,OAAO,MAAK;MAC/B,OAAO,OAAM;IACf;IAEA,MAAME,eAAc,GAAIA,CAAA,KAAM;MAC5B,MAAMF,OAAM,GAAIJ,qBAAqB,CAAC;MACtC,IAAII,OAAM,IAAK,EAAE,EAAE,OAAO,SAAQ;MAClC,IAAIA,OAAM,IAAK,EAAE,EAAE,OAAO,SAAQ;MAClC,IAAIA,OAAM,IAAK,EAAE,EAAE,OAAO,SAAQ;MAClC,OAAO,SAAQ;IACjB;;IAEA;IACA,MAAMG,wBAAuB,GAAIA,CAAA,KAAM;MACrC,MAAMC,WAAU,GAAI,EAAC;MACrB,MAAMC,eAAc,GAAI1B,kBAAkB,CAAC;MAC3C,MAAMqB,OAAM,GAAIJ,qBAAqB,CAAC;MACtC,MAAMhB,aAAY,GAAIzB,UAAU,CAAC0B,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAC/CD,GAAE,GAAIzB,sBAAsB,CAAC0B,GAAG,CAAC3B,GAAG,CAAC,EAAE,CAAC;;MAE1C;MACA,IAAIiD,eAAc,KAAM,MAAM,EAAE;QAC9BD,WAAW,CAACE,IAAI,CAAC;UACfC,EAAE,EAAE,mBAAmB;UACvBC,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE,wCAAwC;UACjDC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,QAAQ;UACjBC,IAAI,EAAExE,kBAAkB;UACxByE,SAAS,EAAE,aAAa;UACxBC,MAAM,EAAE,QAAQ;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,OAAO,IAAIV,eAAc,KAAM,MAAM,EAAE;QACrCD,WAAW,CAACE,IAAI,CAAC;UACfC,EAAE,EAAE,qBAAqB;UACzBC,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE,gCAAgC;UACzCC,QAAQ,EAAE,QAAQ;UAClBC,OAAO,EAAE,SAAS;UAClBC,IAAI,EAAE3E,OAAO;UACb4E,SAAS,EAAE,cAAc;UACzBC,MAAM,EAAE,MAAM;UACdC,UAAU,EAAE;QACd,CAAC;MACH;;MAEA;MACA,IAAIf,OAAM,GAAI,EAAE,EAAE;QAChBI,WAAW,CAACE,IAAI,CAAC;UACfC,EAAE,EAAE,gBAAgB;UACpBC,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE,8BAA8B;UACvCC,QAAQ,EAAE,QAAQ;UAClBC,OAAO,EAAE,SAAS;UAClBC,IAAI,EAAE7E,WAAW;UACjB8E,SAAS,EAAE,cAAc;UACzBC,MAAM,EAAE,MAAM;UACdC,UAAU,EAAE;QACd,CAAC;MACH;;MAEA;MACA,IAAInC,aAAY,GAAI,EAAE,EAAE;QACtBwB,WAAW,CAACE,IAAI,CAAC;UACfC,EAAE,EAAE,aAAa;UACjBC,KAAK,EAAE,OAAO;UACdC,OAAO,EAAE,+BAA+B;UACxCC,QAAQ,EAAE,QAAQ;UAClBC,OAAO,EAAE,MAAM;UACfC,IAAI,EAAEvE,KAAK;UACXwE,SAAS,EAAE,WAAW;UACtBC,MAAM,EAAE,QAAQ;UAChBC,UAAU,EAAE;QACd,CAAC;MACH;;MAEA;MACA,IAAIX,WAAW,CAACY,MAAK,KAAM,CAAC,EAAE;QAC5BZ,WAAW,CAACE,IAAI,CAAC;UACfC,EAAE,EAAE,kBAAkB;UACtBC,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE,6BAA6B;UACtCC,QAAQ,EAAE,KAAK;UACfC,OAAO,EAAE,SAAS;UAClBC,IAAI,EAAEzE,aAAa;UACnB0E,SAAS,EAAE,cAAc;UACzBC,MAAM,EAAE,MAAM;UACdC,UAAU,EAAE;QACd,CAAC;MACH;MAEA,OAAOX,WAAU;IACnB;;IAEA;IACA,MAAMa,eAAc,GAAKC,UAAU,IAAK;MACtChE,IAAI,CAAC,kBAAkB,EAAEgE,UAAU;IACrC;IAEA,OAAO;MACL/D,UAAU;MACVE,sBAAsB;MACtBG,gBAAgB;MAChBE,oBAAoB;MACpBM,eAAe;MACfC,cAAc;MACdC,sBAAsB;MACtBG,sBAAsB;MACtBE,iBAAiB;MACjBG,qBAAqB;MACrBC,kBAAkB;MAClBM,uBAAuB;MACvBG,wBAAwB;MACxBE,kBAAkB;MAClBI,kBAAkB;MAClBE,qBAAqB;MACrBK,gBAAgB;MAChBC,eAAe;MACfC,wBAAwB;MACxBc;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}