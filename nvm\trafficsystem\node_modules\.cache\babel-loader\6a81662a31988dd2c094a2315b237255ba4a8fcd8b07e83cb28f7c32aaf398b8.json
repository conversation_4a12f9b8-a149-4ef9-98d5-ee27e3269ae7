{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { VideoPlay, VideoPause, Delete, Loading, Connection } from '@element-plus/icons-vue';\nimport IntelligentTrafficPanel from '@/components/traffic/IntelligentTrafficPanel.vue';\nexport default {\n  name: 'RealTimeFrameViewer',\n  components: {\n    VideoPlay,\n    VideoPause,\n    Delete,\n    Loading,\n    Connection,\n    IntelligentTrafficPanel\n  },\n  props: {\n    // 任务ID，用于接收对应的帧数据\n    taskId: {\n      type: String,\n      required: true\n    },\n    // 是否自动开始播放\n    autoStart: {\n      type: Boolean,\n      default: true\n    },\n    // 最大缓冲帧数\n    maxBufferFrames: {\n      type: Number,\n      default: 30\n    }\n  },\n  emits: ['frame-received', 'playback-state-change'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const frames = ref([]);\n    const currentFrameIndex = ref(0);\n    const isPlaying = ref(false);\n    const autoPlay = ref(props.autoStart);\n    const playbackSpeed = ref(1);\n    const totalFrames = ref(0);\n\n    // 最新检测帧（定格显示）\n    const latestDetectionFrame = ref(null);\n\n    // 网络状况监控（优化帧率计算）\n    const networkStats = ref({\n      quality: 'good',\n      frameRate: 0,\n      averageSize: 0,\n      lastFrameTime: 0,\n      frameIntervals: [],\n      realTimeFrameRate: 0\n    });\n\n    // 播放控制\n    let playbackTimer = null;\n    let networkStatsTimer = null;\n\n    // 计算属性\n    const hasFrames = computed(() => frames.value.length > 0);\n    const currentFrame = computed(() => frames.value[currentFrameIndex.value] || null);\n\n    // 可见缩略图（最多显示10个）\n    const visibleThumbnails = computed(() => {\n      const maxThumbnails = 10;\n      if (frames.value.length <= maxThumbnails) {\n        return frames.value;\n      }\n      const step = Math.floor(frames.value.length / maxThumbnails);\n      return frames.value.filter((_, index) => index % step === 0).slice(0, maxThumbnails);\n    });\n\n    // 网络质量相关计算属性\n    const networkQualityClass = computed(() => {\n      return `network-${networkStats.value.quality}`;\n    });\n    const networkQualityText = computed(() => {\n      const quality = networkStats.value.quality;\n      const rate = networkStats.value.realTimeFrameRate > 0 ? networkStats.value.realTimeFrameRate.toFixed(1) : networkStats.value.frameRate.toFixed(1);\n      switch (quality) {\n        case 'good':\n          return `良好 ${rate}fps`;\n        case 'fair':\n          return `一般 ${rate}fps`;\n        case 'poor':\n          return `较差 ${rate}fps`;\n        default:\n          return `实时 ${rate}fps`;\n      }\n    });\n\n    // 方法\n    const getFrameImageUrl = frame => {\n      if (!frame || !frame.imageData) {\n        console.log('🖼️ getFrameImageUrl: 无效的帧数据', {\n          frame: !!frame,\n          hasImageData: frame ? !!frame.imageData : false\n        });\n        return '';\n      }\n      const imageUrl = `data:image/jpeg;base64,${frame.imageData}`;\n      console.log('🖼️ getFrameImageUrl: 生成图像URL', {\n        frameNumber: frame.frameNumber,\n        imageDataLength: frame.imageData.length,\n        urlLength: imageUrl.length,\n        urlPrefix: imageUrl.substring(0, 50) + '...'\n      });\n      return imageUrl;\n    };\n    const formatTimestamp = timestamp => {\n      if (!timestamp) return '';\n      try {\n        const date = new Date(timestamp);\n        return date.toLocaleTimeString();\n      } catch (e) {\n        return timestamp;\n      }\n    };\n\n    // 获取检测标签类型\n    const getDetectionTagType = detectionCount => {\n      if (detectionCount === 0) return 'info';\n      if (detectionCount <= 2) return 'success';\n      if (detectionCount <= 5) return 'warning';\n      return 'danger';\n    };\n    const addFrame = frameData => {\n      try {\n        // 计算实时帧率\n        const currentTime = Date.now();\n        if (networkStats.value.lastFrameTime > 0) {\n          const interval = currentTime - networkStats.value.lastFrameTime;\n          networkStats.value.frameIntervals.push(interval);\n\n          // 保持最近10个间隔用于计算平均帧率\n          if (networkStats.value.frameIntervals.length > 10) {\n            networkStats.value.frameIntervals.shift();\n          }\n\n          // 计算实时帧率\n          const avgInterval = networkStats.value.frameIntervals.reduce((a, b) => a + b, 0) / networkStats.value.frameIntervals.length;\n          networkStats.value.realTimeFrameRate = 1000 / avgInterval;\n        }\n        networkStats.value.lastFrameTime = currentTime;\n\n        // 调试日志：检查接收到的帧数据\n        console.log('🎬 RealTimeFrameViewer接收到帧数据:', {\n          frameNumber: frameData.frameNumber,\n          detectionCount: frameData.detectionCount,\n          hasImageData: !!frameData.imageData,\n          imageDataLength: frameData.imageData ? frameData.imageData.length : 0,\n          realTimeFrameRate: networkStats.value.realTimeFrameRate.toFixed(2) + ' fps'\n        });\n\n        // 添加新帧到缓冲区\n        frames.value.push(frameData);\n\n        // 更新总帧数\n        if (frameData.totalFrames) {\n          totalFrames.value = frameData.totalFrames;\n        }\n\n        // 检查是否有车辆检测，如果有则更新最新检测帧（定格显示）\n        if (frameData.detectionCount && frameData.detectionCount > 0) {\n          console.log(`🎯 更新最新检测帧: 帧${frameData.frameNumber}, 车辆${frameData.detectionCount}`);\n          latestDetectionFrame.value = {\n            ...frameData\n          };\n        }\n\n        // 限制缓冲区大小\n        while (frames.value.length > props.maxBufferFrames) {\n          frames.value.shift();\n          if (currentFrameIndex.value > 0) {\n            currentFrameIndex.value--;\n          }\n        }\n\n        // 如果启用自动播放且当前在最后一帧，自动跳到新帧\n        if (autoPlay.value && currentFrameIndex.value === frames.value.length - 2) {\n          currentFrameIndex.value = frames.value.length - 1;\n        }\n\n        // 发出帧接收事件\n        emit('frame-received', frameData);\n      } catch (error) {\n        console.error('添加帧数据失败:', error);\n        ElMessage.error('处理帧数据失败');\n      }\n    };\n    const togglePlayback = () => {\n      if (isPlaying.value) {\n        stopPlayback();\n      } else {\n        startPlayback();\n      }\n    };\n    const startPlayback = () => {\n      if (!hasFrames.value) return;\n      isPlaying.value = true;\n      emit('playback-state-change', {\n        playing: true,\n        speed: playbackSpeed.value\n      });\n      const interval = 1000 / playbackSpeed.value; // 基础间隔1秒，根据速度调整\n\n      playbackTimer = setInterval(() => {\n        if (currentFrameIndex.value < frames.value.length - 1) {\n          currentFrameIndex.value++;\n        } else {\n          // 播放完毕，停止播放\n          stopPlayback();\n        }\n      }, interval);\n    };\n    const stopPlayback = () => {\n      isPlaying.value = false;\n      emit('playback-state-change', {\n        playing: false,\n        speed: playbackSpeed.value\n      });\n      if (playbackTimer) {\n        clearInterval(playbackTimer);\n        playbackTimer = null;\n      }\n    };\n    const clearFrames = () => {\n      stopPlayback();\n      frames.value = [];\n      currentFrameIndex.value = 0;\n      totalFrames.value = 0;\n      latestDetectionFrame.value = null; // 清除定格显示的检测帧\n      ElMessage.success('已清空帧缓冲区');\n    };\n\n    // 清除最新检测帧\n    const clearLatestDetection = () => {\n      latestDetectionFrame.value = null;\n      console.log('🧹 已清除最新检测帧');\n      ElMessage.success('已清除最新检测结果');\n    };\n    const selectFrame = index => {\n      if (index >= 0 && index < frames.value.length) {\n        currentFrameIndex.value = index;\n      }\n    };\n    const onFrameIndexChange = value => {\n      currentFrameIndex.value = value;\n    };\n    const handleImageLoad = () => {\n      // 图像加载成功\n    };\n    const handleImageError = () => {\n      console.error('帧图像加载失败');\n    };\n\n    // 更新网络状况统计\n    const updateNetworkStats = () => {\n      try {\n        // 从STOMP服务获取网络统计\n        const stats = window.stompService?.getNetworkStats?.() || {};\n        networkStats.value = {\n          quality: stats.connectionQuality || 'good',\n          frameRate: stats.frameReceiveRate || 0,\n          averageSize: stats.averageFrameSize || 0\n        };\n      } catch (error) {\n        console.error('更新网络统计失败:', error);\n      }\n    };\n\n    // 启动网络状况监控\n    const startNetworkMonitoring = () => {\n      // 每2秒更新一次网络统计\n      networkStatsTimer = setInterval(updateNetworkStats, 2000);\n    };\n\n    // 停止网络状况监控\n    const stopNetworkMonitoring = () => {\n      if (networkStatsTimer) {\n        clearInterval(networkStatsTimer);\n        networkStatsTimer = null;\n      }\n    };\n\n    // 监听播放速度变化\n    watch(playbackSpeed, newSpeed => {\n      if (isPlaying.value) {\n        stopPlayback();\n        nextTick(() => {\n          startPlayback();\n        });\n      }\n    });\n\n    // 监听自动播放设置变化\n    watch(autoPlay, newValue => {\n      if (!newValue && isPlaying.value) {\n        stopPlayback();\n      }\n    });\n\n    // 组件挂载时启动网络监控\n    onMounted(() => {\n      startNetworkMonitoring();\n    });\n\n    // 组件卸载时清理\n    onUnmounted(() => {\n      stopPlayback();\n      stopNetworkMonitoring();\n    });\n\n    // 暴露方法给父组件\n    const addFrameData = addFrame;\n    const clearFrameData = clearFrames;\n    const getFrameCount = () => frames.value.length;\n    const getCurrentFrame = () => currentFrame.value;\n    return {\n      // 响应式数据\n      frames,\n      currentFrameIndex,\n      isPlaying,\n      autoPlay,\n      playbackSpeed,\n      totalFrames,\n      latestDetectionFrame,\n      // 计算属性\n      hasFrames,\n      currentFrame,\n      visibleThumbnails,\n      networkQualityClass,\n      networkQualityText,\n      // 方法\n      getFrameImageUrl,\n      formatTimestamp,\n      getDetectionTagType,\n      togglePlayback,\n      clearFrames,\n      clearLatestDetection,\n      selectFrame,\n      onFrameIndexChange,\n      handleImageLoad,\n      handleImageError,\n      // 暴露给父组件的方法\n      addFrameData,\n      clearFrameData,\n      getFrameCount,\n      getCurrentFrame\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "watch", "onMounted", "onUnmounted", "nextTick", "ElMessage", "VideoPlay", "VideoPause", "Delete", "Loading", "Connection", "IntelligentTrafficPanel", "name", "components", "props", "taskId", "type", "String", "required", "autoStart", "Boolean", "default", "max<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "Number", "emits", "setup", "emit", "frames", "currentFrameIndex", "isPlaying", "autoPlay", "playbackSpeed", "totalFrames", "latestDetectionFrame", "networkStats", "quality", "frameRate", "averageSize", "lastFrameTime", "frameIntervals", "realTimeFrameRate", "playbackTimer", "networkStatsTimer", "<PERSON><PERSON><PERSON><PERSON>", "value", "length", "currentFrame", "visibleThumbnails", "maxThumbnails", "step", "Math", "floor", "filter", "_", "index", "slice", "networkQualityClass", "networkQualityText", "rate", "toFixed", "getFrameImageUrl", "frame", "imageData", "console", "log", "hasImageData", "imageUrl", "frameNumber", "imageDataLength", "u<PERSON><PERSON><PERSON><PERSON>", "urlPrefix", "substring", "formatTimestamp", "timestamp", "date", "Date", "toLocaleTimeString", "e", "getDetectionTagType", "detectionCount", "addFrame", "frameData", "currentTime", "now", "interval", "push", "shift", "avgInterval", "reduce", "a", "b", "error", "togglePlayback", "stopPlayback", "startPlayback", "playing", "speed", "setInterval", "clearInterval", "clearFrames", "success", "clearLatestDetection", "selectFrame", "onFrameIndexChange", "handleImageLoad", "handleImageError", "updateNetworkStats", "stats", "window", "stompService", "getNetworkStats", "connectionQuality", "frameReceiveRate", "averageFrameSize", "startNetworkMonitoring", "stopNetworkMonitoring", "newSpeed", "newValue", "addFrameData", "clearFrameData", "getFrameCount", "getCurrentFrame"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\video\\RealTimeFrameViewer.vue"], "sourcesContent": ["<template>\n  <div class=\"realtime-frame-viewer\">\n    <div class=\"viewer-header\">\n      <h4>实时视频预览</h4>\n      <div class=\"viewer-controls\">\n        <el-button \n          size=\"small\" \n          :type=\"isPlaying ? 'danger' : 'primary'\"\n          @click=\"togglePlayback\"\n          :disabled=\"!hasFrames\"\n        >\n          <el-icon><video-play v-if=\"!isPlaying\" /><video-pause v-else /></el-icon>\n          {{ isPlaying ? '暂停' : '播放' }}\n        </el-button>\n        \n        <el-button\n          size=\"small\"\n          type=\"info\"\n          @click=\"clearFrames\"\n          :disabled=\"!hasFrames\"\n        >\n          <el-icon><delete /></el-icon>\n          清空\n        </el-button>\n\n        <el-button\n          size=\"small\"\n          type=\"warning\"\n          @click=\"clearLatestDetection\"\n          :disabled=\"!latestDetectionFrame\"\n          title=\"清除定格显示的检测结果\"\n        >\n          <el-icon><delete /></el-icon>\n          清除检测\n        </el-button>\n        \n        <el-switch\n          v-model=\"autoPlay\"\n          active-text=\"自动播放\"\n          inactive-text=\"手动控制\"\n          size=\"small\"\n        />\n      </div>\n    </div>\n\n    <div class=\"viewer-content\">\n      <!-- 主显示区域 -->\n      <div class=\"frame-display\" :class=\"{ 'no-frames': !hasFrames }\">\n        <div v-if=\"!hasFrames\" class=\"no-frames-message\">\n          <el-icon class=\"waiting-icon\"><loading /></el-icon>\n          <p>等待实时帧数据...</p>\n        </div>\n        \n        <!-- 最新检测帧显示（定格显示） -->\n        <div v-if=\"latestDetectionFrame\" class=\"detection-frame-container\">\n          <img\n            :src=\"getFrameImageUrl(latestDetectionFrame)\"\n            :alt=\"`检测帧 ${latestDetectionFrame.frameNumber}`\"\n            class=\"detection-frame-image\"\n            @load=\"handleImageLoad\"\n            @error=\"handleImageError\"\n          />\n\n          <!-- 检测结果覆盖层 -->\n          <div class=\"detection-overlay\">\n            <div class=\"detection-header\">\n              <el-tag :type=\"getDetectionTagType(latestDetectionFrame.detectionCount)\" size=\"small\">\n                🚗 当前帧: {{ latestDetectionFrame.detectionCount }} 辆车\n              </el-tag>\n              <span class=\"detection-time\">{{ formatTimestamp(latestDetectionFrame.timestamp) }}</span>\n            </div>\n\n            <div class=\"detection-info\">\n              <span class=\"frame-number\">帧: {{ latestDetectionFrame.frameNumber }}/{{ totalFrames || 0 }}</span>\n              <span class=\"detection-status\">实时检测结果</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 常规帧显示 -->\n        <div v-else class=\"frame-container\">\n          <img\n            v-if=\"currentFrame\"\n            :src=\"getFrameImageUrl(currentFrame)\"\n            :alt=\"`帧 ${currentFrame.frameNumber}`\"\n            class=\"frame-image\"\n            @load=\"handleImageLoad\"\n            @error=\"handleImageError\"\n          />\n\n          <!-- 帧信息覆盖层 -->\n          <div class=\"frame-overlay\">\n            <div class=\"frame-info\">\n              <span class=\"frame-number\">帧: {{ currentFrame?.frameNumber || 0 }}/{{ totalFrames || 0 }}</span>\n              <span class=\"detection-count\">当前: {{ currentFrame?.detectionCount || 0 }}辆</span>\n              <span class=\"timestamp\">{{ formatTimestamp(currentFrame?.timestamp) }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 进度条和控制 -->\n      <div class=\"playback-controls\" v-if=\"hasFrames\">\n        <el-slider\n          v-model=\"currentFrameIndex\"\n          :min=\"0\"\n          :max=\"frames.length - 1\"\n          :step=\"1\"\n          :show-tooltip=\"false\"\n          @change=\"onFrameIndexChange\"\n          class=\"frame-slider\"\n        />\n        \n        <div class=\"playback-info\">\n          <span>播放速度:</span>\n          <el-select v-model=\"playbackSpeed\" size=\"small\" style=\"width: 80px;\">\n            <el-option label=\"0.5x\" :value=\"0.5\" />\n            <el-option label=\"1x\" :value=\"1\" />\n            <el-option label=\"2x\" :value=\"2\" />\n            <el-option label=\"4x\" :value=\"4\" />\n          </el-select>\n          \n          <span class=\"buffer-info\">缓冲: {{ frames.length }} 帧</span>\n          <span class=\"network-info\" :class=\"networkQualityClass\">\n            <el-icon><connection /></el-icon>\n            {{ networkQualityText }}\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 缩略图条 -->\n    <div class=\"thumbnail-strip\" v-if=\"hasFrames && frames.length > 1\">\n      <div class=\"thumbnail-container\">\n        <div\n          v-for=\"(frame, index) in visibleThumbnails\"\n          :key=\"frame.frameNumber\"\n          class=\"thumbnail-item\"\n          :class=\"{ 'active': index === currentFrameIndex }\"\n          @click=\"selectFrame(index)\"\n        >\n          <img\n            :src=\"getFrameImageUrl(frame)\"\n            :alt=\"`缩略图 ${frame.frameNumber}`\"\n            class=\"thumbnail-image\"\n          />\n          <div class=\"thumbnail-label\">{{ frame.frameNumber }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 智能交通状态面板 -->\n    <div class=\"traffic-panel-container\" v-if=\"showTrafficPanel && latestDetectionFrame\">\n      <IntelligentTrafficPanel\n        :current-vehicle-count=\"latestDetectionFrame.detectionCount || 0\"\n        :auto-update=\"true\"\n        @strategy-applied=\"handleStrategyApplied\"\n        @data-updated=\"handleTrafficDataUpdated\"\n      />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport {\n  VideoPlay,\n  VideoPause,\n  Delete,\n  Loading,\n  Connection\n} from '@element-plus/icons-vue'\nimport IntelligentTrafficPanel from '@/components/traffic/IntelligentTrafficPanel.vue'\n\nexport default {\n  name: 'RealTimeFrameViewer',\n  components: {\n    VideoPlay,\n    VideoPause,\n    Delete,\n    Loading,\n    Connection,\n    IntelligentTrafficPanel\n  },\n  props: {\n    // 任务ID，用于接收对应的帧数据\n    taskId: {\n      type: String,\n      required: true\n    },\n    // 是否自动开始播放\n    autoStart: {\n      type: Boolean,\n      default: true\n    },\n    // 最大缓冲帧数\n    maxBufferFrames: {\n      type: Number,\n      default: 30\n    }\n  },\n  emits: ['frame-received', 'playback-state-change'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const frames = ref([])\n    const currentFrameIndex = ref(0)\n    const isPlaying = ref(false)\n    const autoPlay = ref(props.autoStart)\n    const playbackSpeed = ref(1)\n    const totalFrames = ref(0)\n\n    // 最新检测帧（定格显示）\n    const latestDetectionFrame = ref(null)\n\n    // 网络状况监控（优化帧率计算）\n    const networkStats = ref({\n      quality: 'good',\n      frameRate: 0,\n      averageSize: 0,\n      lastFrameTime: 0,\n      frameIntervals: [],\n      realTimeFrameRate: 0\n    })\n\n    // 播放控制\n    let playbackTimer = null\n    let networkStatsTimer = null\n    \n    // 计算属性\n    const hasFrames = computed(() => frames.value.length > 0)\n    const currentFrame = computed(() => frames.value[currentFrameIndex.value] || null)\n    \n    // 可见缩略图（最多显示10个）\n    const visibleThumbnails = computed(() => {\n      const maxThumbnails = 10\n      if (frames.value.length <= maxThumbnails) {\n        return frames.value\n      }\n\n      const step = Math.floor(frames.value.length / maxThumbnails)\n      return frames.value.filter((_, index) => index % step === 0).slice(0, maxThumbnails)\n    })\n\n    // 网络质量相关计算属性\n    const networkQualityClass = computed(() => {\n      return `network-${networkStats.value.quality}`\n    })\n\n    const networkQualityText = computed(() => {\n      const quality = networkStats.value.quality\n      const rate = networkStats.value.realTimeFrameRate > 0\n        ? networkStats.value.realTimeFrameRate.toFixed(1)\n        : networkStats.value.frameRate.toFixed(1)\n\n      switch (quality) {\n        case 'good':\n          return `良好 ${rate}fps`\n        case 'fair':\n          return `一般 ${rate}fps`\n        case 'poor':\n          return `较差 ${rate}fps`\n        default:\n          return `实时 ${rate}fps`\n      }\n    })\n    \n    // 方法\n    const getFrameImageUrl = (frame) => {\n      if (!frame || !frame.imageData) {\n        console.log('🖼️ getFrameImageUrl: 无效的帧数据', { frame: !!frame, hasImageData: frame ? !!frame.imageData : false })\n        return ''\n      }\n      const imageUrl = `data:image/jpeg;base64,${frame.imageData}`\n      console.log('🖼️ getFrameImageUrl: 生成图像URL', {\n        frameNumber: frame.frameNumber,\n        imageDataLength: frame.imageData.length,\n        urlLength: imageUrl.length,\n        urlPrefix: imageUrl.substring(0, 50) + '...'\n      })\n      return imageUrl\n    }\n    \n    const formatTimestamp = (timestamp) => {\n      if (!timestamp) return ''\n      try {\n        const date = new Date(timestamp)\n        return date.toLocaleTimeString()\n      } catch (e) {\n        return timestamp\n      }\n    }\n\n    // 获取检测标签类型\n    const getDetectionTagType = (detectionCount) => {\n      if (detectionCount === 0) return 'info'\n      if (detectionCount <= 2) return 'success'\n      if (detectionCount <= 5) return 'warning'\n      return 'danger'\n    }\n    \n    const addFrame = (frameData) => {\n      try {\n        // 计算实时帧率\n        const currentTime = Date.now()\n        if (networkStats.value.lastFrameTime > 0) {\n          const interval = currentTime - networkStats.value.lastFrameTime\n          networkStats.value.frameIntervals.push(interval)\n\n          // 保持最近10个间隔用于计算平均帧率\n          if (networkStats.value.frameIntervals.length > 10) {\n            networkStats.value.frameIntervals.shift()\n          }\n\n          // 计算实时帧率\n          const avgInterval = networkStats.value.frameIntervals.reduce((a, b) => a + b, 0) / networkStats.value.frameIntervals.length\n          networkStats.value.realTimeFrameRate = 1000 / avgInterval\n        }\n        networkStats.value.lastFrameTime = currentTime\n\n        // 调试日志：检查接收到的帧数据\n        console.log('🎬 RealTimeFrameViewer接收到帧数据:', {\n          frameNumber: frameData.frameNumber,\n          detectionCount: frameData.detectionCount,\n          hasImageData: !!frameData.imageData,\n          imageDataLength: frameData.imageData ? frameData.imageData.length : 0,\n          realTimeFrameRate: networkStats.value.realTimeFrameRate.toFixed(2) + ' fps'\n        })\n\n        // 添加新帧到缓冲区\n        frames.value.push(frameData)\n\n        // 更新总帧数\n        if (frameData.totalFrames) {\n          totalFrames.value = frameData.totalFrames\n        }\n\n        // 检查是否有车辆检测，如果有则更新最新检测帧（定格显示）\n        if (frameData.detectionCount && frameData.detectionCount > 0) {\n          console.log(`🎯 更新最新检测帧: 帧${frameData.frameNumber}, 车辆${frameData.detectionCount}`)\n          latestDetectionFrame.value = { ...frameData }\n        }\n\n        // 限制缓冲区大小\n        while (frames.value.length > props.maxBufferFrames) {\n          frames.value.shift()\n          if (currentFrameIndex.value > 0) {\n            currentFrameIndex.value--\n          }\n        }\n\n        // 如果启用自动播放且当前在最后一帧，自动跳到新帧\n        if (autoPlay.value && currentFrameIndex.value === frames.value.length - 2) {\n          currentFrameIndex.value = frames.value.length - 1\n        }\n\n        // 发出帧接收事件\n        emit('frame-received', frameData)\n\n      } catch (error) {\n        console.error('添加帧数据失败:', error)\n        ElMessage.error('处理帧数据失败')\n      }\n    }\n    \n    const togglePlayback = () => {\n      if (isPlaying.value) {\n        stopPlayback()\n      } else {\n        startPlayback()\n      }\n    }\n    \n    const startPlayback = () => {\n      if (!hasFrames.value) return\n      \n      isPlaying.value = true\n      emit('playback-state-change', { playing: true, speed: playbackSpeed.value })\n      \n      const interval = 1000 / playbackSpeed.value // 基础间隔1秒，根据速度调整\n      \n      playbackTimer = setInterval(() => {\n        if (currentFrameIndex.value < frames.value.length - 1) {\n          currentFrameIndex.value++\n        } else {\n          // 播放完毕，停止播放\n          stopPlayback()\n        }\n      }, interval)\n    }\n    \n    const stopPlayback = () => {\n      isPlaying.value = false\n      emit('playback-state-change', { playing: false, speed: playbackSpeed.value })\n      \n      if (playbackTimer) {\n        clearInterval(playbackTimer)\n        playbackTimer = null\n      }\n    }\n    \n    const clearFrames = () => {\n      stopPlayback()\n      frames.value = []\n      currentFrameIndex.value = 0\n      totalFrames.value = 0\n      latestDetectionFrame.value = null  // 清除定格显示的检测帧\n      ElMessage.success('已清空帧缓冲区')\n    }\n\n    // 清除最新检测帧\n    const clearLatestDetection = () => {\n      latestDetectionFrame.value = null\n      console.log('🧹 已清除最新检测帧')\n      ElMessage.success('已清除最新检测结果')\n    }\n    \n    const selectFrame = (index) => {\n      if (index >= 0 && index < frames.value.length) {\n        currentFrameIndex.value = index\n      }\n    }\n    \n    const onFrameIndexChange = (value) => {\n      currentFrameIndex.value = value\n    }\n    \n    const handleImageLoad = () => {\n      // 图像加载成功\n    }\n    \n    const handleImageError = () => {\n      console.error('帧图像加载失败')\n    }\n\n    // 更新网络状况统计\n    const updateNetworkStats = () => {\n      try {\n        // 从STOMP服务获取网络统计\n        const stats = window.stompService?.getNetworkStats?.() || {}\n\n        networkStats.value = {\n          quality: stats.connectionQuality || 'good',\n          frameRate: stats.frameReceiveRate || 0,\n          averageSize: stats.averageFrameSize || 0\n        }\n\n      } catch (error) {\n        console.error('更新网络统计失败:', error)\n      }\n    }\n\n    // 启动网络状况监控\n    const startNetworkMonitoring = () => {\n      // 每2秒更新一次网络统计\n      networkStatsTimer = setInterval(updateNetworkStats, 2000)\n    }\n\n    // 停止网络状况监控\n    const stopNetworkMonitoring = () => {\n      if (networkStatsTimer) {\n        clearInterval(networkStatsTimer)\n        networkStatsTimer = null\n      }\n    }\n    \n    // 监听播放速度变化\n    watch(playbackSpeed, (newSpeed) => {\n      if (isPlaying.value) {\n        stopPlayback()\n        nextTick(() => {\n          startPlayback()\n        })\n      }\n    })\n    \n    // 监听自动播放设置变化\n    watch(autoPlay, (newValue) => {\n      if (!newValue && isPlaying.value) {\n        stopPlayback()\n      }\n    })\n    \n    // 组件挂载时启动网络监控\n    onMounted(() => {\n      startNetworkMonitoring()\n    })\n\n    // 组件卸载时清理\n    onUnmounted(() => {\n      stopPlayback()\n      stopNetworkMonitoring()\n    })\n    \n    // 暴露方法给父组件\n    const addFrameData = addFrame\n    const clearFrameData = clearFrames\n    const getFrameCount = () => frames.value.length\n    const getCurrentFrame = () => currentFrame.value\n    \n    return {\n      // 响应式数据\n      frames,\n      currentFrameIndex,\n      isPlaying,\n      autoPlay,\n      playbackSpeed,\n      totalFrames,\n      latestDetectionFrame,\n      \n      // 计算属性\n      hasFrames,\n      currentFrame,\n      visibleThumbnails,\n      networkQualityClass,\n      networkQualityText,\n      \n      // 方法\n      getFrameImageUrl,\n      formatTimestamp,\n      getDetectionTagType,\n      togglePlayback,\n      clearFrames,\n      clearLatestDetection,\n      selectFrame,\n      onFrameIndexChange,\n      handleImageLoad,\n      handleImageError,\n      \n      // 暴露给父组件的方法\n      addFrameData,\n      clearFrameData,\n      getFrameCount,\n      getCurrentFrame\n    }\n  }\n}\n</script>\n\n<style scoped>\n.realtime-frame-viewer {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.viewer-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.viewer-header h4 {\n  margin: 0;\n  color: #333;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.viewer-controls {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.viewer-content {\n  padding: 20px;\n}\n\n.frame-display {\n  position: relative;\n  width: 100%;\n  height: 400px;\n  background: #000;\n  border-radius: 6px;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.frame-display.no-frames {\n  background: #f5f5f5;\n  border: 2px dashed #d9d9d9;\n}\n\n.no-frames-message {\n  text-align: center;\n  color: #999;\n}\n\n.no-frames-message .waiting-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n  animation: spin 2s linear infinite;\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n.frame-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 检测帧容器样式 */\n.detection-frame-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1));\n  border: 2px solid rgba(16, 185, 129, 0.3);\n  border-radius: 8px;\n}\n\n.detection-frame-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n}\n\n.detection-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n}\n\n.detection-header {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 8px;\n}\n\n.detection-time {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 11px;\n}\n\n.detection-info {\n  position: absolute;\n  bottom: 12px;\n  left: 12px;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n  border-left: 3px solid #10b981;\n}\n\n.detection-status {\n  color: #10b981;\n  font-weight: 600;\n  font-size: 11px;\n}\n\n.frame-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n  border-radius: 4px;\n}\n\n.frame-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n}\n\n.frame-info {\n  position: absolute;\n  top: 12px;\n  left: 12px;\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 8px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.frame-info span {\n  white-space: nowrap;\n}\n\n.playback-controls {\n  margin-top: 20px;\n  padding: 16px;\n  background: #f8f9fa;\n  border-radius: 6px;\n}\n\n.frame-slider {\n  margin-bottom: 12px;\n}\n\n.playback-info {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  color: #666;\n}\n\n.playback-info > span {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.buffer-info {\n  color: #999;\n  font-size: 12px;\n}\n\n.network-info {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  padding: 2px 6px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n.network-good {\n  color: #10b981;\n  background: rgba(16, 185, 129, 0.1);\n}\n\n.network-fair {\n  color: #f59e0b;\n  background: rgba(245, 158, 11, 0.1);\n}\n\n.network-poor {\n  color: #ef4444;\n  background: rgba(239, 68, 68, 0.1);\n}\n\n.network-info .el-icon {\n  font-size: 14px;\n}\n\n.thumbnail-strip {\n  padding: 16px 20px;\n  background: #f8f9fa;\n  border-top: 1px solid #e9ecef;\n  overflow-x: auto;\n}\n\n.thumbnail-container {\n  display: flex;\n  gap: 8px;\n  min-height: 60px;\n}\n\n.thumbnail-item {\n  flex-shrink: 0;\n  width: 80px;\n  cursor: pointer;\n  border-radius: 4px;\n  overflow: hidden;\n  border: 2px solid transparent;\n  transition: all 0.2s ease;\n}\n\n.thumbnail-item:hover {\n  border-color: #409eff;\n  transform: translateY(-2px);\n}\n\n.thumbnail-item.active {\n  border-color: #409eff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);\n}\n\n.thumbnail-image {\n  width: 100%;\n  height: 45px;\n  object-fit: cover;\n  display: block;\n}\n\n.thumbnail-label {\n  padding: 2px 4px;\n  background: #fff;\n  text-align: center;\n  font-size: 10px;\n  color: #666;\n  border-top: 1px solid #e9ecef;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .viewer-header {\n    flex-direction: column;\n    gap: 12px;\n    align-items: stretch;\n  }\n\n  .viewer-controls {\n    justify-content: center;\n  }\n\n  .frame-display {\n    height: 300px;\n  }\n\n  .playback-info {\n    flex-direction: column;\n    gap: 8px;\n    align-items: stretch;\n  }\n\n  .thumbnail-item {\n    width: 60px;\n  }\n\n  .thumbnail-image {\n    height: 35px;\n  }\n}\n\n/* 深色模式支持 */\n@media (prefers-color-scheme: dark) {\n  .realtime-frame-viewer {\n    background: #1f1f1f;\n    color: #fff;\n  }\n\n  .viewer-header {\n    background: #2d2d2d;\n    border-bottom-color: #404040;\n  }\n\n  .viewer-header h4 {\n    color: #fff;\n  }\n\n  .frame-display.no-frames {\n    background: #2d2d2d;\n    border-color: #404040;\n  }\n\n  .no-frames-message {\n    color: #ccc;\n  }\n\n  .playback-controls {\n    background: #2d2d2d;\n  }\n\n  .thumbnail-strip {\n    background: #2d2d2d;\n    border-top-color: #404040;\n  }\n\n  .thumbnail-label {\n    background: #2d2d2d;\n    color: #ccc;\n    border-top-color: #404040;\n  }\n}\n</style>\n"], "mappings": ";;;;AAoKA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAO,QAAS,KAAI;AAC3E,SAASC,SAAQ,QAAS,cAAa;AACvC,SACEC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,UAAS,QACJ,yBAAwB;AAC/B,OAAOC,uBAAsB,MAAO,kDAAiD;AAErF,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAE;IACVP,SAAS;IACTC,UAAU;IACVC,MAAM;IACNC,OAAO;IACPC,UAAU;IACVC;EACF,CAAC;EACDG,KAAK,EAAE;IACL;IACAC,MAAM,EAAE;MACNC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACD;IACAC,SAAS,EAAE;MACTH,IAAI,EAAEI,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACD;IACAC,eAAe,EAAE;MACfN,IAAI,EAAEO,MAAM;MACZF,OAAO,EAAE;IACX;EACF,CAAC;EACDG,KAAK,EAAE,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;EAClDC,KAAKA,CAACX,KAAK,EAAE;IAAEY;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,MAAK,GAAI5B,GAAG,CAAC,EAAE;IACrB,MAAM6B,iBAAgB,GAAI7B,GAAG,CAAC,CAAC;IAC/B,MAAM8B,SAAQ,GAAI9B,GAAG,CAAC,KAAK;IAC3B,MAAM+B,QAAO,GAAI/B,GAAG,CAACe,KAAK,CAACK,SAAS;IACpC,MAAMY,aAAY,GAAIhC,GAAG,CAAC,CAAC;IAC3B,MAAMiC,WAAU,GAAIjC,GAAG,CAAC,CAAC;;IAEzB;IACA,MAAMkC,oBAAmB,GAAIlC,GAAG,CAAC,IAAI;;IAErC;IACA,MAAMmC,YAAW,GAAInC,GAAG,CAAC;MACvBoC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,EAAE;MAClBC,iBAAiB,EAAE;IACrB,CAAC;;IAED;IACA,IAAIC,aAAY,GAAI,IAAG;IACvB,IAAIC,iBAAgB,GAAI,IAAG;;IAE3B;IACA,MAAMC,SAAQ,GAAI3C,QAAQ,CAAC,MAAM2B,MAAM,CAACiB,KAAK,CAACC,MAAK,GAAI,CAAC;IACxD,MAAMC,YAAW,GAAI9C,QAAQ,CAAC,MAAM2B,MAAM,CAACiB,KAAK,CAAChB,iBAAiB,CAACgB,KAAK,KAAK,IAAI;;IAEjF;IACA,MAAMG,iBAAgB,GAAI/C,QAAQ,CAAC,MAAM;MACvC,MAAMgD,aAAY,GAAI,EAAC;MACvB,IAAIrB,MAAM,CAACiB,KAAK,CAACC,MAAK,IAAKG,aAAa,EAAE;QACxC,OAAOrB,MAAM,CAACiB,KAAI;MACpB;MAEA,MAAMK,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACxB,MAAM,CAACiB,KAAK,CAACC,MAAK,GAAIG,aAAa;MAC3D,OAAOrB,MAAM,CAACiB,KAAK,CAACQ,MAAM,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAKA,KAAI,GAAIL,IAAG,KAAM,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,EAAEP,aAAa;IACrF,CAAC;;IAED;IACA,MAAMQ,mBAAkB,GAAIxD,QAAQ,CAAC,MAAM;MACzC,OAAO,WAAWkC,YAAY,CAACU,KAAK,CAACT,OAAO,EAAC;IAC/C,CAAC;IAED,MAAMsB,kBAAiB,GAAIzD,QAAQ,CAAC,MAAM;MACxC,MAAMmC,OAAM,GAAID,YAAY,CAACU,KAAK,CAACT,OAAM;MACzC,MAAMuB,IAAG,GAAIxB,YAAY,CAACU,KAAK,CAACJ,iBAAgB,GAAI,IAChDN,YAAY,CAACU,KAAK,CAACJ,iBAAiB,CAACmB,OAAO,CAAC,CAAC,IAC9CzB,YAAY,CAACU,KAAK,CAACR,SAAS,CAACuB,OAAO,CAAC,CAAC;MAE1C,QAAQxB,OAAO;QACb,KAAK,MAAM;UACT,OAAO,MAAMuB,IAAI,KAAI;QACvB,KAAK,MAAM;UACT,OAAO,MAAMA,IAAI,KAAI;QACvB,KAAK,MAAM;UACT,OAAO,MAAMA,IAAI,KAAI;QACvB;UACE,OAAO,MAAMA,IAAI,KAAI;MACzB;IACF,CAAC;;IAED;IACA,MAAME,gBAAe,GAAKC,KAAK,IAAK;MAClC,IAAI,CAACA,KAAI,IAAK,CAACA,KAAK,CAACC,SAAS,EAAE;QAC9BC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;UAAEH,KAAK,EAAE,CAAC,CAACA,KAAK;UAAEI,YAAY,EAAEJ,KAAI,GAAI,CAAC,CAACA,KAAK,CAACC,SAAQ,GAAI;QAAM,CAAC;QAC/G,OAAO,EAAC;MACV;MACA,MAAMI,QAAO,GAAI,0BAA0BL,KAAK,CAACC,SAAS,EAAC;MAC3DC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CG,WAAW,EAAEN,KAAK,CAACM,WAAW;QAC9BC,eAAe,EAAEP,KAAK,CAACC,SAAS,CAACjB,MAAM;QACvCwB,SAAS,EAAEH,QAAQ,CAACrB,MAAM;QAC1ByB,SAAS,EAAEJ,QAAQ,CAACK,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI;MACzC,CAAC;MACD,OAAOL,QAAO;IAChB;IAEA,MAAMM,eAAc,GAAKC,SAAS,IAAK;MACrC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAC;MACxB,IAAI;QACF,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,SAAS;QAC/B,OAAOC,IAAI,CAACE,kBAAkB,CAAC;MACjC,EAAE,OAAOC,CAAC,EAAE;QACV,OAAOJ,SAAQ;MACjB;IACF;;IAEA;IACA,MAAMK,mBAAkB,GAAKC,cAAc,IAAK;MAC9C,IAAIA,cAAa,KAAM,CAAC,EAAE,OAAO,MAAK;MACtC,IAAIA,cAAa,IAAK,CAAC,EAAE,OAAO,SAAQ;MACxC,IAAIA,cAAa,IAAK,CAAC,EAAE,OAAO,SAAQ;MACxC,OAAO,QAAO;IAChB;IAEA,MAAMC,QAAO,GAAKC,SAAS,IAAK;MAC9B,IAAI;QACF;QACA,MAAMC,WAAU,GAAIP,IAAI,CAACQ,GAAG,CAAC;QAC7B,IAAIjD,YAAY,CAACU,KAAK,CAACN,aAAY,GAAI,CAAC,EAAE;UACxC,MAAM8C,QAAO,GAAIF,WAAU,GAAIhD,YAAY,CAACU,KAAK,CAACN,aAAY;UAC9DJ,YAAY,CAACU,KAAK,CAACL,cAAc,CAAC8C,IAAI,CAACD,QAAQ;;UAE/C;UACA,IAAIlD,YAAY,CAACU,KAAK,CAACL,cAAc,CAACM,MAAK,GAAI,EAAE,EAAE;YACjDX,YAAY,CAACU,KAAK,CAACL,cAAc,CAAC+C,KAAK,CAAC;UAC1C;;UAEA;UACA,MAAMC,WAAU,GAAIrD,YAAY,CAACU,KAAK,CAACL,cAAc,CAACiD,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAA,GAAIC,CAAC,EAAE,CAAC,IAAIxD,YAAY,CAACU,KAAK,CAACL,cAAc,CAACM,MAAK;UAC1HX,YAAY,CAACU,KAAK,CAACJ,iBAAgB,GAAI,IAAG,GAAI+C,WAAU;QAC1D;QACArD,YAAY,CAACU,KAAK,CAACN,aAAY,GAAI4C,WAAU;;QAE7C;QACAnB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;UAC3CG,WAAW,EAAEc,SAAS,CAACd,WAAW;UAClCY,cAAc,EAAEE,SAAS,CAACF,cAAc;UACxCd,YAAY,EAAE,CAAC,CAACgB,SAAS,CAACnB,SAAS;UACnCM,eAAe,EAAEa,SAAS,CAACnB,SAAQ,GAAImB,SAAS,CAACnB,SAAS,CAACjB,MAAK,GAAI,CAAC;UACrEL,iBAAiB,EAAEN,YAAY,CAACU,KAAK,CAACJ,iBAAiB,CAACmB,OAAO,CAAC,CAAC,IAAI;QACvE,CAAC;;QAED;QACAhC,MAAM,CAACiB,KAAK,CAACyC,IAAI,CAACJ,SAAS;;QAE3B;QACA,IAAIA,SAAS,CAACjD,WAAW,EAAE;UACzBA,WAAW,CAACY,KAAI,GAAIqC,SAAS,CAACjD,WAAU;QAC1C;;QAEA;QACA,IAAIiD,SAAS,CAACF,cAAa,IAAKE,SAAS,CAACF,cAAa,GAAI,CAAC,EAAE;UAC5DhB,OAAO,CAACC,GAAG,CAAC,gBAAgBiB,SAAS,CAACd,WAAW,OAAOc,SAAS,CAACF,cAAc,EAAE;UAClF9C,oBAAoB,CAACW,KAAI,GAAI;YAAE,GAAGqC;UAAU;QAC9C;;QAEA;QACA,OAAOtD,MAAM,CAACiB,KAAK,CAACC,MAAK,GAAI/B,KAAK,CAACQ,eAAe,EAAE;UAClDK,MAAM,CAACiB,KAAK,CAAC0C,KAAK,CAAC;UACnB,IAAI1D,iBAAiB,CAACgB,KAAI,GAAI,CAAC,EAAE;YAC/BhB,iBAAiB,CAACgB,KAAK,EAAC;UAC1B;QACF;;QAEA;QACA,IAAId,QAAQ,CAACc,KAAI,IAAKhB,iBAAiB,CAACgB,KAAI,KAAMjB,MAAM,CAACiB,KAAK,CAACC,MAAK,GAAI,CAAC,EAAE;UACzEjB,iBAAiB,CAACgB,KAAI,GAAIjB,MAAM,CAACiB,KAAK,CAACC,MAAK,GAAI;QAClD;;QAEA;QACAnB,IAAI,CAAC,gBAAgB,EAAEuD,SAAS;MAElC,EAAE,OAAOU,KAAK,EAAE;QACd5B,OAAO,CAAC4B,KAAK,CAAC,UAAU,EAAEA,KAAK;QAC/BtF,SAAS,CAACsF,KAAK,CAAC,SAAS;MAC3B;IACF;IAEA,MAAMC,cAAa,GAAIA,CAAA,KAAM;MAC3B,IAAI/D,SAAS,CAACe,KAAK,EAAE;QACnBiD,YAAY,CAAC;MACf,OAAO;QACLC,aAAa,CAAC;MAChB;IACF;IAEA,MAAMA,aAAY,GAAIA,CAAA,KAAM;MAC1B,IAAI,CAACnD,SAAS,CAACC,KAAK,EAAE;MAEtBf,SAAS,CAACe,KAAI,GAAI,IAAG;MACrBlB,IAAI,CAAC,uBAAuB,EAAE;QAAEqE,OAAO,EAAE,IAAI;QAAEC,KAAK,EAAEjE,aAAa,CAACa;MAAM,CAAC;MAE3E,MAAMwC,QAAO,GAAI,IAAG,GAAIrD,aAAa,CAACa,KAAI,EAAE;;MAE5CH,aAAY,GAAIwD,WAAW,CAAC,MAAM;QAChC,IAAIrE,iBAAiB,CAACgB,KAAI,GAAIjB,MAAM,CAACiB,KAAK,CAACC,MAAK,GAAI,CAAC,EAAE;UACrDjB,iBAAiB,CAACgB,KAAK,EAAC;QAC1B,OAAO;UACL;UACAiD,YAAY,CAAC;QACf;MACF,CAAC,EAAET,QAAQ;IACb;IAEA,MAAMS,YAAW,GAAIA,CAAA,KAAM;MACzBhE,SAAS,CAACe,KAAI,GAAI,KAAI;MACtBlB,IAAI,CAAC,uBAAuB,EAAE;QAAEqE,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEjE,aAAa,CAACa;MAAM,CAAC;MAE5E,IAAIH,aAAa,EAAE;QACjByD,aAAa,CAACzD,aAAa;QAC3BA,aAAY,GAAI,IAAG;MACrB;IACF;IAEA,MAAM0D,WAAU,GAAIA,CAAA,KAAM;MACxBN,YAAY,CAAC;MACblE,MAAM,CAACiB,KAAI,GAAI,EAAC;MAChBhB,iBAAiB,CAACgB,KAAI,GAAI;MAC1BZ,WAAW,CAACY,KAAI,GAAI;MACpBX,oBAAoB,CAACW,KAAI,GAAI,IAAG,EAAG;MACnCvC,SAAS,CAAC+F,OAAO,CAAC,SAAS;IAC7B;;IAEA;IACA,MAAMC,oBAAmB,GAAIA,CAAA,KAAM;MACjCpE,oBAAoB,CAACW,KAAI,GAAI,IAAG;MAChCmB,OAAO,CAACC,GAAG,CAAC,aAAa;MACzB3D,SAAS,CAAC+F,OAAO,CAAC,WAAW;IAC/B;IAEA,MAAME,WAAU,GAAKhD,KAAK,IAAK;MAC7B,IAAIA,KAAI,IAAK,KAAKA,KAAI,GAAI3B,MAAM,CAACiB,KAAK,CAACC,MAAM,EAAE;QAC7CjB,iBAAiB,CAACgB,KAAI,GAAIU,KAAI;MAChC;IACF;IAEA,MAAMiD,kBAAiB,GAAK3D,KAAK,IAAK;MACpChB,iBAAiB,CAACgB,KAAI,GAAIA,KAAI;IAChC;IAEA,MAAM4D,eAAc,GAAIA,CAAA,KAAM;MAC5B;IAAA,CACF;IAEA,MAAMC,gBAAe,GAAIA,CAAA,KAAM;MAC7B1C,OAAO,CAAC4B,KAAK,CAAC,SAAS;IACzB;;IAEA;IACA,MAAMe,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,IAAI;QACF;QACA,MAAMC,KAAI,GAAIC,MAAM,CAACC,YAAY,EAAEC,eAAe,GAAG,KAAK,CAAC;QAE3D5E,YAAY,CAACU,KAAI,GAAI;UACnBT,OAAO,EAAEwE,KAAK,CAACI,iBAAgB,IAAK,MAAM;UAC1C3E,SAAS,EAAEuE,KAAK,CAACK,gBAAe,IAAK,CAAC;UACtC3E,WAAW,EAAEsE,KAAK,CAACM,gBAAe,IAAK;QACzC;MAEF,EAAE,OAAOtB,KAAK,EAAE;QACd5B,OAAO,CAAC4B,KAAK,CAAC,WAAW,EAAEA,KAAK;MAClC;IACF;;IAEA;IACA,MAAMuB,sBAAqB,GAAIA,CAAA,KAAM;MACnC;MACAxE,iBAAgB,GAAIuD,WAAW,CAACS,kBAAkB,EAAE,IAAI;IAC1D;;IAEA;IACA,MAAMS,qBAAoB,GAAIA,CAAA,KAAM;MAClC,IAAIzE,iBAAiB,EAAE;QACrBwD,aAAa,CAACxD,iBAAiB;QAC/BA,iBAAgB,GAAI,IAAG;MACzB;IACF;;IAEA;IACAzC,KAAK,CAAC8B,aAAa,EAAGqF,QAAQ,IAAK;MACjC,IAAIvF,SAAS,CAACe,KAAK,EAAE;QACnBiD,YAAY,CAAC;QACbzF,QAAQ,CAAC,MAAM;UACb0F,aAAa,CAAC;QAChB,CAAC;MACH;IACF,CAAC;;IAED;IACA7F,KAAK,CAAC6B,QAAQ,EAAGuF,QAAQ,IAAK;MAC5B,IAAI,CAACA,QAAO,IAAKxF,SAAS,CAACe,KAAK,EAAE;QAChCiD,YAAY,CAAC;MACf;IACF,CAAC;;IAED;IACA3F,SAAS,CAAC,MAAM;MACdgH,sBAAsB,CAAC;IACzB,CAAC;;IAED;IACA/G,WAAW,CAAC,MAAM;MAChB0F,YAAY,CAAC;MACbsB,qBAAqB,CAAC;IACxB,CAAC;;IAED;IACA,MAAMG,YAAW,GAAItC,QAAO;IAC5B,MAAMuC,cAAa,GAAIpB,WAAU;IACjC,MAAMqB,aAAY,GAAIA,CAAA,KAAM7F,MAAM,CAACiB,KAAK,CAACC,MAAK;IAC9C,MAAM4E,eAAc,GAAIA,CAAA,KAAM3E,YAAY,CAACF,KAAI;IAE/C,OAAO;MACL;MACAjB,MAAM;MACNC,iBAAiB;MACjBC,SAAS;MACTC,QAAQ;MACRC,aAAa;MACbC,WAAW;MACXC,oBAAoB;MAEpB;MACAU,SAAS;MACTG,YAAY;MACZC,iBAAiB;MACjBS,mBAAmB;MACnBC,kBAAkB;MAElB;MACAG,gBAAgB;MAChBY,eAAe;MACfM,mBAAmB;MACnBc,cAAc;MACdO,WAAW;MACXE,oBAAoB;MACpBC,WAAW;MACXC,kBAAkB;MAClBC,eAAe;MACfC,gBAAgB;MAEhB;MACAa,YAAY;MACZC,cAAc;MACdC,aAAa;MACbC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}