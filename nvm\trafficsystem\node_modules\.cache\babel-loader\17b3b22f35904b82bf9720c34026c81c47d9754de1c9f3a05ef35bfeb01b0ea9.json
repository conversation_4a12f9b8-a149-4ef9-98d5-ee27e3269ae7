{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveDynamicComponent as _resolveDynamicComponent, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"intelligent-traffic-report\"\n};\nconst _hoisted_2 = {\n  class: \"report-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-content\"\n};\nconst _hoisted_4 = {\n  class: \"report-title\"\n};\nconst _hoisted_5 = {\n  class: \"report-meta\"\n};\nconst _hoisted_6 = {\n  class: \"header-actions\"\n};\nconst _hoisted_7 = {\n  class: \"executive-summary\"\n};\nconst _hoisted_8 = {\n  class: \"summary-grid\"\n};\nconst _hoisted_9 = {\n  class: \"summary-card highlight\"\n};\nconst _hoisted_10 = {\n  class: \"card-content\"\n};\nconst _hoisted_11 = {\n  class: \"card-value\"\n};\nconst _hoisted_12 = {\n  class: \"card-change positive\"\n};\nconst _hoisted_13 = {\n  class: \"summary-card\"\n};\nconst _hoisted_14 = {\n  class: \"card-content\"\n};\nconst _hoisted_15 = {\n  class: \"card-value\"\n};\nconst _hoisted_16 = {\n  class: \"card-change\"\n};\nconst _hoisted_17 = {\n  class: \"summary-card\"\n};\nconst _hoisted_18 = {\n  class: \"card-content\"\n};\nconst _hoisted_19 = {\n  class: \"card-value\"\n};\nconst _hoisted_20 = {\n  class: \"card-change\"\n};\nconst _hoisted_21 = {\n  class: \"summary-card\"\n};\nconst _hoisted_22 = {\n  class: \"card-content\"\n};\nconst _hoisted_23 = {\n  class: \"card-value\"\n};\nconst _hoisted_24 = {\n  class: \"direction-analysis\"\n};\nconst _hoisted_25 = {\n  class: \"direction-grid\"\n};\nconst _hoisted_26 = {\n  class: \"direction-header\"\n};\nconst _hoisted_27 = {\n  class: \"direction-stats\"\n};\nconst _hoisted_28 = {\n  class: \"stat-row\"\n};\nconst _hoisted_29 = {\n  class: \"stat-value\"\n};\nconst _hoisted_30 = {\n  class: \"stat-row\"\n};\nconst _hoisted_31 = {\n  class: \"stat-value\"\n};\nconst _hoisted_32 = {\n  class: \"stat-row\"\n};\nconst _hoisted_33 = {\n  class: \"stat-value\"\n};\nconst _hoisted_34 = {\n  class: \"stat-row\"\n};\nconst _hoisted_35 = {\n  class: \"direction-chart\"\n};\nconst _hoisted_36 = {\n  class: \"intelligent-analysis\"\n};\nconst _hoisted_37 = {\n  class: \"analysis-section\"\n};\nconst _hoisted_38 = {\n  class: \"flow-analysis\"\n};\nconst _hoisted_39 = {\n  class: \"flow-chart-container\"\n};\nconst _hoisted_40 = {\n  ref: \"flowChart\",\n  class: \"flow-chart\"\n};\nconst _hoisted_41 = {\n  class: \"flow-insights\"\n};\nconst _hoisted_42 = {\n  class: \"insight-item\"\n};\nconst _hoisted_43 = {\n  class: \"insight-item\"\n};\nconst _hoisted_44 = {\n  class: \"insight-item\"\n};\nconst _hoisted_45 = {\n  class: \"analysis-section\"\n};\nconst _hoisted_46 = {\n  class: \"congestion-analysis\"\n};\nconst _hoisted_47 = {\n  class: \"congestion-heatmap\"\n};\nconst _hoisted_48 = {\n  ref: \"heatmapChart\",\n  class: \"heatmap-chart\"\n};\nconst _hoisted_49 = {\n  class: \"congestion-insights\"\n};\nconst _hoisted_50 = {\n  class: \"optimization-recommendations\"\n};\nconst _hoisted_51 = {\n  class: \"recommendations-list\"\n};\nconst _hoisted_52 = {\n  class: \"recommendation-header\"\n};\nconst _hoisted_53 = {\n  class: \"recommendation-description\"\n};\nconst _hoisted_54 = {\n  class: \"recommendation-impact\"\n};\nconst _hoisted_55 = {\n  class: \"impact-value\"\n};\nconst _hoisted_56 = {\n  class: \"technical-metrics\"\n};\nconst _hoisted_57 = {\n  class: \"metrics-grid\"\n};\nconst _hoisted_58 = {\n  class: \"metric-card\"\n};\nconst _hoisted_59 = {\n  class: \"metric-value\"\n};\nconst _hoisted_60 = {\n  class: \"metric-card\"\n};\nconst _hoisted_61 = {\n  class: \"metric-value\"\n};\nconst _hoisted_62 = {\n  class: \"metric-card\"\n};\nconst _hoisted_63 = {\n  class: \"metric-value\"\n};\nconst _hoisted_64 = {\n  class: \"metric-card\"\n};\nconst _hoisted_65 = {\n  class: \"metric-value\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_DataAnalysis = _resolveComponent(\"DataAnalysis\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_Download = _resolveComponent(\"Download\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 报告头部 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"h1\", _hoisted_4, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_DataAnalysis)]),\n    _: 1 /* STABLE */\n  }), _cache[0] || (_cache[0] = _createTextVNode(\" 四方向智能交通分析报告 \"))]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_tag, {\n    type: \"info\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"任务ID: \" + _toDisplayString($props.taskId), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_tag, {\n    type: \"success\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDate(_ctx.safeReportData.generatedAt)), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_tag, null, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.safeReportData.analysisType), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  })])]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.exportReport,\n    loading: $setup.exporting\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Download)]),\n      _: 1 /* STABLE */\n    }), _cache[1] || (_cache[1] = _createTextVNode(\" 导出报告 \"))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"loading\"]), _createVNode(_component_el_button, {\n    onClick: $setup.refreshData,\n    loading: $setup.refreshing\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[2] || (_cache[2] = _createTextVNode(\" 刷新数据 \"))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"loading\"])])]), _createCommentVNode(\" 执行摘要 \"), _createElementVNode(\"div\", _hoisted_7, [_cache[11] || (_cache[11] = _createElementVNode(\"h2\", null, \"执行摘要\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"card-icon\"\n  }, \"🚗\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString(_ctx.safeReportData.summary.totalVehicles), 1 /* TEXT */), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n    class: \"card-label\"\n  }, \"总检测车辆\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, \"+\" + _toDisplayString(_ctx.safeReportData.summary.vehicleIncrease) + \"%\", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_13, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"card-icon\"\n  }, \"⏱️\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, _toDisplayString($setup.formatDuration($props.reportData.summary.processingDuration)), 1 /* TEXT */), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"card-label\"\n  }, \"分析时长\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_16, _toDisplayString($props.reportData.summary.efficiency) + \"% 效率\", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_17, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"card-icon\"\n  }, \"📍\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, _toDisplayString($props.reportData.summary.peakDirection), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"card-label\"\n  }, \"最繁忙方向\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_20, _toDisplayString($props.reportData.summary.peakPercentage) + \"% 占比\", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_21, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n    class: \"card-icon\"\n  }, \"🚨\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, _toDisplayString($props.reportData.summary.congestionLevel), 1 /* TEXT */), _cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n    class: \"card-label\"\n  }, \"拥堵等级\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"card-change\", $setup.getCongestionClass()])\n  }, _toDisplayString($props.reportData.summary.congestionTrend), 3 /* TEXT, CLASS */)])])])]), _createCommentVNode(\" 方向详细分析 \"), _createElementVNode(\"div\", _hoisted_24, [_cache[16] || (_cache[16] = _createElementVNode(\"h2\", null, \"方向详细分析\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_25, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.reportData.directions, (data, direction) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: direction,\n      class: \"direction-card\"\n    }, [_createElementVNode(\"div\", _hoisted_26, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent($setup.getDirectionIcon(direction))))]),\n      _: 2 /* DYNAMIC */\n    }, 1024 /* DYNAMIC_SLOTS */), _createElementVNode(\"h3\", null, _toDisplayString($setup.getDirectionName(direction)), 1 /* TEXT */), _createVNode(_component_el_tag, {\n      type: $setup.getDirectionStatusType(data.status)\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString(data.status), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_cache[12] || (_cache[12] = _createElementVNode(\"span\", {\n      class: \"stat-label\"\n    }, \"检测车辆:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_29, _toDisplayString(data.vehicleCount || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_30, [_cache[13] || (_cache[13] = _createElementVNode(\"span\", {\n      class: \"stat-label\"\n    }, \"平均速度:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_31, _toDisplayString(data.averageSpeed || 0) + \" km/h\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_32, [_cache[14] || (_cache[14] = _createElementVNode(\"span\", {\n      class: \"stat-label\"\n    }, \"流量密度:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_33, _toDisplayString(data.density || data.averageFlowDensity || 0) + \" 辆/km\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_34, [_cache[15] || (_cache[15] = _createElementVNode(\"span\", {\n      class: \"stat-label\"\n    }, \"拥堵等级:\", -1 /* HOISTED */)), _createElementVNode(\"span\", {\n      class: _normalizeClass([\"stat-value\", $setup.getCongestionIndexClass(data.congestionIndex || 0)])\n    }, _toDisplayString(data.crowdLevel || data.congestionLevel || '畅通'), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"div\", {\n      ref_for: true,\n      ref: `chart_${direction}`,\n      class: \"mini-chart\"\n    }, null, 512 /* NEED_PATCH */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 智能分析结果 \"), _createElementVNode(\"div\", _hoisted_36, [_cache[22] || (_cache[22] = _createElementVNode(\"h2\", null, \"智能分析结果\", -1 /* HOISTED */)), _createCommentVNode(\" 交通流量分析 \"), _createElementVNode(\"div\", _hoisted_37, [_cache[20] || (_cache[20] = _createElementVNode(\"h3\", null, \"交通流量分析\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"div\", _hoisted_40, null, 512 /* NEED_PATCH */)]), _createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"div\", _hoisted_42, [_cache[17] || (_cache[17] = _createElementVNode(\"strong\", null, \"流量平衡度:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($props.reportData.intelligentAnalysis.flowBalance) + \"% \", 1 /* TEXT */), _createVNode(_component_el_progress, {\n    percentage: $props.reportData.intelligentAnalysis.flowBalance,\n    \"show-text\": false\n  }, null, 8 /* PROPS */, [\"percentage\"])]), _createElementVNode(\"div\", _hoisted_43, [_cache[18] || (_cache[18] = _createElementVNode(\"strong\", null, \"峰值时段:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($props.reportData.intelligentAnalysis.peakHours), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_44, [_cache[19] || (_cache[19] = _createElementVNode(\"strong\", null, \"流量趋势:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($props.reportData.intelligentAnalysis.flowTrend), 1 /* TEXT */)])])])]), _createCommentVNode(\" 拥堵分析 \"), _createElementVNode(\"div\", _hoisted_45, [_cache[21] || (_cache[21] = _createElementVNode(\"h3\", null, \"拥堵分析\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_46, [_createElementVNode(\"div\", _hoisted_47, [_createElementVNode(\"div\", _hoisted_48, null, 512 /* NEED_PATCH */)]), _createElementVNode(\"div\", _hoisted_49, [_createVNode(_component_el_alert, {\n    title: `当前拥堵等级: ${$props.reportData.summary?.congestionLevel || '畅通'}`,\n    type: $setup.getCongestionAlertType(),\n    description: $props.reportData.intelligentAnalysis?.congestionDescription || '交通状况良好，建议保持当前管理策略',\n    \"show-icon\": \"\",\n    closable: false\n  }, null, 8 /* PROPS */, [\"title\", \"type\", \"description\"])])])])]), _createCommentVNode(\" 优化建议 \"), _createElementVNode(\"div\", _hoisted_50, [_cache[24] || (_cache[24] = _createElementVNode(\"h2\", null, \"优化建议\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_51, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.reportData.recommendations, (recommendation, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: _normalizeClass([\"recommendation-item\", recommendation.priority])\n    }, [_createElementVNode(\"div\", _hoisted_52, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent($setup.getRecommendationIcon(recommendation.type))))]),\n      _: 2 /* DYNAMIC */\n    }, 1024 /* DYNAMIC_SLOTS */), _createElementVNode(\"h4\", null, _toDisplayString(recommendation.title), 1 /* TEXT */), _createVNode(_component_el_tag, {\n      type: $setup.getPriorityType(recommendation.priority),\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getPriorityText(recommendation.priority)), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]), _createElementVNode(\"p\", _hoisted_53, _toDisplayString(recommendation.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_54, [_cache[23] || (_cache[23] = _createElementVNode(\"span\", {\n      class: \"impact-label\"\n    }, \"预期效果:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_55, _toDisplayString(recommendation.expectedImprovement), 1 /* TEXT */)])], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 技术指标 \"), _createElementVNode(\"div\", _hoisted_56, [_cache[29] || (_cache[29] = _createElementVNode(\"h2\", null, \"技术指标\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_57, [_createElementVNode(\"div\", _hoisted_58, [_cache[25] || (_cache[25] = _createElementVNode(\"h4\", null, \"检测精度\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_59, _toDisplayString($props.reportData.technicalMetrics.accuracy) + \"%\", 1 /* TEXT */), _createVNode(_component_el_progress, {\n    percentage: $props.reportData.technicalMetrics.accuracy,\n    \"show-text\": false\n  }, null, 8 /* PROPS */, [\"percentage\"])]), _createElementVNode(\"div\", _hoisted_60, [_cache[26] || (_cache[26] = _createElementVNode(\"h4\", null, \"处理速度\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_61, _toDisplayString($props.reportData.technicalMetrics.processingSpeed) + \" FPS\", 1 /* TEXT */), _createVNode(_component_el_progress, {\n    percentage: $setup.getSpeedPercentage(),\n    \"show-text\": false\n  }, null, 8 /* PROPS */, [\"percentage\"])]), _createElementVNode(\"div\", _hoisted_62, [_cache[27] || (_cache[27] = _createElementVNode(\"h4\", null, \"系统稳定性\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_63, _toDisplayString($props.reportData.technicalMetrics.stability) + \"%\", 1 /* TEXT */), _createVNode(_component_el_progress, {\n    percentage: $props.reportData.technicalMetrics.stability,\n    \"show-text\": false\n  }, null, 8 /* PROPS */, [\"percentage\"])]), _createElementVNode(\"div\", _hoisted_64, [_cache[28] || (_cache[28] = _createElementVNode(\"h4\", null, \"数据完整性\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_65, _toDisplayString($props.reportData.technicalMetrics.dataIntegrity) + \"%\", 1 /* TEXT */), _createVNode(_component_el_progress, {\n    percentage: $props.reportData.technicalMetrics.dataIntegrity,\n    \"show-text\": false\n  }, null, 8 /* PROPS */, [\"percentage\"])])])])]);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_DataAnalysis", "_", "_createTextVNode", "_hoisted_5", "_component_el_tag", "type", "_toDisplayString", "$props", "taskId", "$setup", "formatDate", "_ctx", "safeReportData", "generatedAt", "analysisType", "_hoisted_6", "_component_el_button", "onClick", "exportReport", "loading", "exporting", "_component_Download", "refreshData", "refreshing", "_component_Refresh", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "summary", "totalVehicles", "_hoisted_12", "vehicleIncrease", "_hoisted_13", "_hoisted_14", "_hoisted_15", "formatDuration", "reportData", "processingDuration", "_hoisted_16", "efficiency", "_hoisted_17", "_hoisted_18", "_hoisted_19", "peakDirection", "_hoisted_20", "peakPercentage", "_hoisted_21", "_hoisted_22", "_hoisted_23", "congestionLevel", "_normalizeClass", "getCongestionClass", "congestionTrend", "_hoisted_24", "_hoisted_25", "_Fragment", "_renderList", "directions", "data", "direction", "key", "_hoisted_26", "_createBlock", "_resolveDynamicComponent", "getDirectionIcon", "getDirectionName", "getDirectionStatusType", "status", "_hoisted_27", "_hoisted_28", "_hoisted_29", "vehicleCount", "_hoisted_30", "_hoisted_31", "averageSpeed", "_hoisted_32", "_hoisted_33", "density", "averageFlowDensity", "_hoisted_34", "getCongestionIndexClass", "congestionIndex", "crowdLevel", "_hoisted_35", "ref_for", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "intelligentAnalysis", "flowBalance", "_component_el_progress", "percentage", "_hoisted_43", "peakHours", "_hoisted_44", "flowTrend", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_component_el_alert", "title", "getCongestionAlertType", "description", "congestionDescription", "closable", "_hoisted_50", "_hoisted_51", "recommendations", "recommendation", "index", "priority", "_hoisted_52", "getRecommendationIcon", "getPriorityType", "size", "getPriorityText", "_hoisted_53", "_hoisted_54", "_hoisted_55", "expectedImprovement", "_hoisted_56", "_hoisted_57", "_hoisted_58", "_hoisted_59", "technicalMetrics", "accuracy", "_hoisted_60", "_hoisted_61", "processingSpeed", "getSpeedPercentage", "_hoisted_62", "_hoisted_63", "stability", "_hoisted_64", "_hoisted_65", "dataIntegrity"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\analysis\\IntelligentTrafficReport.vue"], "sourcesContent": ["<template>\n  <div class=\"intelligent-traffic-report\">\n    <!-- 报告头部 -->\n    <div class=\"report-header\">\n      <div class=\"header-content\">\n        <h1 class=\"report-title\">\n          <el-icon><DataAnalysis /></el-icon>\n          四方向智能交通分析报告\n        </h1>\n        <div class=\"report-meta\">\n          <el-tag type=\"info\">任务ID: {{ taskId }}</el-tag>\n          <el-tag type=\"success\">{{ formatDate(safeReportData.generatedAt) }}</el-tag>\n          <el-tag>{{ safeReportData.analysisType }}</el-tag>\n        </div>\n      </div>\n      <div class=\"header-actions\">\n        <el-button type=\"primary\" @click=\"exportReport\" :loading=\"exporting\">\n          <el-icon><Download /></el-icon>\n          导出报告\n        </el-button>\n        <el-button @click=\"refreshData\" :loading=\"refreshing\">\n          <el-icon><Refresh /></el-icon>\n          刷新数据\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 执行摘要 -->\n    <div class=\"executive-summary\">\n      <h2>执行摘要</h2>\n      <div class=\"summary-grid\">\n        <div class=\"summary-card highlight\">\n          <div class=\"card-icon\">🚗</div>\n          <div class=\"card-content\">\n            <div class=\"card-value\">{{ safeReportData.summary.totalVehicles }}</div>\n            <div class=\"card-label\">总检测车辆</div>\n            <div class=\"card-change positive\">+{{ safeReportData.summary.vehicleIncrease }}%</div>\n          </div>\n        </div>\n        \n        <div class=\"summary-card\">\n          <div class=\"card-icon\">⏱️</div>\n          <div class=\"card-content\">\n            <div class=\"card-value\">{{ formatDuration(reportData.summary.processingDuration) }}</div>\n            <div class=\"card-label\">分析时长</div>\n            <div class=\"card-change\">{{ reportData.summary.efficiency }}% 效率</div>\n          </div>\n        </div>\n        \n        <div class=\"summary-card\">\n          <div class=\"card-icon\">📍</div>\n          <div class=\"card-content\">\n            <div class=\"card-value\">{{ reportData.summary.peakDirection }}</div>\n            <div class=\"card-label\">最繁忙方向</div>\n            <div class=\"card-change\">{{ reportData.summary.peakPercentage }}% 占比</div>\n          </div>\n        </div>\n        \n        <div class=\"summary-card\">\n          <div class=\"card-icon\">🚨</div>\n          <div class=\"card-content\">\n            <div class=\"card-value\">{{ reportData.summary.congestionLevel }}</div>\n            <div class=\"card-label\">拥堵等级</div>\n            <div class=\"card-change\" :class=\"getCongestionClass()\">\n              {{ reportData.summary.congestionTrend }}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 方向详细分析 -->\n    <div class=\"direction-analysis\">\n      <h2>方向详细分析</h2>\n      <div class=\"direction-grid\">\n        <div \n          v-for=\"(data, direction) in reportData.directions\" \n          :key=\"direction\"\n          class=\"direction-card\"\n        >\n          <div class=\"direction-header\">\n            <el-icon>\n              <component :is=\"getDirectionIcon(direction)\" />\n            </el-icon>\n            <h3>{{ getDirectionName(direction) }}</h3>\n            <el-tag :type=\"getDirectionStatusType(data.status)\">\n              {{ data.status }}\n            </el-tag>\n          </div>\n          \n          <div class=\"direction-stats\">\n            <div class=\"stat-row\">\n              <span class=\"stat-label\">检测车辆:</span>\n              <span class=\"stat-value\">{{ data.vehicleCount || 0 }}</span>\n            </div>\n            <div class=\"stat-row\">\n              <span class=\"stat-label\">平均速度:</span>\n              <span class=\"stat-value\">{{ data.averageSpeed || 0 }} km/h</span>\n            </div>\n            <div class=\"stat-row\">\n              <span class=\"stat-label\">流量密度:</span>\n              <span class=\"stat-value\">{{ data.density || data.averageFlowDensity || 0 }} 辆/km</span>\n            </div>\n            <div class=\"stat-row\">\n              <span class=\"stat-label\">拥堵等级:</span>\n              <span class=\"stat-value\" :class=\"getCongestionIndexClass(data.congestionIndex || 0)\">\n                {{ data.crowdLevel || data.congestionLevel || '畅通' }}\n              </span>\n            </div>\n          </div>\n          \n          <div class=\"direction-chart\">\n            <div :ref=\"`chart_${direction}`\" class=\"mini-chart\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 智能分析结果 -->\n    <div class=\"intelligent-analysis\">\n      <h2>智能分析结果</h2>\n      \n      <!-- 交通流量分析 -->\n      <div class=\"analysis-section\">\n        <h3>交通流量分析</h3>\n        <div class=\"flow-analysis\">\n          <div class=\"flow-chart-container\">\n            <div ref=\"flowChart\" class=\"flow-chart\"></div>\n          </div>\n          <div class=\"flow-insights\">\n            <div class=\"insight-item\">\n              <strong>流量平衡度:</strong> {{ reportData.intelligentAnalysis.flowBalance }}%\n              <el-progress :percentage=\"reportData.intelligentAnalysis.flowBalance\" :show-text=\"false\" />\n            </div>\n            <div class=\"insight-item\">\n              <strong>峰值时段:</strong> {{ reportData.intelligentAnalysis.peakHours }}\n            </div>\n            <div class=\"insight-item\">\n              <strong>流量趋势:</strong> {{ reportData.intelligentAnalysis.flowTrend }}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 拥堵分析 -->\n      <div class=\"analysis-section\">\n        <h3>拥堵分析</h3>\n        <div class=\"congestion-analysis\">\n          <div class=\"congestion-heatmap\">\n            <div ref=\"heatmapChart\" class=\"heatmap-chart\"></div>\n          </div>\n          <div class=\"congestion-insights\">\n            <el-alert\n              :title=\"`当前拥堵等级: ${reportData.summary?.congestionLevel || '畅通'}`\"\n              :type=\"getCongestionAlertType()\"\n              :description=\"reportData.intelligentAnalysis?.congestionDescription || '交通状况良好，建议保持当前管理策略'\"\n              show-icon\n              :closable=\"false\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 优化建议 -->\n    <div class=\"optimization-recommendations\">\n      <h2>优化建议</h2>\n      <div class=\"recommendations-list\">\n        <div \n          v-for=\"(recommendation, index) in reportData.recommendations\" \n          :key=\"index\"\n          class=\"recommendation-item\"\n          :class=\"recommendation.priority\"\n        >\n          <div class=\"recommendation-header\">\n            <el-icon>\n              <component :is=\"getRecommendationIcon(recommendation.type)\" />\n            </el-icon>\n            <h4>{{ recommendation.title }}</h4>\n            <el-tag :type=\"getPriorityType(recommendation.priority)\" size=\"small\">\n              {{ getPriorityText(recommendation.priority) }}\n            </el-tag>\n          </div>\n          <p class=\"recommendation-description\">{{ recommendation.description }}</p>\n          <div class=\"recommendation-impact\">\n            <span class=\"impact-label\">预期效果:</span>\n            <span class=\"impact-value\">{{ recommendation.expectedImprovement }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 技术指标 -->\n    <div class=\"technical-metrics\">\n      <h2>技术指标</h2>\n      <div class=\"metrics-grid\">\n        <div class=\"metric-card\">\n          <h4>检测精度</h4>\n          <div class=\"metric-value\">{{ reportData.technicalMetrics.accuracy }}%</div>\n          <el-progress :percentage=\"reportData.technicalMetrics.accuracy\" :show-text=\"false\" />\n        </div>\n        <div class=\"metric-card\">\n          <h4>处理速度</h4>\n          <div class=\"metric-value\">{{ reportData.technicalMetrics.processingSpeed }} FPS</div>\n          <el-progress :percentage=\"getSpeedPercentage()\" :show-text=\"false\" />\n        </div>\n        <div class=\"metric-card\">\n          <h4>系统稳定性</h4>\n          <div class=\"metric-value\">{{ reportData.technicalMetrics.stability }}%</div>\n          <el-progress :percentage=\"reportData.technicalMetrics.stability\" :show-text=\"false\" />\n        </div>\n        <div class=\"metric-card\">\n          <h4>数据完整性</h4>\n          <div class=\"metric-value\">{{ reportData.technicalMetrics.dataIntegrity }}%</div>\n          <el-progress :percentage=\"reportData.technicalMetrics.dataIntegrity\" :show-text=\"false\" />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, nextTick } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport {\n  DataAnalysis, Download, Refresh, Top, Bottom, \n  ArrowLeft as Back, Right, Setting, Warning, \n  CircleCheck, InfoFilled\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'IntelligentTrafficReport',\n  components: {\n    DataAnalysis, Download, Refresh, Top, Bottom, \n    Back, Right, Setting, Warning, CircleCheck, InfoFilled\n  },\n  props: {\n    taskId: {\n      type: String,\n      required: true\n    },\n    reportData: {\n      type: Object,\n      default: () => ({\n        generatedAt: new Date(),\n        analysisType: '四方向智能分析',\n        summary: {\n          totalVehicles: 0,\n          vehicleIncrease: 0,\n          processingDuration: 0,\n          efficiency: 0,\n          peakDirection: '未知',\n          peakPercentage: 0,\n          congestionLevel: '畅通',\n          congestionTrend: '稳定'\n        },\n        directions: {},\n        intelligentAnalysis: {},\n        recommendations: [],\n        technicalMetrics: {}\n      })\n    }\n  },\n  emits: ['export-report', 'refresh-data'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const exporting = ref(false)\n    const refreshing = ref(false)\n\n    // 图表引用\n    const flowChart = ref(null)\n    const heatmapChart = ref(null)\n\n    // 数据验证和默认值处理\n    const safeReportData = computed(() => {\n      const data = props.reportData || {}\n      return {\n        taskId: data.taskId || 'unknown',\n        generatedAt: data.generatedAt || new Date(),\n        analysisType: data.analysisType || '四方向智能分析',\n        summary: {\n          totalVehicles: data.summary?.totalVehicles || 0,\n          vehicleIncrease: data.summary?.vehicleIncrease || 0,\n          processingDuration: data.summary?.processingDuration || 0,\n          efficiency: data.summary?.efficiency || 0,\n          peakDirection: data.summary?.peakDirection || '未知',\n          peakPercentage: data.summary?.peakPercentage || 0,\n          congestionLevel: data.summary?.congestionLevel || '畅通',\n          congestionTrend: data.summary?.congestionTrend || '稳定'\n        },\n        directions: data.directions || {},\n        intelligentAnalysis: {\n          flowBalance: data.intelligentAnalysis?.flowBalance || 75,\n          peakHours: data.intelligentAnalysis?.peakHours || '08:00-09:00, 17:00-18:00',\n          flowTrend: data.intelligentAnalysis?.flowTrend || '稳定',\n          congestionPrediction: data.intelligentAnalysis?.congestionPrediction || '低风险',\n          congestionDescription: data.intelligentAnalysis?.congestionDescription || '交通状况良好',\n          signalOptimization: data.intelligentAnalysis?.signalOptimization || {\n            recommendedCycle: 120,\n            greenTimeAllocation: { east: 30, south: 30, west: 30, north: 30 },\n            expectedImprovement: '通行效率提升15%'\n          }\n        },\n        recommendations: Array.isArray(data.recommendations) ? data.recommendations : [],\n        technicalMetrics: {\n          accuracy: data.technicalMetrics?.accuracy || 95.5,\n          processingSpeed: data.technicalMetrics?.processingSpeed || 25.0,\n          stability: data.technicalMetrics?.stability || 98.2,\n          dataIntegrity: data.technicalMetrics?.dataIntegrity || 99.1,\n          responseTime: data.technicalMetrics?.responseTime || 150,\n          memoryUsage: data.technicalMetrics?.memoryUsage || 65.3,\n          cpuUsage: data.technicalMetrics?.cpuUsage || 45.8\n        }\n      }\n    })\n    \n    // 方法\n    const formatDate = (date) => {\n      return new Date(date).toLocaleString('zh-CN')\n    }\n    \n    const formatDuration = (seconds) => {\n      const minutes = Math.floor(seconds / 60)\n      const remainingSeconds = seconds % 60\n      return `${minutes}分${remainingSeconds}秒`\n    }\n    \n    const getDirectionIcon = (direction) => {\n      const icons = {\n        east: Right,\n        south: Bottom,\n        west: Back,\n        north: Top\n      }\n      return icons[direction] || InfoFilled\n    }\n    \n    const getDirectionName = (direction) => {\n      const names = {\n        east: '东向',\n        south: '南向',\n        west: '西向',\n        north: '北向'\n      }\n      return names[direction] || direction\n    }\n    \n    const getDirectionStatusType = (status) => {\n      const types = {\n        'completed': 'success',\n        'processing': 'warning',\n        'error': 'danger',\n        'waiting': 'info'\n      }\n      return types[status] || 'info'\n    }\n    \n    const getCongestionClass = () => {\n      const level = props.reportData.summary.congestionLevel\n      if (level.includes('严重')) return 'negative'\n      if (level.includes('中度')) return 'warning'\n      if (level.includes('轻度')) return 'caution'\n      return 'positive'\n    }\n    \n    const getCongestionIndexClass = (index) => {\n      if (index >= 0.8) return 'high-congestion'\n      if (index >= 0.5) return 'medium-congestion'\n      if (index >= 0.3) return 'low-congestion'\n      return 'no-congestion'\n    }\n    \n    const getCongestionAlertType = () => {\n      const level = props.reportData.summary?.congestionLevel || props.reportData.intelligentAnalysis?.congestionLevel\n      if (level?.includes('重度')) return 'error'\n      if (level?.includes('中度')) return 'warning'\n      if (level?.includes('轻度')) return 'info'\n      return 'success'\n    }\n    \n    const getRecommendationIcon = (type) => {\n      const icons = {\n        'signal': Setting,\n        'infrastructure': Warning,\n        'management': CircleCheck,\n        'technology': InfoFilled\n      }\n      return icons[type] || InfoFilled\n    }\n    \n    const getPriorityType = (priority) => {\n      const types = {\n        'high': 'danger',\n        'medium': 'warning',\n        'low': 'info'\n      }\n      return types[priority] || 'info'\n    }\n    \n    const getPriorityText = (priority) => {\n      const texts = {\n        'high': '高优先级',\n        'medium': '中优先级',\n        'low': '低优先级'\n      }\n      return texts[priority] || '未知'\n    }\n    \n    const getSpeedPercentage = () => {\n      const speed = props.reportData.technicalMetrics.processingSpeed || 0\n      return Math.min(100, (speed / 30) * 100) // 假设30 FPS为满分\n    }\n    \n    const exportReport = async () => {\n      exporting.value = true\n      try {\n        emit('export-report', props.taskId)\n        ElMessage.success('报告导出成功')\n      } catch (error) {\n        ElMessage.error('报告导出失败: ' + error.message)\n      } finally {\n        exporting.value = false\n      }\n    }\n    \n    const refreshData = async () => {\n      refreshing.value = true\n      try {\n        emit('refresh-data', props.taskId)\n        ElMessage.success('数据刷新成功')\n      } catch (error) {\n        ElMessage.error('数据刷新失败: ' + error.message)\n      } finally {\n        refreshing.value = false\n      }\n    }\n    \n    // 初始化图表\n    const initializeCharts = () => {\n      nextTick(() => {\n        // 这里可以集成 ECharts 或其他图表库\n        console.log('初始化图表...')\n      })\n    }\n    \n    onMounted(() => {\n      initializeCharts()\n    })\n    \n    return {\n      // 响应式数据\n      exporting,\n      refreshing,\n      flowChart,\n      heatmapChart,\n      \n      // 方法\n      formatDate,\n      formatDuration,\n      getDirectionIcon,\n      getDirectionName,\n      getDirectionStatusType,\n      getCongestionClass,\n      getCongestionIndexClass,\n      getCongestionAlertType,\n      getRecommendationIcon,\n      getPriorityType,\n      getPriorityText,\n      getSpeedPercentage,\n      exportReport,\n      refreshData\n    }\n  }\n}\n</script>\n\n<style scoped>\n.intelligent-traffic-report {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 24px;\n  background: #ffffff;\n}\n\n/* 报告头部 */\n.report-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 32px;\n  padding-bottom: 24px;\n  border-bottom: 2px solid #e5e7eb;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.report-title {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 28px;\n  font-weight: 700;\n  color: #1f2937;\n  margin: 0 0 12px 0;\n}\n\n.report-meta {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.header-actions {\n  display: flex;\n  gap: 12px;\n}\n\n/* 执行摘要 */\n.executive-summary {\n  margin-bottom: 32px;\n}\n\n.executive-summary h2 {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16px;\n}\n\n.summary-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 16px;\n}\n\n.summary-card {\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 12px;\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  transition: all 0.3s ease;\n}\n\n.summary-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.summary-card.highlight {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n}\n\n.card-icon {\n  font-size: 32px;\n  width: 60px;\n  height: 60px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n}\n\n.summary-card:not(.highlight) .card-icon {\n  background: #f3f4f6;\n}\n\n.card-content {\n  flex: 1;\n}\n\n.card-value {\n  font-size: 24px;\n  font-weight: 700;\n  line-height: 1.2;\n  margin-bottom: 4px;\n}\n\n.card-label {\n  font-size: 14px;\n  opacity: 0.8;\n  margin-bottom: 4px;\n}\n\n.card-change {\n  font-size: 12px;\n  font-weight: 500;\n  padding: 2px 8px;\n  border-radius: 4px;\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.summary-card:not(.highlight) .card-change {\n  background: #f3f4f6;\n  color: #6b7280;\n}\n\n.card-change.positive {\n  background: #dcfce7;\n  color: #16a34a;\n}\n\n.card-change.negative {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.card-change.warning {\n  background: #fef3c7;\n  color: #d97706;\n}\n\n/* 方向分析 */\n.direction-analysis {\n  margin-bottom: 32px;\n}\n\n.direction-analysis h2 {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16px;\n}\n\n.direction-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.direction-card {\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 12px;\n  padding: 20px;\n  transition: all 0.3s ease;\n}\n\n.direction-card:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.direction-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 16px;\n  padding-bottom: 12px;\n  border-bottom: 1px solid #f3f4f6;\n}\n\n.direction-header h3 {\n  flex: 1;\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.direction-stats {\n  margin-bottom: 16px;\n}\n\n.stat-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #f9fafb;\n}\n\n.stat-row:last-child {\n  border-bottom: none;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #6b7280;\n}\n\n.stat-value {\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.stat-value.high-congestion {\n  color: #dc2626;\n}\n\n.stat-value.medium-congestion {\n  color: #d97706;\n}\n\n.stat-value.low-congestion {\n  color: #059669;\n}\n\n.stat-value.no-congestion {\n  color: #10b981;\n}\n\n.direction-chart {\n  height: 120px;\n  background: #f9fafb;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #6b7280;\n}\n\n.mini-chart {\n  width: 100%;\n  height: 100%;\n}\n\n/* 智能分析 */\n.intelligent-analysis {\n  margin-bottom: 32px;\n}\n\n.intelligent-analysis h2 {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16px;\n}\n\n.analysis-section {\n  margin-bottom: 24px;\n  background: #f9fafb;\n  border-radius: 12px;\n  padding: 20px;\n}\n\n.analysis-section h3 {\n  font-size: 16px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16px;\n}\n\n.flow-analysis {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 20px;\n  align-items: start;\n}\n\n.flow-chart-container {\n  background: white;\n  border-radius: 8px;\n  padding: 16px;\n  height: 300px;\n}\n\n.flow-chart {\n  width: 100%;\n  height: 100%;\n  background: #f3f4f6;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #6b7280;\n}\n\n.flow-insights {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.insight-item {\n  background: white;\n  padding: 16px;\n  border-radius: 8px;\n  border: 1px solid #e5e7eb;\n}\n\n.insight-item strong {\n  display: block;\n  margin-bottom: 8px;\n  color: #1f2937;\n}\n\n.congestion-analysis {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  align-items: start;\n}\n\n.congestion-heatmap {\n  background: white;\n  border-radius: 8px;\n  padding: 16px;\n  height: 250px;\n}\n\n.heatmap-chart {\n  width: 100%;\n  height: 100%;\n  background: #f3f4f6;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #6b7280;\n}\n\n.congestion-insights {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n/* 优化建议 */\n.optimization-recommendations {\n  margin-bottom: 32px;\n}\n\n.optimization-recommendations h2 {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16px;\n}\n\n.recommendations-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.recommendation-item {\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 12px;\n  padding: 20px;\n  transition: all 0.3s ease;\n}\n\n.recommendation-item:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.recommendation-item.high {\n  border-left: 4px solid #dc2626;\n}\n\n.recommendation-item.medium {\n  border-left: 4px solid #d97706;\n}\n\n.recommendation-item.low {\n  border-left: 4px solid #059669;\n}\n\n.recommendation-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 12px;\n}\n\n.recommendation-header h4 {\n  flex: 1;\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.recommendation-description {\n  color: #6b7280;\n  line-height: 1.6;\n  margin-bottom: 12px;\n}\n\n.recommendation-impact {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.impact-label {\n  font-size: 14px;\n  color: #6b7280;\n}\n\n.impact-value {\n  font-weight: 600;\n  color: #059669;\n}\n\n/* 技术指标 */\n.technical-metrics h2 {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16px;\n}\n\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n}\n\n.metric-card {\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 12px;\n  padding: 20px;\n  text-align: center;\n  transition: all 0.3s ease;\n}\n\n.metric-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.metric-card h4 {\n  margin: 0 0 12px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #6b7280;\n}\n\n.metric-value {\n  font-size: 24px;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 12px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .intelligent-traffic-report {\n    padding: 16px;\n  }\n\n  .report-header {\n    flex-direction: column;\n    gap: 16px;\n  }\n\n  .summary-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .direction-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .flow-analysis {\n    grid-template-columns: 1fr;\n  }\n\n  .congestion-analysis {\n    grid-template-columns: 1fr;\n  }\n\n  .metrics-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA4B;;EAEhCA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAgB;;EACrBA,KAAK,EAAC;AAAc;;EAInBA,KAAK,EAAC;AAAa;;EAMrBA,KAAK,EAAC;AAAgB;;EAaxBA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAwB;;EAE5BA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAElBA,KAAK,EAAC;AAAsB;;EAIhCA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAElBA,KAAK,EAAC;AAAa;;EAIvBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAElBA,KAAK,EAAC;AAAa;;EAIvBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAW1BA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAgB;;EAMlBA,KAAK,EAAC;AAAkB;;EAUxBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAU;;EAQlBA,KAAK,EAAC;AAAiB;;EAQ7BA,KAAK,EAAC;AAAsB;;EAI1BA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAsB;;EAC1BC,GAAG,EAAC,WAAW;EAACD,KAAK,EAAC;;;EAExBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAGpBA,KAAK,EAAC;AAAc;;EAQ1BA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAoB;;EACxBC,GAAG,EAAC,cAAc;EAACD,KAAK,EAAC;;;EAE3BA,KAAK,EAAC;AAAqB;;EAcjCA,KAAK,EAAC;AAA8B;;EAElCA,KAAK,EAAC;AAAsB;;EAOxBA,KAAK,EAAC;AAAuB;;EAS/BA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAuB;;EAE1BA,KAAK,EAAC;AAAc;;EAO7BA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAc;;EAGtBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAc;;EAGtBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAc;;EAGtBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAc;;;;;;;;;;uBApNjCE,mBAAA,CAyNM,OAzNNC,UAyNM,GAxNJC,mBAAA,UAAa,EACbC,mBAAA,CAsBM,OAtBNC,UAsBM,GArBJD,mBAAA,CAUM,OAVNE,UAUM,GATJF,mBAAA,CAGK,MAHLG,UAGK,GAFHC,YAAA,CAAmCC,kBAAA;IAN7CC,OAAA,EAAAC,QAAA,CAMmB,MAAgB,CAAhBH,YAAA,CAAgBI,uBAAA,E;IANnCC,CAAA;gCAAAC,gBAAA,CAM6C,eAErC,G,GACAV,mBAAA,CAIM,OAJNW,UAIM,GAHJP,YAAA,CAA+CQ,iBAAA;IAAvCC,IAAI,EAAC;EAAM;IAV7BP,OAAA,EAAAC,QAAA,CAU8B,MAAM,CAVpCG,gBAAA,CAU8B,QAAM,GAAAI,gBAAA,CAAGC,MAAA,CAAAC,MAAM,iB;IAV7CP,CAAA;MAWUL,YAAA,CAA4EQ,iBAAA;IAApEC,IAAI,EAAC;EAAS;IAXhCP,OAAA,EAAAC,QAAA,CAWiC,MAA4C,CAX7EG,gBAAA,CAAAI,gBAAA,CAWoCG,MAAA,CAAAC,UAAU,CAACC,IAAA,CAAAC,cAAc,CAACC,WAAW,kB;IAXzEZ,CAAA;MAYUL,YAAA,CAAkDQ,iBAAA;IAZ5DN,OAAA,EAAAC,QAAA,CAYkB,MAAiC,CAZnDG,gBAAA,CAAAI,gBAAA,CAYqBK,IAAA,CAAAC,cAAc,CAACE,YAAY,iB;IAZhDb,CAAA;UAeMT,mBAAA,CASM,OATNuB,UASM,GARJnB,YAAA,CAGYoB,oBAAA;IAHDX,IAAI,EAAC,SAAS;IAAEY,OAAK,EAAER,MAAA,CAAAS,YAAY;IAAGC,OAAO,EAAEV,MAAA,CAAAW;;IAhBlEtB,OAAA,EAAAC,QAAA,CAiBU,MAA+B,CAA/BH,YAAA,CAA+BC,kBAAA;MAjBzCC,OAAA,EAAAC,QAAA,CAiBmB,MAAY,CAAZH,YAAA,CAAYyB,mBAAA,E;MAjB/BpB,CAAA;kCAAAC,gBAAA,CAiByC,QAEjC,G;IAnBRD,CAAA;6CAoBQL,YAAA,CAGYoB,oBAAA;IAHAC,OAAK,EAAER,MAAA,CAAAa,WAAW;IAAGH,OAAO,EAAEV,MAAA,CAAAc;;IApBlDzB,OAAA,EAAAC,QAAA,CAqBU,MAA8B,CAA9BH,YAAA,CAA8BC,kBAAA;MArBxCC,OAAA,EAAAC,QAAA,CAqBmB,MAAW,CAAXH,YAAA,CAAW4B,kBAAA,E;MArB9BvB,CAAA;kCAAAC,gBAAA,CAqBwC,QAEhC,G;IAvBRD,CAAA;iDA2BIV,mBAAA,UAAa,EACbC,mBAAA,CAyCM,OAzCNiC,UAyCM,G,4BAxCJjC,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAsCM,OAtCNkC,UAsCM,GArCJlC,mBAAA,CAOM,OAPNmC,UAOM,G,0BANJnC,mBAAA,CAA+B;IAA1BL,KAAK,EAAC;EAAW,GAAC,IAAE,sBACzBK,mBAAA,CAIM,OAJNoC,WAIM,GAHJpC,mBAAA,CAAwE,OAAxEqC,WAAwE,EAAAvB,gBAAA,CAA7CK,IAAA,CAAAC,cAAc,CAACkB,OAAO,CAACC,aAAa,kB,0BAC/DvC,mBAAA,CAAmC;IAA9BL,KAAK,EAAC;EAAY,GAAC,OAAK,sBAC7BK,mBAAA,CAAsF,OAAtFwC,WAAsF,EAApD,GAAC,GAAA1B,gBAAA,CAAGK,IAAA,CAAAC,cAAc,CAACkB,OAAO,CAACG,eAAe,IAAG,GAAC,gB,KAIpFzC,mBAAA,CAOM,OAPN0C,WAOM,G,0BANJ1C,mBAAA,CAA+B;IAA1BL,KAAK,EAAC;EAAW,GAAC,IAAE,sBACzBK,mBAAA,CAIM,OAJN2C,WAIM,GAHJ3C,mBAAA,CAAyF,OAAzF4C,WAAyF,EAAA9B,gBAAA,CAA9DG,MAAA,CAAA4B,cAAc,CAAC9B,MAAA,CAAA+B,UAAU,CAACR,OAAO,CAACS,kBAAkB,mB,0BAC/E/C,mBAAA,CAAkC;IAA7BL,KAAK,EAAC;EAAY,GAAC,MAAI,sBAC5BK,mBAAA,CAAsE,OAAtEgD,WAAsE,EAAAlC,gBAAA,CAA1CC,MAAA,CAAA+B,UAAU,CAACR,OAAO,CAACW,UAAU,IAAG,MAAI,gB,KAIpEjD,mBAAA,CAOM,OAPNkD,WAOM,G,0BANJlD,mBAAA,CAA+B;IAA1BL,KAAK,EAAC;EAAW,GAAC,IAAE,sBACzBK,mBAAA,CAIM,OAJNmD,WAIM,GAHJnD,mBAAA,CAAoE,OAApEoD,WAAoE,EAAAtC,gBAAA,CAAzCC,MAAA,CAAA+B,UAAU,CAACR,OAAO,CAACe,aAAa,kB,0BAC3DrD,mBAAA,CAAmC;IAA9BL,KAAK,EAAC;EAAY,GAAC,OAAK,sBAC7BK,mBAAA,CAA0E,OAA1EsD,WAA0E,EAAAxC,gBAAA,CAA9CC,MAAA,CAAA+B,UAAU,CAACR,OAAO,CAACiB,cAAc,IAAG,MAAI,gB,KAIxEvD,mBAAA,CASM,OATNwD,WASM,G,4BARJxD,mBAAA,CAA+B;IAA1BL,KAAK,EAAC;EAAW,GAAC,IAAE,sBACzBK,mBAAA,CAMM,OANNyD,WAMM,GALJzD,mBAAA,CAAsE,OAAtE0D,WAAsE,EAAA5C,gBAAA,CAA3CC,MAAA,CAAA+B,UAAU,CAACR,OAAO,CAACqB,eAAe,kB,0BAC7D3D,mBAAA,CAAkC;IAA7BL,KAAK,EAAC;EAAY,GAAC,MAAI,sBAC5BK,mBAAA,CAEM;IAFDL,KAAK,EA/DtBiE,eAAA,EA+DuB,aAAa,EAAS3C,MAAA,CAAA4C,kBAAkB;sBAC9C9C,MAAA,CAAA+B,UAAU,CAACR,OAAO,CAACwB,eAAe,wB,SAO/C/D,mBAAA,YAAe,EACfC,mBAAA,CA4CM,OA5CN+D,WA4CM,G,4BA3CJ/D,mBAAA,CAAe,YAAX,QAAM,sBACVA,mBAAA,CAyCM,OAzCNgE,WAyCM,I,kBAxCJnE,mBAAA,CAuCMoE,SAAA,QAlHdC,WAAA,CA4EsCnD,MAAA,CAAA+B,UAAU,CAACqB,UAAU,EA5E3D,CA4EkBC,IAAI,EAAEC,SAAS;yBADzBxE,mBAAA,CAuCM;MArCHyE,GAAG,EAAED,SAAS;MACf1E,KAAK,EAAC;QAENK,mBAAA,CAQM,OARNuE,WAQM,GAPJnE,YAAA,CAEUC,kBAAA;MAnFtBC,OAAA,EAAAC,QAAA,CAkFc,MAA+C,E,cAA/CiE,YAAA,CAA+CC,wBAlF7D,CAkF8BxD,MAAA,CAAAyD,gBAAgB,CAACL,SAAS,K;MAlFxD5D,CAAA;kCAoFYT,mBAAA,CAA0C,YAAAc,gBAAA,CAAnCG,MAAA,CAAA0D,gBAAgB,CAACN,SAAS,mBACjCjE,YAAA,CAESQ,iBAAA;MAFAC,IAAI,EAAEI,MAAA,CAAA2D,sBAAsB,CAACR,IAAI,CAACS,MAAM;;MArF7DvE,OAAA,EAAAC,QAAA,CAsFc,MAAiB,CAtF/BG,gBAAA,CAAAI,gBAAA,CAsFiBsD,IAAI,CAACS,MAAM,iB;MAtF5BpE,CAAA;qDA0FUT,mBAAA,CAmBM,OAnBN8E,WAmBM,GAlBJ9E,mBAAA,CAGM,OAHN+E,WAGM,G,4BAFJ/E,mBAAA,CAAqC;MAA/BL,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC9BK,mBAAA,CAA4D,QAA5DgF,WAA4D,EAAAlE,gBAAA,CAAhCsD,IAAI,CAACa,YAAY,sB,GAE/CjF,mBAAA,CAGM,OAHNkF,WAGM,G,4BAFJlF,mBAAA,CAAqC;MAA/BL,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC9BK,mBAAA,CAAiE,QAAjEmF,WAAiE,EAAArE,gBAAA,CAArCsD,IAAI,CAACgB,YAAY,SAAQ,OAAK,gB,GAE5DpF,mBAAA,CAGM,OAHNqF,WAGM,G,4BAFJrF,mBAAA,CAAqC;MAA/BL,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC9BK,mBAAA,CAAuF,QAAvFsF,WAAuF,EAAAxE,gBAAA,CAA3DsD,IAAI,CAACmB,OAAO,IAAInB,IAAI,CAACoB,kBAAkB,SAAQ,OAAK,gB,GAElFxF,mBAAA,CAKM,OALNyF,WAKM,G,4BAJJzF,mBAAA,CAAqC;MAA/BL,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC9BK,mBAAA,CAEO;MAFDL,KAAK,EAzGzBiE,eAAA,EAyG0B,YAAY,EAAS3C,MAAA,CAAAyE,uBAAuB,CAACtB,IAAI,CAACuB,eAAe;wBACxEvB,IAAI,CAACwB,UAAU,IAAIxB,IAAI,CAACT,eAAe,gC,KAKhD3D,mBAAA,CAEM,OAFN6F,WAEM,GADJ7F,mBAAA,CAA0D;MAhHtE8F,OAAA;MAgHkBlG,GAAG,WAAWyE,SAAS;MAAI1E,KAAK,EAAC;;sCAM/CI,mBAAA,YAAe,EACfC,mBAAA,CA2CM,OA3CN+F,WA2CM,G,4BA1CJ/F,mBAAA,CAAe,YAAX,QAAM,sBAEVD,mBAAA,YAAe,EACfC,mBAAA,CAmBM,OAnBNgG,WAmBM,G,4BAlBJhG,mBAAA,CAAe,YAAX,QAAM,sBACVA,mBAAA,CAgBM,OAhBNiG,WAgBM,GAfJjG,mBAAA,CAEM,OAFNkG,WAEM,GADJlG,mBAAA,CAA8C,OAA9CmG,WAA8C,8B,GAEhDnG,mBAAA,CAWM,OAXNoG,WAWM,GAVJpG,mBAAA,CAGM,OAHNqG,WAGM,G,4BAFJrG,mBAAA,CAAuB,gBAAf,QAAM,sBAnI5BU,gBAAA,CAmIqC,GAAC,GAAAI,gBAAA,CAAGC,MAAA,CAAA+B,UAAU,CAACwD,mBAAmB,CAACC,WAAW,IAAG,IACxE,iBAAAnG,YAAA,CAA2FoG,sBAAA;IAA7EC,UAAU,EAAE1F,MAAA,CAAA+B,UAAU,CAACwD,mBAAmB,CAACC,WAAW;IAAG,WAAS,EAAE;6CAEpFvG,mBAAA,CAEM,OAFN0G,WAEM,G,4BADJ1G,mBAAA,CAAsB,gBAAd,OAAK,sBAvI3BU,gBAAA,CAuIoC,GAAC,GAAAI,gBAAA,CAAGC,MAAA,CAAA+B,UAAU,CAACwD,mBAAmB,CAACK,SAAS,iB,GAEpE3G,mBAAA,CAEM,OAFN4G,WAEM,G,4BADJ5G,mBAAA,CAAsB,gBAAd,OAAK,sBA1I3BU,gBAAA,CA0IoC,GAAC,GAAAI,gBAAA,CAAGC,MAAA,CAAA+B,UAAU,CAACwD,mBAAmB,CAACO,SAAS,iB,SAM1E9G,mBAAA,UAAa,EACbC,mBAAA,CAgBM,OAhBN8G,WAgBM,G,4BAfJ9G,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAaM,OAbN+G,WAaM,GAZJ/G,mBAAA,CAEM,OAFNgH,WAEM,GADJhH,mBAAA,CAAoD,OAApDiH,WAAoD,8B,GAEtDjH,mBAAA,CAQM,OARNkH,WAQM,GAPJ9G,YAAA,CAME+G,mBAAA;IALCC,KAAK,aAAarG,MAAA,CAAA+B,UAAU,CAACR,OAAO,EAAEqB,eAAe;IACrD9C,IAAI,EAAEI,MAAA,CAAAoG,sBAAsB;IAC5BC,WAAW,EAAEvG,MAAA,CAAA+B,UAAU,CAACwD,mBAAmB,EAAEiB,qBAAqB;IACnE,WAAS,EAAT,EAAS;IACRC,QAAQ,EAAE;qEAOrBzH,mBAAA,UAAa,EACbC,mBAAA,CAyBM,OAzBNyH,WAyBM,G,4BAxBJzH,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAsBM,OAtBN0H,WAsBM,I,kBArBJ7H,mBAAA,CAoBMoE,SAAA,QA5LdC,WAAA,CAyK4CnD,MAAA,CAAA+B,UAAU,CAAC6E,eAAe,EAzKtE,CAyKkBC,cAAc,EAAEC,KAAK;yBAD/BhI,mBAAA,CAoBM;MAlBHyE,GAAG,EAAEuD,KAAK;MACXlI,KAAK,EA3KfiE,eAAA,EA2KgB,qBAAqB,EACnBgE,cAAc,CAACE,QAAQ;QAE/B9H,mBAAA,CAQM,OARN+H,WAQM,GAPJ3H,YAAA,CAEUC,kBAAA;MAjLtBC,OAAA,EAAAC,QAAA,CAgLc,MAA8D,E,cAA9DiE,YAAA,CAA8DC,wBAhL5E,CAgL8BxD,MAAA,CAAA+G,qBAAqB,CAACJ,cAAc,CAAC/G,IAAI,K;MAhLvEJ,CAAA;kCAkLYT,mBAAA,CAAmC,YAAAc,gBAAA,CAA5B8G,cAAc,CAACR,KAAK,kBAC3BhH,YAAA,CAESQ,iBAAA;MAFAC,IAAI,EAAEI,MAAA,CAAAgH,eAAe,CAACL,cAAc,CAACE,QAAQ;MAAGI,IAAI,EAAC;;MAnL1E5H,OAAA,EAAAC,QAAA,CAoLc,MAA8C,CApL5DG,gBAAA,CAAAI,gBAAA,CAoLiBG,MAAA,CAAAkH,eAAe,CAACP,cAAc,CAACE,QAAQ,kB;MApLxDrH,CAAA;qDAuLUT,mBAAA,CAA0E,KAA1EoI,WAA0E,EAAAtH,gBAAA,CAAjC8G,cAAc,CAACN,WAAW,kBACnEtH,mBAAA,CAGM,OAHNqI,WAGM,G,4BAFJrI,mBAAA,CAAuC;MAAjCL,KAAK,EAAC;IAAc,GAAC,OAAK,sBAChCK,mBAAA,CAA0E,QAA1EsI,WAA0E,EAAAxH,gBAAA,CAA5C8G,cAAc,CAACW,mBAAmB,iB;sCAMxExI,mBAAA,UAAa,EACbC,mBAAA,CAwBM,OAxBNwI,WAwBM,G,4BAvBJxI,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAqBM,OArBNyI,WAqBM,GApBJzI,mBAAA,CAIM,OAJN0I,WAIM,G,4BAHJ1I,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAA2E,OAA3E2I,WAA2E,EAAA7H,gBAAA,CAA9CC,MAAA,CAAA+B,UAAU,CAAC8F,gBAAgB,CAACC,QAAQ,IAAG,GAAC,iBACrEzI,YAAA,CAAqFoG,sBAAA;IAAvEC,UAAU,EAAE1F,MAAA,CAAA+B,UAAU,CAAC8F,gBAAgB,CAACC,QAAQ;IAAG,WAAS,EAAE;6CAE9E7I,mBAAA,CAIM,OAJN8I,WAIM,G,4BAHJ9I,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAAqF,OAArF+I,WAAqF,EAAAjI,gBAAA,CAAxDC,MAAA,CAAA+B,UAAU,CAAC8F,gBAAgB,CAACI,eAAe,IAAG,MAAI,iBAC/E5I,YAAA,CAAqEoG,sBAAA;IAAvDC,UAAU,EAAExF,MAAA,CAAAgI,kBAAkB;IAAK,WAAS,EAAE;6CAE9DjJ,mBAAA,CAIM,OAJNkJ,WAIM,G,4BAHJlJ,mBAAA,CAAc,YAAV,OAAK,sBACTA,mBAAA,CAA4E,OAA5EmJ,WAA4E,EAAArI,gBAAA,CAA/CC,MAAA,CAAA+B,UAAU,CAAC8F,gBAAgB,CAACQ,SAAS,IAAG,GAAC,iBACtEhJ,YAAA,CAAsFoG,sBAAA;IAAxEC,UAAU,EAAE1F,MAAA,CAAA+B,UAAU,CAAC8F,gBAAgB,CAACQ,SAAS;IAAG,WAAS,EAAE;6CAE/EpJ,mBAAA,CAIM,OAJNqJ,WAIM,G,4BAHJrJ,mBAAA,CAAc,YAAV,OAAK,sBACTA,mBAAA,CAAgF,OAAhFsJ,WAAgF,EAAAxI,gBAAA,CAAnDC,MAAA,CAAA+B,UAAU,CAAC8F,gBAAgB,CAACW,aAAa,IAAG,GAAC,iBAC1EnJ,YAAA,CAA0FoG,sBAAA;IAA5EC,UAAU,EAAE1F,MAAA,CAAA+B,UAAU,CAAC8F,gBAAgB,CAACW,aAAa;IAAG,WAAS,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}