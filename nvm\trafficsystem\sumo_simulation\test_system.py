#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统测试脚本
验证SUMO + TraCI动态红绿灯决策系统是否正常工作
"""

import os
import sys
import logging

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        import traci
        print("✓ TraCI导入成功")
        return True
    except ImportError as e:
        print(f"✗ TraCI导入失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构"""
    print("\n测试目录结构...")
    
    required_dirs = [
        'config',
        'algorithms',
        'controllers',
        'analysis',
        'results'
    ]
    
    all_exist = True
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✓ {directory}/ 目录存在")
        else:
            print(f"✗ {directory}/ 目录不存在")
            all_exist = False
    
    return all_exist

def run_basic_tests():
    """运行基础测试"""
    print("="*60)
    print("SUMO + TraCI 动态红绿灯决策系统基础测试")
    print("="*60)
    
    tests = [
        ("模块导入", test_imports),
        ("目录结构", test_directory_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}测试出现异常: {e}")
    
    print("\n" + "="*60)
    print(f"基础测试结果: {passed}/{total} 通过")
    print("="*60)
    
    if passed == total:
        print("🎉 基础测试通过！")
        return True
    else:
        print("❌ 部分基础测试失败。")
        return False

if __name__ == "__main__":
    success = run_basic_tests()
    sys.exit(0 if success else 1)
