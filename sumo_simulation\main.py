#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SUMO + TraCI 动态红绿灯决策系统主程序
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from typing import Dict, Any, List

# 添加模块路径
sys.path.append(os.path.dirname(__file__))

from controllers.traffic_controller import TrafficController
from controllers.data_collector import DataCollector
from analysis.performance_analyzer import PerformanceAnalyzer
from analysis.visualization import TrafficVisualization

def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('simulation.log', encoding='utf-8')
        ]
    )

def get_sumo_command(config_file: str, gui: bool = False) -> List[str]:
    """
    构建SUMO启动命令
    
    Args:
        config_file: 配置文件路径
        gui: 是否使用GUI
        
    Returns:
        SUMO命令列表
    """
    if gui:
        cmd = ["sumo-gui"]
    else:
        cmd = ["sumo"]
    
    cmd.extend([
        "-c", config_file,
        "--remote-port", "8813",
        "--start",
        "--quit-on-end"
    ])
    
    return cmd

def run_single_simulation(algorithm_type: str, config: Dict[str, Any], 
                         duration: int = 3600, gui: bool = False) -> Dict[str, Any]:
    """
    运行单个仿真
    
    Args:
        algorithm_type: 算法类型
        config: 配置参数
        duration: 仿真持续时间
        gui: 是否使用GUI
        
    Returns:
        仿真结果字典
    """
    logger = logging.getLogger("SimulationRunner")
    logger.info(f"开始运行 {algorithm_type} 算法仿真")
    
    # 创建控制器和数据采集器
    controller = TrafficController(algorithm_type, config)
    data_collector = DataCollector(f"results/{algorithm_type}")
    
    # 构建SUMO命令
    config_file = "config/simulation.sumocfg"
    sumo_cmd = get_sumo_command(config_file, gui)
    
    try:
        # 连接到SUMO
        controller.connect_to_sumo(sumo_cmd)
        
        # 运行仿真
        start_time = time.time()
        performance_data = controller.run_simulation(duration)
        end_time = time.time()
        
        # 保存性能数据
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        performance_file = f"results/{algorithm_type}/performance_{algorithm_type}_{timestamp}.json"
        controller.save_performance_data(performance_file)
        
        # 计算汇总统计
        if performance_data:
            avg_waiting_time = sum(d.get('avg_waiting_time', 0) for d in performance_data) / len(performance_data)
            avg_throughput = sum(d.get('throughput', 0) for d in performance_data) / len(performance_data)
            total_vehicles = max(d.get('vehicle_count', 0) for d in performance_data)
        else:
            avg_waiting_time = 0
            avg_throughput = 0
            total_vehicles = 0
        
        result = {
            'algorithm_type': algorithm_type,
            'simulation_duration': duration,
            'real_time_duration': end_time - start_time,
            'performance_data_file': performance_file,
            'summary_statistics': {
                'avg_waiting_time': avg_waiting_time,
                'avg_throughput': avg_throughput,
                'total_vehicles': total_vehicles,
                'data_points': len(performance_data)
            },
            'algorithm_info': controller.get_algorithm_info()
        }
        
        logger.info(f"{algorithm_type} 算法仿真完成")
        logger.info(f"平均等待时间: {avg_waiting_time:.2f}秒")
        logger.info(f"平均通行效率: {avg_throughput:.0f}车辆/小时")
        
        return result
        
    except Exception as e:
        logger.error(f"仿真运行失败: {str(e)}")
        raise
    
    finally:
        # 断开连接
        controller.disconnect_from_sumo()

def run_comparison_study(algorithms: List[str], configs: Dict[str, Dict[str, Any]], 
                        duration: int = 3600, gui: bool = False) -> Dict[str, Any]:
    """
    运行对比研究
    
    Args:
        algorithms: 算法列表
        configs: 算法配置字典
        duration: 仿真持续时间
        gui: 是否使用GUI
        
    Returns:
        对比研究结果
    """
    logger = logging.getLogger("ComparisonStudy")
    logger.info(f"开始运行对比研究，算法: {algorithms}")
    
    results = {}
    performance_files = {}
    
    # 运行每个算法的仿真
    for algorithm in algorithms:
        config = configs.get(algorithm, {})
        
        try:
            result = run_single_simulation(algorithm, config, duration, gui)
            results[algorithm] = result
            performance_files[algorithm] = result['performance_data_file']
            
            # 等待一段时间，确保SUMO完全关闭
            time.sleep(2)
            
        except Exception as e:
            logger.error(f"算法 {algorithm} 仿真失败: {str(e)}")
            continue
    
    # 进行性能分析
    if len(results) >= 2:
        logger.info("开始性能分析和可视化")
        
        # 创建分析器
        analyzer = PerformanceAnalyzer()
        
        # 加载性能数据
        for algorithm, file_path in performance_files.items():
            if os.path.exists(file_path):
                analyzer.load_performance_data(algorithm, file_path)
        
        # 生成性能报告
        performance_report = analyzer.generate_performance_report(list(results.keys()))
        
        # 保存分析结果
        import json
        report_file = f"results/comparison/performance_report_{time.strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(performance_report, f, indent=2, ensure_ascii=False)
        
        # 导出比较表格
        table_file = f"results/comparison/comparison_table_{time.strftime('%Y%m%d_%H%M%S')}.csv"
        analyzer.export_comparison_table(list(results.keys()), table_file)
        
        # 生成可视化图表
        visualizer = TrafficVisualization()
        
        # 创建图表目录
        chart_dir = f"results/comparison/charts_{time.strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(chart_dir, exist_ok=True)
        
        # 生成各种图表
        try:
            # 等待时间对比图
            waiting_time_data = {}
            for algorithm in results.keys():
                if algorithm in analyzer.data:
                    waiting_times = [d.get('avg_waiting_time', 0) for d in analyzer.data[algorithm]]
                    waiting_time_data[algorithm] = waiting_times
            
            if waiting_time_data:
                fig1 = visualizer.plot_performance_comparison(
                    waiting_time_data, '平均等待时间', 
                    title='算法等待时间对比', ylabel='等待时间 (秒)',
                    save_path=os.path.join(chart_dir, 'waiting_time_comparison.png')
                )
                plt.close(fig1)
            
            # 效率评分图
            efficiency_scores = {}
            for algorithm in results.keys():
                score = analyzer.calculate_efficiency_score(algorithm)
                efficiency_scores[algorithm] = score
            
            if efficiency_scores:
                fig2 = visualizer.plot_efficiency_scores(
                    efficiency_scores,
                    save_path=os.path.join(chart_dir, 'efficiency_scores.png')
                )
                plt.close(fig2)
            
            logger.info(f"可视化图表已保存到: {chart_dir}")
            
        except Exception as e:
            logger.warning(f"生成可视化图表时出错: {str(e)}")
        
        # 汇总结果
        comparison_result = {
            'algorithms': algorithms,
            'individual_results': results,
            'performance_report': performance_report,
            'report_file': report_file,
            'table_file': table_file,
            'chart_directory': chart_dir,
            'summary': {
                'total_simulations': len(results),
                'successful_simulations': len([r for r in results.values() if 'summary_statistics' in r]),
                'comparison_completed': True
            }
        }
        
        logger.info("对比研究完成")
        return comparison_result
    
    else:
        logger.warning("成功的仿真数量不足，无法进行对比分析")
        return {
            'algorithms': algorithms,
            'individual_results': results,
            'summary': {
                'total_simulations': len(results),
                'successful_simulations': len(results),
                'comparison_completed': False
            }
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SUMO交通信号控制仿真系统')
    parser.add_argument('--algorithm', type=str, choices=['fixed', 'adaptive', 'all'], 
                       default='all', help='算法类型')
    parser.add_argument('--duration', type=int, default=3600, help='仿真持续时间（秒）')
    parser.add_argument('--gui', action='store_true', help='使用SUMO GUI')
    parser.add_argument('--log-level', type=str, default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger("Main")
    
    # 确保结果目录存在
    os.makedirs("results", exist_ok=True)
    os.makedirs("results/comparison", exist_ok=True)
    
    # 算法配置
    configs = {
        'fixed': {
            'cycle_time': 120,
            'east_west_green': 45,
            'north_south_green': 45,
            'yellow_time': 3,
            'all_red_time': 2
        },
        'adaptive': {
            'min_green_time': 15,
            'max_green_time': 80,
            'yellow_time': 3,
            'all_red_time': 2,
            'detection_threshold': 3,
            'extension_time': 5
        }
    }
    
    try:
        if args.algorithm == 'all':
            # 运行对比研究
            algorithms = ['fixed', 'adaptive']
            result = run_comparison_study(algorithms, configs, args.duration, args.gui)
            
            print("\n" + "="*60)
            print("对比研究完成！")
            print("="*60)
            
            if result['summary']['comparison_completed']:
                print(f"报告文件: {result['report_file']}")
                print(f"比较表格: {result['table_file']}")
                print(f"图表目录: {result['chart_directory']}")
            
            # 显示简要结果
            for algorithm, res in result['individual_results'].items():
                if 'summary_statistics' in res:
                    stats = res['summary_statistics']
                    print(f"\n{algorithm.upper()} 算法:")
                    print(f"  平均等待时间: {stats['avg_waiting_time']:.2f} 秒")
                    print(f"  平均通行效率: {stats['avg_throughput']:.0f} 车辆/小时")
                    print(f"  总车辆数: {stats['total_vehicles']}")
        
        else:
            # 运行单个算法
            config = configs.get(args.algorithm, {})
            result = run_single_simulation(args.algorithm, config, args.duration, args.gui)
            
            print("\n" + "="*60)
            print(f"{args.algorithm.upper()} 算法仿真完成！")
            print("="*60)
            print(f"性能数据文件: {result['performance_data_file']}")
            
            stats = result['summary_statistics']
            print(f"平均等待时间: {stats['avg_waiting_time']:.2f} 秒")
            print(f"平均通行效率: {stats['avg_throughput']:.0f} 车辆/小时")
            print(f"总车辆数: {stats['total_vehicles']}")
    
    except KeyboardInterrupt:
        logger.info("用户中断仿真")
        print("\n仿真已被用户中断")
    
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        print(f"\n错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
