{"ast": null, "code": "import { ref, reactive, computed, onMounted, nextTick } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { DataAnalysis, Download, Refresh, Top, Bottom, ArrowLeft as Back, Right, Setting, Warning, CircleCheck, InfoFilled } from '@element-plus/icons-vue';\nexport default {\n  name: 'IntelligentTrafficReport',\n  components: {\n    DataAnalysis,\n    Download,\n    Refresh,\n    Top,\n    Bottom,\n    Back,\n    Right,\n    Setting,\n    Warning,\n    CircleCheck,\n    InfoFilled\n  },\n  props: {\n    taskId: {\n      type: String,\n      required: true\n    },\n    reportData: {\n      type: Object,\n      default: () => ({\n        generatedAt: new Date(),\n        analysisType: '四方向智能分析',\n        summary: {\n          totalVehicles: 0,\n          vehicleIncrease: 0,\n          processingDuration: 0,\n          efficiency: 0,\n          peakDirection: '未知',\n          peakPercentage: 0,\n          congestionLevel: '畅通',\n          congestionTrend: '稳定'\n        },\n        directions: {},\n        intelligentAnalysis: {},\n        recommendations: [],\n        technicalMetrics: {}\n      })\n    }\n  },\n  emits: ['export-report', 'refresh-data'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const exporting = ref(false);\n    const refreshing = ref(false);\n\n    // 图表引用\n    const flowChart = ref(null);\n    const heatmapChart = ref(null);\n\n    // 数据验证和默认值处理\n    const safeReportData = computed(() => {\n      const data = props.reportData || {};\n      return {\n        taskId: data.taskId || 'unknown',\n        generatedAt: data.generatedAt || new Date(),\n        analysisType: data.analysisType || '四方向智能分析',\n        summary: {\n          totalVehicles: data.summary?.totalVehicles || 0,\n          vehicleIncrease: data.summary?.vehicleIncrease || 0,\n          processingDuration: data.summary?.processingDuration || 0,\n          efficiency: data.summary?.efficiency || 0,\n          peakDirection: data.summary?.peakDirection || '未知',\n          peakPercentage: data.summary?.peakPercentage || 0,\n          congestionLevel: data.summary?.congestionLevel || '畅通',\n          congestionTrend: data.summary?.congestionTrend || '稳定'\n        },\n        directions: data.directions || {},\n        intelligentAnalysis: {\n          flowBalance: data.intelligentAnalysis?.flowBalance || 75,\n          peakHours: data.intelligentAnalysis?.peakHours || '08:00-09:00, 17:00-18:00',\n          flowTrend: data.intelligentAnalysis?.flowTrend || '稳定',\n          congestionPrediction: data.intelligentAnalysis?.congestionPrediction || '低风险',\n          congestionDescription: data.intelligentAnalysis?.congestionDescription || '交通状况良好',\n          signalOptimization: data.intelligentAnalysis?.signalOptimization || {\n            recommendedCycle: 120,\n            greenTimeAllocation: {\n              east: 30,\n              south: 30,\n              west: 30,\n              north: 30\n            },\n            expectedImprovement: '通行效率提升15%'\n          }\n        },\n        recommendations: Array.isArray(data.recommendations) ? data.recommendations : [],\n        technicalMetrics: {\n          accuracy: data.technicalMetrics?.accuracy || 95.5,\n          processingSpeed: data.technicalMetrics?.processingSpeed || 25.0,\n          stability: data.technicalMetrics?.stability || 98.2,\n          dataIntegrity: data.technicalMetrics?.dataIntegrity || 99.1,\n          responseTime: data.technicalMetrics?.responseTime || 150,\n          memoryUsage: data.technicalMetrics?.memoryUsage || 65.3,\n          cpuUsage: data.technicalMetrics?.cpuUsage || 45.8\n        }\n      };\n    });\n\n    // 方法\n    const formatDate = date => {\n      return new Date(date).toLocaleString('zh-CN');\n    };\n    const formatDuration = seconds => {\n      const minutes = Math.floor(seconds / 60);\n      const remainingSeconds = seconds % 60;\n      return `${minutes}分${remainingSeconds}秒`;\n    };\n    const getDirectionIcon = direction => {\n      const icons = {\n        east: Right,\n        south: Bottom,\n        west: Back,\n        north: Top\n      };\n      return icons[direction] || InfoFilled;\n    };\n    const getDirectionName = direction => {\n      const names = {\n        east: '东向',\n        south: '南向',\n        west: '西向',\n        north: '北向'\n      };\n      return names[direction] || direction;\n    };\n    const getDirectionStatusType = status => {\n      const types = {\n        'completed': 'success',\n        'processing': 'warning',\n        'error': 'danger',\n        'waiting': 'info'\n      };\n      return types[status] || 'info';\n    };\n    const getCongestionClass = () => {\n      const level = props.reportData.summary.congestionLevel;\n      if (level.includes('严重')) return 'negative';\n      if (level.includes('中度')) return 'warning';\n      if (level.includes('轻度')) return 'caution';\n      return 'positive';\n    };\n    const getCongestionIndexClass = index => {\n      if (index >= 0.8) return 'high-congestion';\n      if (index >= 0.5) return 'medium-congestion';\n      if (index >= 0.3) return 'low-congestion';\n      return 'no-congestion';\n    };\n    const getCongestionAlertType = () => {\n      const level = props.reportData.summary?.congestionLevel || props.reportData.intelligentAnalysis?.congestionLevel;\n      if (level?.includes('重度')) return 'error';\n      if (level?.includes('中度')) return 'warning';\n      if (level?.includes('轻度')) return 'info';\n      return 'success';\n    };\n    const getRecommendationIcon = type => {\n      const icons = {\n        'signal': Setting,\n        'infrastructure': Warning,\n        'management': CircleCheck,\n        'technology': InfoFilled\n      };\n      return icons[type] || InfoFilled;\n    };\n    const getPriorityType = priority => {\n      const types = {\n        'high': 'danger',\n        'medium': 'warning',\n        'low': 'info'\n      };\n      return types[priority] || 'info';\n    };\n    const getPriorityText = priority => {\n      const texts = {\n        'high': '高优先级',\n        'medium': '中优先级',\n        'low': '低优先级'\n      };\n      return texts[priority] || '未知';\n    };\n    const getSpeedPercentage = () => {\n      const speed = props.reportData.technicalMetrics.processingSpeed || 0;\n      return Math.min(100, speed / 30 * 100); // 假设30 FPS为满分\n    };\n    const exportReport = async () => {\n      exporting.value = true;\n      try {\n        emit('export-report', props.taskId);\n        ElMessage.success('报告导出成功');\n      } catch (error) {\n        ElMessage.error('报告导出失败: ' + error.message);\n      } finally {\n        exporting.value = false;\n      }\n    };\n    const refreshData = async () => {\n      refreshing.value = true;\n      try {\n        emit('refresh-data', props.taskId);\n        ElMessage.success('数据刷新成功');\n      } catch (error) {\n        ElMessage.error('数据刷新失败: ' + error.message);\n      } finally {\n        refreshing.value = false;\n      }\n    };\n\n    // 初始化图表\n    const initializeCharts = () => {\n      nextTick(() => {\n        // 这里可以集成 ECharts 或其他图表库\n        console.log('初始化图表...');\n      });\n    };\n    onMounted(() => {\n      initializeCharts();\n    });\n    return {\n      // 响应式数据\n      exporting,\n      refreshing,\n      flowChart,\n      heatmapChart,\n      // 方法\n      formatDate,\n      formatDuration,\n      getDirectionIcon,\n      getDirectionName,\n      getDirectionStatusType,\n      getCongestionClass,\n      getCongestionIndexClass,\n      getCongestionAlertType,\n      getRecommendationIcon,\n      getPriorityType,\n      getPriorityText,\n      getSpeedPercentage,\n      exportReport,\n      refreshData\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "nextTick", "ElMessage", "DataAnalysis", "Download", "Refresh", "Top", "Bottom", "ArrowLeft", "Back", "Right", "Setting", "Warning", "CircleCheck", "InfoFilled", "name", "components", "props", "taskId", "type", "String", "required", "reportData", "Object", "default", "generatedAt", "Date", "analysisType", "summary", "totalVehicles", "vehicleIncrease", "processingDuration", "efficiency", "peakDirection", "peakPercentage", "congestionLevel", "congestionTrend", "directions", "intelligentAnalysis", "recommendations", "technicalMetrics", "emits", "setup", "emit", "exporting", "refreshing", "flowChart", "heatma<PERSON><PERSON><PERSON>", "safeReportData", "data", "flowBalance", "peakHours", "flowTrend", "congestionPrediction", "congestionDescription", "signalOptimization", "recommendedCycle", "greenTimeAllocation", "east", "south", "west", "north", "expectedImprovement", "Array", "isArray", "accuracy", "processingSpeed", "stability", "dataIntegrity", "responseTime", "memoryUsage", "cpuUsage", "formatDate", "date", "toLocaleString", "formatDuration", "seconds", "minutes", "Math", "floor", "remainingSeconds", "getDirectionIcon", "direction", "icons", "getDirectionName", "names", "getDirectionStatusType", "status", "types", "getCongestionClass", "level", "includes", "getCongestionIndexClass", "index", "getCongestionAlertType", "getRecommendationIcon", "getPriorityType", "priority", "getPriorityText", "texts", "getSpeedPercentage", "speed", "min", "exportReport", "value", "success", "error", "message", "refreshData", "initializeCharts", "console", "log"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\analysis\\IntelligentTrafficReport.vue"], "sourcesContent": ["<template>\n  <div class=\"intelligent-traffic-report\">\n    <!-- 报告头部 -->\n    <div class=\"report-header\">\n      <div class=\"header-content\">\n        <h1 class=\"report-title\">\n          <el-icon><DataAnalysis /></el-icon>\n          四方向智能交通分析报告\n        </h1>\n        <div class=\"report-meta\">\n          <el-tag type=\"info\">任务ID: {{ taskId }}</el-tag>\n          <el-tag type=\"success\">{{ formatDate(safeReportData.generatedAt) }}</el-tag>\n          <el-tag>{{ safeReportData.analysisType }}</el-tag>\n        </div>\n      </div>\n      <div class=\"header-actions\">\n        <el-button type=\"primary\" @click=\"exportReport\" :loading=\"exporting\">\n          <el-icon><Download /></el-icon>\n          导出报告\n        </el-button>\n        <el-button @click=\"refreshData\" :loading=\"refreshing\">\n          <el-icon><Refresh /></el-icon>\n          刷新数据\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 执行摘要 -->\n    <div class=\"executive-summary\">\n      <h2>执行摘要</h2>\n      <div class=\"summary-grid\">\n        <div class=\"summary-card highlight\">\n          <div class=\"card-icon\">🚗</div>\n          <div class=\"card-content\">\n            <div class=\"card-value\">{{ safeReportData.summary.totalVehicles }}</div>\n            <div class=\"card-label\">总检测车辆</div>\n            <div class=\"card-change positive\">+{{ safeReportData.summary.vehicleIncrease }}%</div>\n          </div>\n        </div>\n        \n        <div class=\"summary-card\">\n          <div class=\"card-icon\">⏱️</div>\n          <div class=\"card-content\">\n            <div class=\"card-value\">{{ formatDuration(safeReportData.summary.processingDuration) }}</div>\n            <div class=\"card-label\">分析时长</div>\n            <div class=\"card-change\">{{ safeReportData.summary.efficiency }}% 效率</div>\n          </div>\n        </div>\n        \n        <div class=\"summary-card\">\n          <div class=\"card-icon\">📍</div>\n          <div class=\"card-content\">\n            <div class=\"card-value\">{{ safeReportData.summary.peakDirection }}</div>\n            <div class=\"card-label\">最繁忙方向</div>\n            <div class=\"card-change\">{{ safeReportData.summary.peakPercentage }}% 占比</div>\n          </div>\n        </div>\n        \n        <div class=\"summary-card\">\n          <div class=\"card-icon\">🚨</div>\n          <div class=\"card-content\">\n            <div class=\"card-value\">{{ safeReportData.summary.congestionLevel }}</div>\n            <div class=\"card-label\">拥堵等级</div>\n            <div class=\"card-change\" :class=\"getCongestionClass()\">\n              {{ safeReportData.summary.congestionTrend }}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 方向详细分析 -->\n    <div class=\"direction-analysis\">\n      <h2>方向详细分析</h2>\n      <div class=\"direction-grid\">\n        <div\n          v-for=\"(data, direction) in safeReportData.directions\"\n          :key=\"direction\"\n          class=\"direction-card\"\n        >\n          <div class=\"direction-header\">\n            <el-icon>\n              <component :is=\"getDirectionIcon(direction)\" />\n            </el-icon>\n            <h3>{{ getDirectionName(direction) }}</h3>\n            <el-tag :type=\"getDirectionStatusType(data.status)\">\n              {{ data.status }}\n            </el-tag>\n          </div>\n          \n          <div class=\"direction-stats\">\n            <div class=\"stat-row\">\n              <span class=\"stat-label\">检测车辆:</span>\n              <span class=\"stat-value\">{{ data.vehicleCount || 0 }}</span>\n            </div>\n            <div class=\"stat-row\">\n              <span class=\"stat-label\">平均速度:</span>\n              <span class=\"stat-value\">{{ data.averageSpeed || 0 }} km/h</span>\n            </div>\n            <div class=\"stat-row\">\n              <span class=\"stat-label\">流量密度:</span>\n              <span class=\"stat-value\">{{ data.density || data.averageFlowDensity || 0 }} 辆/km</span>\n            </div>\n            <div class=\"stat-row\">\n              <span class=\"stat-label\">拥堵等级:</span>\n              <span class=\"stat-value\" :class=\"getCongestionIndexClass(data.congestionIndex || 0)\">\n                {{ data.crowdLevel || data.congestionLevel || '畅通' }}\n              </span>\n            </div>\n          </div>\n          \n          <div class=\"direction-chart\">\n            <div :ref=\"`chart_${direction}`\" class=\"mini-chart\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 智能分析结果 -->\n    <div class=\"intelligent-analysis\">\n      <h2>智能分析结果</h2>\n      \n      <!-- 交通流量分析 -->\n      <div class=\"analysis-section\">\n        <h3>交通流量分析</h3>\n        <div class=\"flow-analysis\">\n          <div class=\"flow-chart-container\">\n            <div ref=\"flowChart\" class=\"flow-chart\"></div>\n          </div>\n          <div class=\"flow-insights\">\n            <div class=\"insight-item\">\n              <strong>流量平衡度:</strong> {{ safeReportData.intelligentAnalysis.flowBalance }}%\n              <el-progress :percentage=\"safeReportData.intelligentAnalysis.flowBalance\" :show-text=\"false\" />\n            </div>\n            <div class=\"insight-item\">\n              <strong>峰值时段:</strong> {{ safeReportData.intelligentAnalysis.peakHours }}\n            </div>\n            <div class=\"insight-item\">\n              <strong>流量趋势:</strong> {{ safeReportData.intelligentAnalysis.flowTrend }}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 拥堵分析 -->\n      <div class=\"analysis-section\">\n        <h3>拥堵分析</h3>\n        <div class=\"congestion-analysis\">\n          <div class=\"congestion-heatmap\">\n            <div ref=\"heatmapChart\" class=\"heatmap-chart\"></div>\n          </div>\n          <div class=\"congestion-insights\">\n            <el-alert\n              :title=\"`当前拥堵等级: ${safeReportData.summary.congestionLevel}`\"\n              :type=\"getCongestionAlertType()\"\n              :description=\"safeReportData.intelligentAnalysis.congestionDescription\"\n              show-icon\n              :closable=\"false\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 优化建议 -->\n    <div class=\"optimization-recommendations\">\n      <h2>优化建议</h2>\n      <div class=\"recommendations-list\">\n        <div\n          v-for=\"(recommendation, index) in safeReportData.recommendations\"\n          :key=\"index\"\n          class=\"recommendation-item\"\n          :class=\"recommendation.priority\"\n        >\n          <div class=\"recommendation-header\">\n            <el-icon>\n              <component :is=\"getRecommendationIcon(recommendation.type)\" />\n            </el-icon>\n            <h4>{{ recommendation.title }}</h4>\n            <el-tag :type=\"getPriorityType(recommendation.priority)\" size=\"small\">\n              {{ getPriorityText(recommendation.priority) }}\n            </el-tag>\n          </div>\n          <p class=\"recommendation-description\">{{ recommendation.description }}</p>\n          <div class=\"recommendation-impact\">\n            <span class=\"impact-label\">预期效果:</span>\n            <span class=\"impact-value\">{{ recommendation.expectedImprovement }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 技术指标 -->\n    <div class=\"technical-metrics\">\n      <h2>技术指标</h2>\n      <div class=\"metrics-grid\">\n        <div class=\"metric-card\">\n          <h4>检测精度</h4>\n          <div class=\"metric-value\">{{ safeReportData.technicalMetrics.accuracy }}%</div>\n          <el-progress :percentage=\"safeReportData.technicalMetrics.accuracy\" :show-text=\"false\" />\n        </div>\n        <div class=\"metric-card\">\n          <h4>处理速度</h4>\n          <div class=\"metric-value\">{{ safeReportData.technicalMetrics.processingSpeed }} FPS</div>\n          <el-progress :percentage=\"getSpeedPercentage()\" :show-text=\"false\" />\n        </div>\n        <div class=\"metric-card\">\n          <h4>系统稳定性</h4>\n          <div class=\"metric-value\">{{ safeReportData.technicalMetrics.stability }}%</div>\n          <el-progress :percentage=\"safeReportData.technicalMetrics.stability\" :show-text=\"false\" />\n        </div>\n        <div class=\"metric-card\">\n          <h4>数据完整性</h4>\n          <div class=\"metric-value\">{{ safeReportData.technicalMetrics.dataIntegrity }}%</div>\n          <el-progress :percentage=\"safeReportData.technicalMetrics.dataIntegrity\" :show-text=\"false\" />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, nextTick } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport {\n  DataAnalysis, Download, Refresh, Top, Bottom, \n  ArrowLeft as Back, Right, Setting, Warning, \n  CircleCheck, InfoFilled\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'IntelligentTrafficReport',\n  components: {\n    DataAnalysis, Download, Refresh, Top, Bottom, \n    Back, Right, Setting, Warning, CircleCheck, InfoFilled\n  },\n  props: {\n    taskId: {\n      type: String,\n      required: true\n    },\n    reportData: {\n      type: Object,\n      default: () => ({\n        generatedAt: new Date(),\n        analysisType: '四方向智能分析',\n        summary: {\n          totalVehicles: 0,\n          vehicleIncrease: 0,\n          processingDuration: 0,\n          efficiency: 0,\n          peakDirection: '未知',\n          peakPercentage: 0,\n          congestionLevel: '畅通',\n          congestionTrend: '稳定'\n        },\n        directions: {},\n        intelligentAnalysis: {},\n        recommendations: [],\n        technicalMetrics: {}\n      })\n    }\n  },\n  emits: ['export-report', 'refresh-data'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const exporting = ref(false)\n    const refreshing = ref(false)\n\n    // 图表引用\n    const flowChart = ref(null)\n    const heatmapChart = ref(null)\n\n    // 数据验证和默认值处理\n    const safeReportData = computed(() => {\n      const data = props.reportData || {}\n      return {\n        taskId: data.taskId || 'unknown',\n        generatedAt: data.generatedAt || new Date(),\n        analysisType: data.analysisType || '四方向智能分析',\n        summary: {\n          totalVehicles: data.summary?.totalVehicles || 0,\n          vehicleIncrease: data.summary?.vehicleIncrease || 0,\n          processingDuration: data.summary?.processingDuration || 0,\n          efficiency: data.summary?.efficiency || 0,\n          peakDirection: data.summary?.peakDirection || '未知',\n          peakPercentage: data.summary?.peakPercentage || 0,\n          congestionLevel: data.summary?.congestionLevel || '畅通',\n          congestionTrend: data.summary?.congestionTrend || '稳定'\n        },\n        directions: data.directions || {},\n        intelligentAnalysis: {\n          flowBalance: data.intelligentAnalysis?.flowBalance || 75,\n          peakHours: data.intelligentAnalysis?.peakHours || '08:00-09:00, 17:00-18:00',\n          flowTrend: data.intelligentAnalysis?.flowTrend || '稳定',\n          congestionPrediction: data.intelligentAnalysis?.congestionPrediction || '低风险',\n          congestionDescription: data.intelligentAnalysis?.congestionDescription || '交通状况良好',\n          signalOptimization: data.intelligentAnalysis?.signalOptimization || {\n            recommendedCycle: 120,\n            greenTimeAllocation: { east: 30, south: 30, west: 30, north: 30 },\n            expectedImprovement: '通行效率提升15%'\n          }\n        },\n        recommendations: Array.isArray(data.recommendations) ? data.recommendations : [],\n        technicalMetrics: {\n          accuracy: data.technicalMetrics?.accuracy || 95.5,\n          processingSpeed: data.technicalMetrics?.processingSpeed || 25.0,\n          stability: data.technicalMetrics?.stability || 98.2,\n          dataIntegrity: data.technicalMetrics?.dataIntegrity || 99.1,\n          responseTime: data.technicalMetrics?.responseTime || 150,\n          memoryUsage: data.technicalMetrics?.memoryUsage || 65.3,\n          cpuUsage: data.technicalMetrics?.cpuUsage || 45.8\n        }\n      }\n    })\n    \n    // 方法\n    const formatDate = (date) => {\n      return new Date(date).toLocaleString('zh-CN')\n    }\n    \n    const formatDuration = (seconds) => {\n      const minutes = Math.floor(seconds / 60)\n      const remainingSeconds = seconds % 60\n      return `${minutes}分${remainingSeconds}秒`\n    }\n    \n    const getDirectionIcon = (direction) => {\n      const icons = {\n        east: Right,\n        south: Bottom,\n        west: Back,\n        north: Top\n      }\n      return icons[direction] || InfoFilled\n    }\n    \n    const getDirectionName = (direction) => {\n      const names = {\n        east: '东向',\n        south: '南向',\n        west: '西向',\n        north: '北向'\n      }\n      return names[direction] || direction\n    }\n    \n    const getDirectionStatusType = (status) => {\n      const types = {\n        'completed': 'success',\n        'processing': 'warning',\n        'error': 'danger',\n        'waiting': 'info'\n      }\n      return types[status] || 'info'\n    }\n    \n    const getCongestionClass = () => {\n      const level = props.reportData.summary.congestionLevel\n      if (level.includes('严重')) return 'negative'\n      if (level.includes('中度')) return 'warning'\n      if (level.includes('轻度')) return 'caution'\n      return 'positive'\n    }\n    \n    const getCongestionIndexClass = (index) => {\n      if (index >= 0.8) return 'high-congestion'\n      if (index >= 0.5) return 'medium-congestion'\n      if (index >= 0.3) return 'low-congestion'\n      return 'no-congestion'\n    }\n    \n    const getCongestionAlertType = () => {\n      const level = props.reportData.summary?.congestionLevel || props.reportData.intelligentAnalysis?.congestionLevel\n      if (level?.includes('重度')) return 'error'\n      if (level?.includes('中度')) return 'warning'\n      if (level?.includes('轻度')) return 'info'\n      return 'success'\n    }\n    \n    const getRecommendationIcon = (type) => {\n      const icons = {\n        'signal': Setting,\n        'infrastructure': Warning,\n        'management': CircleCheck,\n        'technology': InfoFilled\n      }\n      return icons[type] || InfoFilled\n    }\n    \n    const getPriorityType = (priority) => {\n      const types = {\n        'high': 'danger',\n        'medium': 'warning',\n        'low': 'info'\n      }\n      return types[priority] || 'info'\n    }\n    \n    const getPriorityText = (priority) => {\n      const texts = {\n        'high': '高优先级',\n        'medium': '中优先级',\n        'low': '低优先级'\n      }\n      return texts[priority] || '未知'\n    }\n    \n    const getSpeedPercentage = () => {\n      const speed = props.reportData.technicalMetrics.processingSpeed || 0\n      return Math.min(100, (speed / 30) * 100) // 假设30 FPS为满分\n    }\n    \n    const exportReport = async () => {\n      exporting.value = true\n      try {\n        emit('export-report', props.taskId)\n        ElMessage.success('报告导出成功')\n      } catch (error) {\n        ElMessage.error('报告导出失败: ' + error.message)\n      } finally {\n        exporting.value = false\n      }\n    }\n    \n    const refreshData = async () => {\n      refreshing.value = true\n      try {\n        emit('refresh-data', props.taskId)\n        ElMessage.success('数据刷新成功')\n      } catch (error) {\n        ElMessage.error('数据刷新失败: ' + error.message)\n      } finally {\n        refreshing.value = false\n      }\n    }\n    \n    // 初始化图表\n    const initializeCharts = () => {\n      nextTick(() => {\n        // 这里可以集成 ECharts 或其他图表库\n        console.log('初始化图表...')\n      })\n    }\n    \n    onMounted(() => {\n      initializeCharts()\n    })\n    \n    return {\n      // 响应式数据\n      exporting,\n      refreshing,\n      flowChart,\n      heatmapChart,\n      \n      // 方法\n      formatDate,\n      formatDuration,\n      getDirectionIcon,\n      getDirectionName,\n      getDirectionStatusType,\n      getCongestionClass,\n      getCongestionIndexClass,\n      getCongestionAlertType,\n      getRecommendationIcon,\n      getPriorityType,\n      getPriorityText,\n      getSpeedPercentage,\n      exportReport,\n      refreshData\n    }\n  }\n}\n</script>\n\n<style scoped>\n.intelligent-traffic-report {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 24px;\n  background: #ffffff;\n}\n\n/* 报告头部 */\n.report-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 32px;\n  padding-bottom: 24px;\n  border-bottom: 2px solid #e5e7eb;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.report-title {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 28px;\n  font-weight: 700;\n  color: #1f2937;\n  margin: 0 0 12px 0;\n}\n\n.report-meta {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.header-actions {\n  display: flex;\n  gap: 12px;\n}\n\n/* 执行摘要 */\n.executive-summary {\n  margin-bottom: 32px;\n}\n\n.executive-summary h2 {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16px;\n}\n\n.summary-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 16px;\n}\n\n.summary-card {\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 12px;\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  transition: all 0.3s ease;\n}\n\n.summary-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.summary-card.highlight {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n}\n\n.card-icon {\n  font-size: 32px;\n  width: 60px;\n  height: 60px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n}\n\n.summary-card:not(.highlight) .card-icon {\n  background: #f3f4f6;\n}\n\n.card-content {\n  flex: 1;\n}\n\n.card-value {\n  font-size: 24px;\n  font-weight: 700;\n  line-height: 1.2;\n  margin-bottom: 4px;\n}\n\n.card-label {\n  font-size: 14px;\n  opacity: 0.8;\n  margin-bottom: 4px;\n}\n\n.card-change {\n  font-size: 12px;\n  font-weight: 500;\n  padding: 2px 8px;\n  border-radius: 4px;\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.summary-card:not(.highlight) .card-change {\n  background: #f3f4f6;\n  color: #6b7280;\n}\n\n.card-change.positive {\n  background: #dcfce7;\n  color: #16a34a;\n}\n\n.card-change.negative {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.card-change.warning {\n  background: #fef3c7;\n  color: #d97706;\n}\n\n/* 方向分析 */\n.direction-analysis {\n  margin-bottom: 32px;\n}\n\n.direction-analysis h2 {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16px;\n}\n\n.direction-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.direction-card {\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 12px;\n  padding: 20px;\n  transition: all 0.3s ease;\n}\n\n.direction-card:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.direction-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 16px;\n  padding-bottom: 12px;\n  border-bottom: 1px solid #f3f4f6;\n}\n\n.direction-header h3 {\n  flex: 1;\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.direction-stats {\n  margin-bottom: 16px;\n}\n\n.stat-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #f9fafb;\n}\n\n.stat-row:last-child {\n  border-bottom: none;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #6b7280;\n}\n\n.stat-value {\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.stat-value.high-congestion {\n  color: #dc2626;\n}\n\n.stat-value.medium-congestion {\n  color: #d97706;\n}\n\n.stat-value.low-congestion {\n  color: #059669;\n}\n\n.stat-value.no-congestion {\n  color: #10b981;\n}\n\n.direction-chart {\n  height: 120px;\n  background: #f9fafb;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #6b7280;\n}\n\n.mini-chart {\n  width: 100%;\n  height: 100%;\n}\n\n/* 智能分析 */\n.intelligent-analysis {\n  margin-bottom: 32px;\n}\n\n.intelligent-analysis h2 {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16px;\n}\n\n.analysis-section {\n  margin-bottom: 24px;\n  background: #f9fafb;\n  border-radius: 12px;\n  padding: 20px;\n}\n\n.analysis-section h3 {\n  font-size: 16px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16px;\n}\n\n.flow-analysis {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 20px;\n  align-items: start;\n}\n\n.flow-chart-container {\n  background: white;\n  border-radius: 8px;\n  padding: 16px;\n  height: 300px;\n}\n\n.flow-chart {\n  width: 100%;\n  height: 100%;\n  background: #f3f4f6;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #6b7280;\n}\n\n.flow-insights {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.insight-item {\n  background: white;\n  padding: 16px;\n  border-radius: 8px;\n  border: 1px solid #e5e7eb;\n}\n\n.insight-item strong {\n  display: block;\n  margin-bottom: 8px;\n  color: #1f2937;\n}\n\n.congestion-analysis {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  align-items: start;\n}\n\n.congestion-heatmap {\n  background: white;\n  border-radius: 8px;\n  padding: 16px;\n  height: 250px;\n}\n\n.heatmap-chart {\n  width: 100%;\n  height: 100%;\n  background: #f3f4f6;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #6b7280;\n}\n\n.congestion-insights {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n/* 优化建议 */\n.optimization-recommendations {\n  margin-bottom: 32px;\n}\n\n.optimization-recommendations h2 {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16px;\n}\n\n.recommendations-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.recommendation-item {\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 12px;\n  padding: 20px;\n  transition: all 0.3s ease;\n}\n\n.recommendation-item:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.recommendation-item.high {\n  border-left: 4px solid #dc2626;\n}\n\n.recommendation-item.medium {\n  border-left: 4px solid #d97706;\n}\n\n.recommendation-item.low {\n  border-left: 4px solid #059669;\n}\n\n.recommendation-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 12px;\n}\n\n.recommendation-header h4 {\n  flex: 1;\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.recommendation-description {\n  color: #6b7280;\n  line-height: 1.6;\n  margin-bottom: 12px;\n}\n\n.recommendation-impact {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.impact-label {\n  font-size: 14px;\n  color: #6b7280;\n}\n\n.impact-value {\n  font-weight: 600;\n  color: #059669;\n}\n\n/* 技术指标 */\n.technical-metrics h2 {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 16px;\n}\n\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n}\n\n.metric-card {\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n  border-radius: 12px;\n  padding: 20px;\n  text-align: center;\n  transition: all 0.3s ease;\n}\n\n.metric-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.metric-card h4 {\n  margin: 0 0 12px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #6b7280;\n}\n\n.metric-value {\n  font-size: 24px;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 12px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .intelligent-traffic-report {\n    padding: 16px;\n  }\n\n  .report-header {\n    flex-direction: column;\n    gap: 16px;\n  }\n\n  .summary-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .direction-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .flow-analysis {\n    grid-template-columns: 1fr;\n  }\n\n  .congestion-analysis {\n    grid-template-columns: 1fr;\n  }\n\n  .metrics-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n</style>\n"], "mappings": "AA8NA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAO,QAAS,KAAI;AACjE,SAASC,SAAQ,QAAS,cAAa;AACvC,SACEC,YAAY,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAC5CC,SAAQ,IAAKC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAC1CC,WAAW,EAAEC,UAAS,QACjB,yBAAwB;AAE/B,eAAe;EACbC,IAAI,EAAE,0BAA0B;EAChCC,UAAU,EAAE;IACVb,YAAY;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,GAAG;IAAEC,MAAM;IAC5CE,IAAI;IAAEC,KAAK;IAAEC,OAAO;IAAEC,OAAO;IAAEC,WAAW;IAAEC;EAC9C,CAAC;EACDG,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,UAAU,EAAE;MACVH,IAAI,EAAEI,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO;QACdC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC;QACvBC,YAAY,EAAE,SAAS;QACvBC,OAAO,EAAE;UACPC,aAAa,EAAE,CAAC;UAChBC,eAAe,EAAE,CAAC;UAClBC,kBAAkB,EAAE,CAAC;UACrBC,UAAU,EAAE,CAAC;UACbC,aAAa,EAAE,IAAI;UACnBC,cAAc,EAAE,CAAC;UACjBC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE;QACnB,CAAC;QACDC,UAAU,EAAE,CAAC,CAAC;QACdC,mBAAmB,EAAE,CAAC,CAAC;QACvBC,eAAe,EAAE,EAAE;QACnBC,gBAAgB,EAAE,CAAC;MACrB,CAAC;IACH;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,eAAe,EAAE,cAAc,CAAC;EACxCC,KAAKA,CAACzB,KAAK,EAAE;IAAE0B;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,SAAQ,GAAI/C,GAAG,CAAC,KAAK;IAC3B,MAAMgD,UAAS,GAAIhD,GAAG,CAAC,KAAK;;IAE5B;IACA,MAAMiD,SAAQ,GAAIjD,GAAG,CAAC,IAAI;IAC1B,MAAMkD,YAAW,GAAIlD,GAAG,CAAC,IAAI;;IAE7B;IACA,MAAMmD,cAAa,GAAIjD,QAAQ,CAAC,MAAM;MACpC,MAAMkD,IAAG,GAAIhC,KAAK,CAACK,UAAS,IAAK,CAAC;MAClC,OAAO;QACLJ,MAAM,EAAE+B,IAAI,CAAC/B,MAAK,IAAK,SAAS;QAChCO,WAAW,EAAEwB,IAAI,CAACxB,WAAU,IAAK,IAAIC,IAAI,CAAC,CAAC;QAC3CC,YAAY,EAAEsB,IAAI,CAACtB,YAAW,IAAK,SAAS;QAC5CC,OAAO,EAAE;UACPC,aAAa,EAAEoB,IAAI,CAACrB,OAAO,EAAEC,aAAY,IAAK,CAAC;UAC/CC,eAAe,EAAEmB,IAAI,CAACrB,OAAO,EAAEE,eAAc,IAAK,CAAC;UACnDC,kBAAkB,EAAEkB,IAAI,CAACrB,OAAO,EAAEG,kBAAiB,IAAK,CAAC;UACzDC,UAAU,EAAEiB,IAAI,CAACrB,OAAO,EAAEI,UAAS,IAAK,CAAC;UACzCC,aAAa,EAAEgB,IAAI,CAACrB,OAAO,EAAEK,aAAY,IAAK,IAAI;UAClDC,cAAc,EAAEe,IAAI,CAACrB,OAAO,EAAEM,cAAa,IAAK,CAAC;UACjDC,eAAe,EAAEc,IAAI,CAACrB,OAAO,EAAEO,eAAc,IAAK,IAAI;UACtDC,eAAe,EAAEa,IAAI,CAACrB,OAAO,EAAEQ,eAAc,IAAK;QACpD,CAAC;QACDC,UAAU,EAAEY,IAAI,CAACZ,UAAS,IAAK,CAAC,CAAC;QACjCC,mBAAmB,EAAE;UACnBY,WAAW,EAAED,IAAI,CAACX,mBAAmB,EAAEY,WAAU,IAAK,EAAE;UACxDC,SAAS,EAAEF,IAAI,CAACX,mBAAmB,EAAEa,SAAQ,IAAK,0BAA0B;UAC5EC,SAAS,EAAEH,IAAI,CAACX,mBAAmB,EAAEc,SAAQ,IAAK,IAAI;UACtDC,oBAAoB,EAAEJ,IAAI,CAACX,mBAAmB,EAAEe,oBAAmB,IAAK,KAAK;UAC7EC,qBAAqB,EAAEL,IAAI,CAACX,mBAAmB,EAAEgB,qBAAoB,IAAK,QAAQ;UAClFC,kBAAkB,EAAEN,IAAI,CAACX,mBAAmB,EAAEiB,kBAAiB,IAAK;YAClEC,gBAAgB,EAAE,GAAG;YACrBC,mBAAmB,EAAE;cAAEC,IAAI,EAAE,EAAE;cAAEC,KAAK,EAAE,EAAE;cAAEC,IAAI,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAG,CAAC;YACjEC,mBAAmB,EAAE;UACvB;QACF,CAAC;QACDvB,eAAe,EAAEwB,KAAK,CAACC,OAAO,CAACf,IAAI,CAACV,eAAe,IAAIU,IAAI,CAACV,eAAc,GAAI,EAAE;QAChFC,gBAAgB,EAAE;UAChByB,QAAQ,EAAEhB,IAAI,CAACT,gBAAgB,EAAEyB,QAAO,IAAK,IAAI;UACjDC,eAAe,EAAEjB,IAAI,CAACT,gBAAgB,EAAE0B,eAAc,IAAK,IAAI;UAC/DC,SAAS,EAAElB,IAAI,CAACT,gBAAgB,EAAE2B,SAAQ,IAAK,IAAI;UACnDC,aAAa,EAAEnB,IAAI,CAACT,gBAAgB,EAAE4B,aAAY,IAAK,IAAI;UAC3DC,YAAY,EAAEpB,IAAI,CAACT,gBAAgB,EAAE6B,YAAW,IAAK,GAAG;UACxDC,WAAW,EAAErB,IAAI,CAACT,gBAAgB,EAAE8B,WAAU,IAAK,IAAI;UACvDC,QAAQ,EAAEtB,IAAI,CAACT,gBAAgB,EAAE+B,QAAO,IAAK;QAC/C;MACF;IACF,CAAC;;IAED;IACA,MAAMC,UAAS,GAAKC,IAAI,IAAK;MAC3B,OAAO,IAAI/C,IAAI,CAAC+C,IAAI,CAAC,CAACC,cAAc,CAAC,OAAO;IAC9C;IAEA,MAAMC,cAAa,GAAKC,OAAO,IAAK;MAClC,MAAMC,OAAM,GAAIC,IAAI,CAACC,KAAK,CAACH,OAAM,GAAI,EAAE;MACvC,MAAMI,gBAAe,GAAIJ,OAAM,GAAI,EAAC;MACpC,OAAO,GAAGC,OAAO,IAAIG,gBAAgB,GAAE;IACzC;IAEA,MAAMC,gBAAe,GAAKC,SAAS,IAAK;MACtC,MAAMC,KAAI,GAAI;QACZzB,IAAI,EAAEhD,KAAK;QACXiD,KAAK,EAAEpD,MAAM;QACbqD,IAAI,EAAEnD,IAAI;QACVoD,KAAK,EAAEvD;MACT;MACA,OAAO6E,KAAK,CAACD,SAAS,KAAKpE,UAAS;IACtC;IAEA,MAAMsE,gBAAe,GAAKF,SAAS,IAAK;MACtC,MAAMG,KAAI,GAAI;QACZ3B,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE;MACT;MACA,OAAOwB,KAAK,CAACH,SAAS,KAAKA,SAAQ;IACrC;IAEA,MAAMI,sBAAqB,GAAKC,MAAM,IAAK;MACzC,MAAMC,KAAI,GAAI;QACZ,WAAW,EAAE,SAAS;QACtB,YAAY,EAAE,SAAS;QACvB,OAAO,EAAE,QAAQ;QACjB,SAAS,EAAE;MACb;MACA,OAAOA,KAAK,CAACD,MAAM,KAAK,MAAK;IAC/B;IAEA,MAAME,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,KAAI,GAAIzE,KAAK,CAACK,UAAU,CAACM,OAAO,CAACO,eAAc;MACrD,IAAIuD,KAAK,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,UAAS;MAC1C,IAAID,KAAK,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,SAAQ;MACzC,IAAID,KAAK,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,SAAQ;MACzC,OAAO,UAAS;IAClB;IAEA,MAAMC,uBAAsB,GAAKC,KAAK,IAAK;MACzC,IAAIA,KAAI,IAAK,GAAG,EAAE,OAAO,iBAAgB;MACzC,IAAIA,KAAI,IAAK,GAAG,EAAE,OAAO,mBAAkB;MAC3C,IAAIA,KAAI,IAAK,GAAG,EAAE,OAAO,gBAAe;MACxC,OAAO,eAAc;IACvB;IAEA,MAAMC,sBAAqB,GAAIA,CAAA,KAAM;MACnC,MAAMJ,KAAI,GAAIzE,KAAK,CAACK,UAAU,CAACM,OAAO,EAAEO,eAAc,IAAKlB,KAAK,CAACK,UAAU,CAACgB,mBAAmB,EAAEH,eAAc;MAC/G,IAAIuD,KAAK,EAAEC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,OAAM;MACxC,IAAID,KAAK,EAAEC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,SAAQ;MAC1C,IAAID,KAAK,EAAEC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,MAAK;MACvC,OAAO,SAAQ;IACjB;IAEA,MAAMI,qBAAoB,GAAK5E,IAAI,IAAK;MACtC,MAAMgE,KAAI,GAAI;QACZ,QAAQ,EAAExE,OAAO;QACjB,gBAAgB,EAAEC,OAAO;QACzB,YAAY,EAAEC,WAAW;QACzB,YAAY,EAAEC;MAChB;MACA,OAAOqE,KAAK,CAAChE,IAAI,KAAKL,UAAS;IACjC;IAEA,MAAMkF,eAAc,GAAKC,QAAQ,IAAK;MACpC,MAAMT,KAAI,GAAI;QACZ,MAAM,EAAE,QAAQ;QAChB,QAAQ,EAAE,SAAS;QACnB,KAAK,EAAE;MACT;MACA,OAAOA,KAAK,CAACS,QAAQ,KAAK,MAAK;IACjC;IAEA,MAAMC,eAAc,GAAKD,QAAQ,IAAK;MACpC,MAAME,KAAI,GAAI;QACZ,MAAM,EAAE,MAAM;QACd,QAAQ,EAAE,MAAM;QAChB,KAAK,EAAE;MACT;MACA,OAAOA,KAAK,CAACF,QAAQ,KAAK,IAAG;IAC/B;IAEA,MAAMG,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,KAAI,GAAIpF,KAAK,CAACK,UAAU,CAACkB,gBAAgB,CAAC0B,eAAc,IAAK;MACnE,OAAOY,IAAI,CAACwB,GAAG,CAAC,GAAG,EAAGD,KAAI,GAAI,EAAE,GAAI,GAAG,GAAE;IAC3C;IAEA,MAAME,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B3D,SAAS,CAAC4D,KAAI,GAAI,IAAG;MACrB,IAAI;QACF7D,IAAI,CAAC,eAAe,EAAE1B,KAAK,CAACC,MAAM;QAClChB,SAAS,CAACuG,OAAO,CAAC,QAAQ;MAC5B,EAAE,OAAOC,KAAK,EAAE;QACdxG,SAAS,CAACwG,KAAK,CAAC,UAAS,GAAIA,KAAK,CAACC,OAAO;MAC5C,UAAU;QACR/D,SAAS,CAAC4D,KAAI,GAAI,KAAI;MACxB;IACF;IAEA,MAAMI,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B/D,UAAU,CAAC2D,KAAI,GAAI,IAAG;MACtB,IAAI;QACF7D,IAAI,CAAC,cAAc,EAAE1B,KAAK,CAACC,MAAM;QACjChB,SAAS,CAACuG,OAAO,CAAC,QAAQ;MAC5B,EAAE,OAAOC,KAAK,EAAE;QACdxG,SAAS,CAACwG,KAAK,CAAC,UAAS,GAAIA,KAAK,CAACC,OAAO;MAC5C,UAAU;QACR9D,UAAU,CAAC2D,KAAI,GAAI,KAAI;MACzB;IACF;;IAEA;IACA,MAAMK,gBAAe,GAAIA,CAAA,KAAM;MAC7B5G,QAAQ,CAAC,MAAM;QACb;QACA6G,OAAO,CAACC,GAAG,CAAC,UAAU;MACxB,CAAC;IACH;IAEA/G,SAAS,CAAC,MAAM;MACd6G,gBAAgB,CAAC;IACnB,CAAC;IAED,OAAO;MACL;MACAjE,SAAS;MACTC,UAAU;MACVC,SAAS;MACTC,YAAY;MAEZ;MACAyB,UAAU;MACVG,cAAc;MACdM,gBAAgB;MAChBG,gBAAgB;MAChBE,sBAAsB;MACtBG,kBAAkB;MAClBG,uBAAuB;MACvBE,sBAAsB;MACtBC,qBAAqB;MACrBC,eAAe;MACfE,eAAe;MACfE,kBAAkB;MAClBG,YAAY;MACZK;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}